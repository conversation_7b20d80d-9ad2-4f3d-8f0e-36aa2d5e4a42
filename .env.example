# A2A多智能体协作系统环境配置示例
# 复制此文件为 .env 并填入实际值

# ==================== 千问Plus API ====================
# 阿里千问Plus API密钥（必需）
QWEN_API_KEY=your_qwen_api_key_here

# ==================== 服务器配置 ====================
# 服务器主机地址
HOST=0.0.0.0

# 服务器端口
PORT=8000

# 调试模式
DEBUG=false

# ==================== 安全配置 ====================
# 应用密钥（用于加密）
SECRET_KEY=your_secret_key_here

# ==================== 数据库配置 ====================
# 数据库文件路径（SQLite）
DATABASE_URL=sqlite:///a2a_system.db

# ==================== 日志配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/a2a_system.log

# ==================== 工作流配置 ====================
# 最大工作流执行时间（秒）
MAX_WORKFLOW_EXECUTION_TIME=3600

# 最大并行任务数
MAX_PARALLEL_TASKS=10

# 默认重试次数
DEFAULT_RETRY_COUNT=3

# ==================== 功能开关 ====================
# 启用Web UI
ENABLE_WEB_UI=true

# 启用API文档
ENABLE_API_DOCS=true

# 启用指标监控
ENABLE_METRICS=true
