# A2A多智能体协作系统环境配置文件
# 复制此文件为 .env 并填入实际配置值

# ==================== 基础配置 ====================
A2A_APP_NAME=A2A多智能体协作系统
A2A_APP_VERSION=1.0.0
A2A_DEBUG=false
A2A_ENVIRONMENT=production

# ==================== 服务器配置 ====================
A2A_HOST=0.0.0.0
A2A_PORT=8000
A2A_WORKERS=1

# ==================== 数据库配置 ====================
A2A_DATABASE_URL=mysql+pymysql://root:password@localhost:3306/a2a
A2A_DATABASE_ECHO=false
A2A_DATABASE_POOL_SIZE=10
A2A_DATABASE_MAX_OVERFLOW=20

# ==================== Redis配置 ====================
A2A_REDIS_URL=redis://localhost:6379/0
A2A_REDIS_PASSWORD=

# ==================== 阿里千问配置（必填） ====================
# 从阿里云DashScope获取API密钥: https://dashscope.aliyun.com/
A2A_QWEN_API_KEY=your_qwen_api_key_here
A2A_QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
A2A_QWEN_MODEL=qwen-plus
A2A_QWEN_TEMPERATURE=0.7
A2A_QWEN_MAX_TOKENS=2048

# ==================== 其他LLM配置（可选） ====================
# OpenAI配置
A2A_OPENAI_API_KEY=
A2A_OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic配置
A2A_ANTHROPIC_API_KEY=

# Google配置
A2A_GOOGLE_API_KEY=

# 智谱AI配置
A2A_ZHIPU_API_KEY=

# ==================== A2A协议配置 ====================
A2A_A2A_SERVER_NAME=A2A多智能体系统
A2A_A2A_SERVER_DESCRIPTION=基于Google A2A协议的多智能体协作系统
A2A_A2A_SERVER_VERSION=1.0.0
A2A_A2A_MAX_CONCURRENT_TASKS=10

# ==================== 安全配置 ====================
A2A_SECRET_KEY=your-secret-key-change-in-production-please
A2A_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ==================== 日志配置 ====================
A2A_LOG_LEVEL=INFO
A2A_LOG_FILE=logs/a2a.log
A2A_LOG_ROTATION=1 day
A2A_LOG_RETENTION=30 days

# ==================== 工作流配置 ====================
A2A_MAX_WORKFLOW_ITERATIONS=3
A2A_WORKFLOW_TIMEOUT=300
A2A_TASK_TIMEOUT=120

# ==================== MCP服务配置 ====================
A2A_MCP_ENABLED=true
A2A_MCP_TIMEOUT=30

# ==================== 监控配置 ====================
A2A_METRICS_ENABLED=true
A2A_METRICS_PORT=9090
