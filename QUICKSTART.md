# A2A多智能体系统 - 快速启动指南

## 🚀 5分钟快速启动

### 第一步：环境准备

确保你的系统满足以下要求：
- Python 3.12+
- 阿里千问API密钥

### 第二步：获取阿里千问API密钥

1. 访问 [阿里云DashScope](https://dashscope.aliyun.com/)
2. 注册/登录阿里云账号
3. 开通DashScope服务
4. 创建API密钥
5. 复制API密钥备用

### 第三步：安装和配置

```bash
# 1. 克隆项目（如果还没有）
git clone <repository-url>
cd A2A

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境
cp .env.example .env

# 4. 编辑 .env 文件，填入你的千问API密钥
# 将 A2A_QWEN_API_KEY=your_qwen_api_key_here 
# 改为 A2A_QWEN_API_KEY=你的实际API密钥
```

### 第四步：启动系统

```bash
python run.py
```

看到以下输出表示启动成功：
```
🚀 启动A2A多智能体协作系统
✅ Python版本检查通过
✅ 环境配置检查通过
✅ 依赖包检查通过
✅ 日志目录已创建
🎯 所有检查通过，启动系统...
INFO:     Started server process
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### 第五步：验证系统

打开新的终端窗口，运行测试：

```bash
python test_a2a_system.py
```

如果看到 "🎉 所有测试通过！系统运行正常。" 表示系统工作正常。

## 🌐 访问系统

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **智能体列表**: http://localhost:8000/agents

## 🎯 第一个任务

### 使用API创建任务

```bash
# 意图识别任务
curl -X POST "http://localhost:8000/agents/intent_recognizer/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "my_first_task",
    "skill_name": "recognize_intent",
    "parameters": {
      "user_input": "我想开发一个电商网站",
      "intent_types": ["generate_code", "market_research", "other"]
    }
  }'

# 查询任务状态
curl "http://localhost:8000/tasks/my_first_task"
```

### 使用Python客户端

```python
import httpx
import asyncio

async def test_intent_recognition():
    async with httpx.AsyncClient() as client:
        # 创建任务
        task_request = {
            "task_id": "python_test_task",
            "skill_name": "recognize_intent",
            "parameters": {
                "user_input": "我想分析一下智能手机市场",
                "intent_types": ["market_research", "product_analysis", "other"]
            }
        }
        
        response = await client.post(
            "http://localhost:8000/agents/intent_recognizer/tasks",
            json=task_request
        )
        
        print(f"任务创建: {response.status_code}")
        
        # 等待并查询结果
        await asyncio.sleep(3)
        
        result = await client.get("http://localhost:8000/tasks/python_test_task")
        task_data = result.json()
        
        print(f"任务状态: {task_data['status']}")
        if task_data['status'] == 'completed':
            print(f"识别结果: {task_data['result']}")

# 运行测试
asyncio.run(test_intent_recognition())
```

## 🤖 智能体使用示例

### 1. 意图识别智能体

```python
# 识别用户意图
task_request = {
    "task_id": "intent_001",
    "skill_name": "recognize_intent",
    "parameters": {
        "user_input": "帮我写一个Python爬虫程序",
        "intent_types": ["generate_code", "data_analysis", "other"]
    }
}
```

### 2. 任务分解智能体

```python
# 分解复杂任务
task_request = {
    "task_id": "decompose_001",
    "skill_name": "decompose_task",
    "parameters": {
        "intent": "generate_code",
        "user_request": "开发一个在线聊天应用",
        "max_tasks": 8
    }
}
```

### 3. 代码生成智能体

```python
# 生成代码
task_request = {
    "task_id": "code_001",
    "skill_name": "generate_code",
    "parameters": {
        "task_description": "创建一个简单的Flask Web应用",
        "language": "Python",
        "requirements": ["用户注册", "登录功能", "数据库集成"]
    }
}
```

### 4. 市场调研智能体

```python
# 市场调研
task_request = {
    "task_id": "research_001",
    "skill_name": "research_market",
    "parameters": {
        "research_topic": "人工智能聊天机器人市场",
        "research_scope": "中国市场",
        "target_market": "企业客户"
    }
}
```

## 🔧 常见问题

### Q: 启动时提示"请在.env文件中配置阿里千问API密钥"

A: 请确保：
1. 已复制 `.env.example` 为 `.env`
2. 在 `.env` 文件中正确填入千问API密钥
3. API密钥格式正确，没有多余的空格或引号

### Q: 任务一直处于"running"状态

A: 可能的原因：
1. 千问API密钥无效或过期
2. 网络连接问题
3. API配额不足
4. 任务参数格式错误

检查日志文件 `logs/a2a.log` 获取详细错误信息。

### Q: 如何添加自定义智能体？

A: 参考 `src/agents/qwen_agent.py` 创建新的智能体类，然后在 `src/main.py` 中注册。

### Q: 如何修改智能体的提示词？

A: 在创建智能体配置时修改 `system_prompt` 参数，或者重写智能体的 `_build_task_prompt` 方法。

## 📚 下一步

- 阅读完整的 [README.md](README.md)
- 查看 [设计文档](docs/design_document.md)
- 探索 [API文档](http://localhost:8000/docs)
- 尝试创建自定义智能体
- 集成到你的应用中

## 🆘 获取帮助

如果遇到问题：
1. 检查日志文件 `logs/a2a.log`
2. 运行测试脚本 `python test_a2a_system.py`
3. 查看 GitHub Issues
4. 创建新的 Issue 描述问题

---

🎉 恭喜！你已经成功启动了A2A多智能体协作系统！
