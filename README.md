# A2A多智能体协作系统

基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统，实现智能体间的高效协作和工作流编排。

## 🌟 特性

- **Google A2A协议**: 严格遵循Google Agent2Agent协议规范
- **千问Plus集成**: 统一使用阿里千问Plus作为LLM后端
- **多智能体协作**: 支持多个专门智能体协同工作
- **工作流编排**: 灵活的工作流定义和执行引擎
- **Web界面**: 现代化的Vue.js前端界面
- **RESTful API**: 完整的API接口支持
- **实时监控**: 会话、任务、日志的实时监控

## 🏗️ 系统架构

```
A2A多智能体协作系统
├── 前端界面 (Vue.js + Element Plus)
├── API网关 (FastAPI)
├── 工作流引擎
├── A2A协议层
├── 智能体管理
│   ├── 意图识别智能体
│   ├── 任务分解智能体
│   ├── 代码生成智能体
│   ├── 市场调研智能体
│   └── 结果验证智能体
├── 数据持久化 (SQLite)
└── 千问Plus API集成
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+ (用于前端开发)
- 阿里千问Plus API密钥

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd A2A
```

2. **安装Python依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的千问API密钥
```

4. **启动系统**
```bash
python run.py
```

5. **访问系统**
- Web界面: http://localhost:8000
- API文档: http://localhost:8000/docs

### 前端开发

如果需要开发前端：

```bash
cd frontend
npm install
npm run dev
```

## 📖 使用指南

### 智能体

系统包含以下专门智能体：

- **意图识别智能体**: 识别用户意图和提取实体信息
- **任务分解智能体**: 将复杂任务分解为可执行的子任务
- **代码生成智能体**: 生成高质量的代码
- **市场调研智能体**: 进行市场调研和商业分析
- **结果验证智能体**: 验证任务执行结果的质量

### 工作流

系统提供以下预定义工作流：

1. **代码生成工作流**: 从需求到代码的完整流程
2. **市场调研工作流**: 全面的市场分析流程
3. **产品分析工作流**: 产品功能和定位分析
4. **旅游规划工作流**: 智能旅游行程规划
5. **数据分析工作流**: 数据处理和分析

### API使用

#### 执行工作流

```bash
curl -X POST "http://localhost:8000/api/v1/workflows/code_generation_workflow/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "input_data": {
      "user_request": "生成一个Python函数来计算斐波那契数列",
      "programming_language": "Python"
    }
  }'
```

#### 调用智能体

```bash
curl -X POST "http://localhost:8000/api/v1/agents/code_generator/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "skill_name": "generate_code",
    "parameters": {
      "task_description": "生成一个排序算法",
      "language": "Python"
    }
  }'
```

## 🔧 配置

### 环境变量

主要配置项：

- `QWEN_API_KEY`: 千问Plus API密钥（必需）
- `HOST`: 服务器主机地址
- `PORT`: 服务器端口
- `DEBUG`: 调试模式
- `LOG_LEVEL`: 日志级别

### 智能体配置

智能体配置文件位于 `config/agents.yaml`，可以自定义：

- 智能体参数
- LLM配置
- 提示词模板
- 技能定义

## 📊 监控

系统提供完整的监控功能：

- **会话监控**: 查看工作流执行状态
- **任务监控**: 跟踪智能体任务执行
- **日志监控**: 实时查看系统日志
- **性能监控**: 智能体性能统计

## 🛠️ 开发

### 项目结构

```
A2A/
├── src/                    # 后端源码
│   ├── a2a/               # A2A协议实现
│   ├── agents/            # 智能体实现
│   ├── workflow/          # 工作流引擎
│   ├── database/          # 数据库模块
│   ├── config/            # 配置管理
│   └── api/               # API路由
├── frontend/              # 前端源码
├── config/                # 配置文件
├── docs/                  # 文档
└── tests/                 # 测试代码
```

### 添加新智能体

1. 继承 `QwenAgent` 基类
2. 实现专门的技能方法
3. 注册到工作流引擎
4. 更新配置文件

### 创建新工作流

1. 定义工作流步骤
2. 配置智能体调用
3. 设置输入输出模式
4. 注册到系统

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🆘 支持

如有问题，请：

1. 查看文档
2. 搜索已有Issue
3. 创建新Issue
4. 联系维护者

---

**A2A多智能体协作系统** - 让AI智能体协作更简单！
