# A2A多智能体协作系统

基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统。

## 🎯 系统概述

A2A多智能体协作系统是一个基于Google Agent2Agent协议的智能体协作平台，集成了阿里千问Plus大模型，支持复杂的多智能体工作流编排和执行。系统提供了完整的智能体管理、工作流设计、任务监控和MCP服务集成功能。

## 🏗️ 系统架构

### 核心组件
- **A2A协议层**: 基于Google Agent2Agent SDK实现智能体间通信
- **智能体管理**: 支持多种智能体类型和技能管理
- **工作流引擎**: 支持顺序、并行、循环、分支四种执行模式
- **LLM集成**: 统一使用阿里千问Plus (qwen-plus)
- **MCP服务**: 集成Model Context Protocol服务
- **数据库层**: 基于SQLAlchemy的数据持久化
- **Web界面**: Vue.js + Element Plus前端管理界面

### 技术栈
- **后端**: Python 3.12+ + FastAPI + A2A SDK
- **前端**: Vue.js 3 + Element Plus + Vite
- **数据库**: SQLite/MySQL (SQLAlchemy ORM)
- **LLM**: 阿里千问Plus (qwen-plus)
- **协议**: Google Agent2Agent (A2A)
- **服务集成**: Model Context Protocol (MCP)

## 🚀 快速开始

### 环境要求
- Python 3.12+
- Node.js 18+
- npm 或 yarn
- 阿里千问Plus API密钥

### 一键启动（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd A2A

# 使用启动脚本（自动安装依赖并启动）
python scripts/start.py
```

### 手动安装

1. **安装Python依赖**:
```bash
pip install -r requirements.txt
```

2. **安装前端依赖**:
```bash
cd frontend
npm install
```

3. **配置系统**:
```bash
# 复制配置文件
cp config/config.example.yaml config/config.yaml

# 编辑配置文件，设置阿里千问API密钥
# config/config.yaml
```

4. **初始化数据库**:
```bash
# 可选：如果使用MySQL
mysql -u root -p < scripts/init_database.sql
```

5. **启动服务**:
```bash
# 启动后端
python -m src.main

# 启动前端（新终端）
cd frontend
npm run dev
```

### 访问系统
- 🌐 前端界面: http://localhost:3000
- 📚 API文档: http://localhost:8000/docs
- 🔧 API接口: http://localhost:8000

## ✨ 功能特性

### 🤖 智能体管理
- **多类型智能体**: 意图识别、任务分解、代码生成、市场调研等
- **技能系统**: 每个智能体支持多种技能和交互模式
- **健康监控**: 实时监控智能体状态和性能
- **动态注册**: 支持运行时智能体注册和配置

### 🔄 工作流引擎
- **顺序执行**: 按步骤顺序执行任务，支持条件跳转
- **并行执行**: 多智能体并行处理，支持超时控制
- **循环执行**: 支持条件循环和迭代控制
- **分支执行**: 基于条件的智能分支选择
- **结果聚合**: 多种聚合策略（合并、选择、投票、加权）

### 📊 结果聚合策略
- **合并所有** (merge_all): 合并所有并行结果
- **选择最佳** (select_best): 基于评分选择最优结果
- **投票决策** (voting): 多数投票选择结果
- **加权平均** (weighted_average): 基于权重的平均聚合

### 🔌 MCP服务集成
- **文件操作服务**: 安全的文件读写和管理
- **网络搜索服务**: 集成Google/Bing搜索API
- **数据分析服务**: 基础和统计数据分析
- **Git操作服务**: Git仓库状态和操作

### 💾 数据管理
- **会话管理**: 完整的工作流执行会话记录
- **任务追踪**: 详细的任务执行状态和结果
- **消息记录**: 智能体间通信消息存储
- **日志系统**: 分级日志记录和查询

## 📋 预定义工作流

系统包含多个预定义工作流示例：

### 1. 代码生成工作流
```
意图识别 → 任务分解 → 代码生成 → 结果验证
```

### 2. 市场调研工作流
```
调研规划 → 并行调研 → 竞品分析 → 报告生成
```

### 3. 产品分析工作流
```
产品调研 → 竞品分析 → 功能对比 → 分析报告
```

### 4. 旅游规划工作流
```
目的地调研 → 行程规划 → 住宿搜索 → 最终规划
```

### 5. 数据分析工作流
```
数据验证 → 数据分析 → 洞察生成 → 报告创建
```

## 🔗 API接口

### 智能体管理
```http
GET    /agents                    # 获取智能体列表
GET    /agents/{agent_id}         # 获取智能体详情
POST   /agents/{agent_id}/tasks   # 创建智能体任务
GET    /agents/{agent_id}/health  # 智能体健康检查
```

### 工作流管理
```http
GET    /workflows                      # 获取工作流列表
POST   /workflows                      # 创建工作流
POST   /workflows/{workflow_id}/execute # 执行工作流
```

### 会话管理
```http
GET    /sessions                 # 获取会话列表
POST   /sessions                 # 创建会话
GET    /sessions/{session_id}    # 获取会话详情
```

### MCP服务
```http
GET    /mcp/services                              # 获取MCP服务列表
POST   /mcp/services/{service_id}/tools/{tool_name} # 调用MCP工具
GET    /mcp/health                                # 所有MCP服务健康状态
```

## 🛠️ 开发指南

### 添加新智能体
1. 在 `src/agents/` 目录下创建智能体文件
2. 继承 `BaseAgent` 类并实现技能方法
3. 在主应用中注册智能体

### 创建自定义工作流
1. 在 `src/workflows/examples.py` 中定义工作流
2. 使用 `WorkflowDefinition` 和 `WorkflowStep` 构建流程
3. 配置输入输出映射和执行条件

### 集成新的MCP服务
1. 在 `src/mcp/services.py` 中创建服务类
2. 继承 `BaseMCPService` 并实现服务方法
3. 在管理器中注册服务

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t a2a-system .

# 运行容器
docker run -p 8000:8000 -p 3000:3000 a2a-system
```

### 生产环境
1. 使用 `gunicorn` 或 `uvicorn` 部署后端
2. 构建前端静态文件: `npm run build`
3. 使用 `nginx` 代理前端和API

## 📊 监控和日志

- **系统健康状态**: `/health` 端点
- **实时日志**: 使用 `loguru` 记录详细日志
- **性能监控**: 内置统计和监控功能
- **数据库监控**: SQLAlchemy连接池和查询监控

## 🔧 故障排除

### 常见问题

1. **智能体连接失败**
   - 检查阿里千问API密钥配置
   - 确认网络连接正常

2. **工作流执行失败**
   - 查看详细错误日志
   - 检查智能体状态

3. **前端无法访问**
   - 确认后端服务正常运行
   - 检查端口配置

4. **数据库连接问题**
   - 检查数据库配置
   - 确认数据库服务状态

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请：
- 提交 [Issue](https://github.com/your-repo/issues)
- 发送邮件至开发团队
- 查看 [Wiki](https://github.com/your-repo/wiki) 获取更多文档

---

**A2A多智能体协作系统** - 让智能体协作变得简单高效！ 🚀
