# A2A多智能体协作系统

基于Google Agent2Agent (A2A) 协议和阿里千问Plus的多智能体协作系统。

## 🌟 特性

- **🤖 基于Google A2A协议**: 使用最新的Agent2Agent协议实现智能体间通信
- **🧠 阿里千问Plus驱动**: 所有智能体均使用阿里千问Plus模型，确保高质量输出
- **🔄 多智能体协作**: 支持意图识别、任务分解、代码生成、市场调研等多种智能体
- **⚡ 异步处理**: 基于FastAPI和asyncio的高性能异步架构
- **🔧 可配置化**: 灵活的配置管理，支持多种部署环境
- **📊 实时监控**: 内置健康检查和任务状态监控
- **🐍 Python 3.12+**: 使用最新Python特性，确保代码现代化

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   A2A服务器     │    │   智能体层      │
│   (Web UI)     │◄──►│   (FastAPI)    │◄──►│   (千问驱动)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   配置管理      │    │   任务调度      │
                       │   (Pydantic)   │    │   (A2A协议)    │
                       └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.12+
- 阿里千问API密钥

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd A2A
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，填入阿里千问API密钥
```

4. **启动系统**
```bash
python run.py
```

### 获取阿里千问API密钥

1. 访问 [阿里云DashScope](https://dashscope.aliyun.com/)
2. 注册/登录账号
3. 创建API密钥
4. 将密钥填入 `.env` 文件的 `A2A_QWEN_API_KEY` 字段

## 🤖 内置智能体

### 1. 意图识别智能体 (Intent Recognizer)
- **功能**: 分析用户输入，识别用户意图
- **技能**: `recognize_intent`, `analyze_text`, `extract_keywords`
- **用途**: 工作流的第一步，确定用户需求类型

### 2. 任务分解智能体 (Task Decomposer)
- **功能**: 将复杂任务分解为可执行的子任务
- **技能**: `decompose_task`, `plan_project`, `analyze_requirements`
- **用途**: 将用户需求拆分为具体的执行步骤

### 3. 代码生成智能体 (Code Generator)
- **功能**: 根据需求生成高质量代码
- **技能**: `generate_code`, `review_code`, `explain_code`
- **用途**: 自动化代码开发和生成

### 4. 市场调研智能体 (Market Researcher)
- **功能**: 进行市场分析和竞争研究
- **技能**: `research_market`, `analyze_competition`, `generate_report`
- **用途**: 商业分析和市场洞察

### 5. 结果验证智能体 (Result Validator)
- **功能**: 验证任务执行结果的质量
- **技能**: `validate_result`, `assess_quality`, `provide_feedback`
- **用途**: 质量控制和结果优化

## 📡 API接口

### 基础接口

- `GET /` - 系统信息
- `GET /health` - 健康检查
- `GET /docs` - API文档

### 智能体管理

- `GET /agents` - 列出所有智能体
- `GET /agents/{agent_id}` - 获取特定智能体信息

### 任务管理

- `POST /agents/{agent_id}/tasks` - 创建任务
- `GET /tasks/{task_id}` - 获取任务状态
- `DELETE /tasks/{task_id}` - 取消任务
- `GET /tasks` - 列出所有任务

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `A2A_QWEN_API_KEY` | 阿里千问API密钥 | **必填** |
| `A2A_HOST` | 服务器主机 | `0.0.0.0` |
| `A2A_PORT` | 服务器端口 | `8000` |
| `A2A_DEBUG` | 调试模式 | `false` |
| `A2A_LOG_LEVEL` | 日志级别 | `INFO` |

### 智能体配置

每个智能体都可以通过配置文件自定义：

```python
agent_config = create_default_qwen_agent_config(
    agent_id="custom_agent",
    name="自定义智能体",
    description="智能体描述",
    system_prompt="系统提示词",
    api_key="千问API密钥",
    capabilities=["能力1", "能力2"],
    a2a_skills=["技能1", "技能2"]
)
```

## 🧪 使用示例

### 创建任务

```python
import httpx

# 创建意图识别任务
task_request = {
    "task_id": "task_001",
    "skill_name": "recognize_intent",
    "parameters": {
        "user_input": "我想开发一个电商网站",
        "intent_types": ["generate_code", "market_research", "other"]
    }
}

response = httpx.post(
    "http://localhost:8000/agents/intent_recognizer/tasks",
    json=task_request
)
```

### 查询任务状态

```python
response = httpx.get("http://localhost:8000/tasks/task_001")
task_status = response.json()
print(f"任务状态: {task_status['status']}")
```

## 🔍 监控和日志

### 健康检查

访问 `http://localhost:8000/health` 查看系统健康状态

### 日志配置

日志文件默认保存在 `logs/a2a.log`，支持按天轮转和自动清理。

## 🧪 测试系统

运行测试脚本验证系统功能：

```bash
python test_a2a_system.py
```

测试内容包括：
- 系统健康检查
- 智能体列表获取
- 意图识别功能
- 任务分解功能

## 🛠️ 开发指南

### 添加新智能体

1. 继承 `QwenAgent` 类
2. 实现 `_build_task_prompt` 方法
3. 实现 `_parse_result` 方法
4. 注册到A2A服务器

```python
class CustomAgent(QwenAgent):
    def _build_task_prompt(self, skill_name: str, parameters: Dict[str, Any]) -> str:
        # 构建提示词
        pass

    def _parse_result(self, result_text: str, skill_name: str) -> Dict[str, Any]:
        # 解析结果
        pass
```

### 扩展A2A技能

在智能体配置中添加新的A2A技能：

```python
a2a_skills=["existing_skill", "new_skill"]
```

## 📚 技术栈

- **协议**: Google Agent2Agent (A2A) Protocol
- **LLM**: 阿里千问Plus (qwen-plus)
- **后端**: FastAPI + Python 3.12
- **异步**: asyncio + aiohttp
- **配置**: Pydantic Settings
- **日志**: Loguru
- **API客户端**: OpenAI兼容接口

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。

## 🆘 支持

如有问题或建议，请：

1. 查看文档和示例
2. 检查 Issues
3. 创建新的 Issue

---

**注意**: 本系统需要阿里千问API密钥才能正常运行。请确保已正确配置环境变量。
