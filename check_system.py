#!/usr/bin/env python3
"""
A2A系统完整性检查脚本
检查系统各个组件是否正确实现
"""

import os
import sys
import importlib
from pathlib import Path

def check_file_exists(file_path: str, description: str) -> bool:
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (缺失)")
        return False

def check_import(module_name: str, description: str) -> bool:
    """检查模块是否可以导入"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {module_name} (导入失败: {e})")
        return False

def main():
    print("🔍 A2A系统完整性检查")
    print("=" * 50)
    
    issues = []
    
    # 检查核心文件
    print("\n📁 核心文件检查:")
    core_files = [
        ("src/__init__.py", "主模块初始化"),
        ("src/main.py", "主程序入口"),
        ("run.py", "启动脚本"),
        ("requirements.txt", "依赖包列表"),
        (".env.example", "环境配置示例"),
        ("README.md", "项目说明")
    ]
    
    for file_path, desc in core_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失文件: {file_path}")
    
    # 检查A2A协议实现
    print("\n🤖 A2A协议实现检查:")
    a2a_files = [
        ("src/a2a/__init__.py", "A2A模块初始化"),
        ("src/a2a/protocol.py", "A2A协议定义"),
        ("src/a2a/agent.py", "A2A智能体基类"),
        ("src/a2a/server.py", "A2A服务器"),
        ("src/a2a/client.py", "A2A客户端")
    ]
    
    for file_path, desc in a2a_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失A2A文件: {file_path}")
    
    # 检查智能体实现
    print("\n🧠 智能体实现检查:")
    agent_files = [
        ("src/agents/__init__.py", "智能体模块初始化"),
        ("src/agents/qwen_agent.py", "千问智能体基类"),
        ("src/agents/intent_recognizer.py", "意图识别智能体"),
        ("src/agents/task_decomposer.py", "任务分解智能体"),
        ("src/agents/code_generator.py", "代码生成智能体"),
        ("src/agents/market_researcher.py", "市场调研智能体"),
        ("src/agents/result_validator.py", "结果验证智能体")
    ]
    
    for file_path, desc in agent_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失智能体文件: {file_path}")
    
    # 检查工作流引擎
    print("\n⚙️ 工作流引擎检查:")
    workflow_files = [
        ("src/workflow/__init__.py", "工作流模块初始化"),
        ("src/workflow/models.py", "工作流模型"),
        ("src/workflow/engine.py", "工作流引擎"),
        ("src/workflow/executor.py", "工作流执行器")
    ]
    
    for file_path, desc in workflow_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失工作流文件: {file_path}")
    
    # 检查数据库模块
    print("\n💾 数据库模块检查:")
    database_files = [
        ("src/database/__init__.py", "数据库模块初始化"),
        ("src/database/models.py", "数据库模型"),
        ("src/database/database.py", "数据库实现")
    ]
    
    for file_path, desc in database_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失数据库文件: {file_path}")
    
    # 检查配置模块
    print("\n⚙️ 配置模块检查:")
    config_files = [
        ("src/config/__init__.py", "配置模块初始化"),
        ("src/config/settings.py", "系统设置"),
        ("src/config/agent_config.py", "智能体配置")
    ]
    
    for file_path, desc in config_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失配置文件: {file_path}")
    
    # 检查API模块
    print("\n🌐 API模块检查:")
    api_files = [
        ("src/api/__init__.py", "API模块初始化"),
        ("src/api/routes.py", "API路由")
    ]
    
    for file_path, desc in api_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失API文件: {file_path}")
    
    # 检查工作流示例
    print("\n📋 工作流示例检查:")
    workflow_example_files = [
        ("src/workflows/__init__.py", "工作流示例模块初始化"),
        ("src/workflows/examples.py", "工作流示例")
    ]
    
    for file_path, desc in workflow_example_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失工作流示例文件: {file_path}")
    
    # 检查前端文件
    print("\n🎨 前端文件检查:")
    frontend_files = [
        ("frontend/package.json", "前端包配置"),
        ("frontend/src/main.js", "前端入口"),
        ("frontend/src/App.vue", "前端主组件"),
        ("frontend/src/views/Dashboard.vue", "仪表板页面"),
        ("frontend/src/views/Agents.vue", "智能体管理页面"),
        ("frontend/src/views/Workflows.vue", "工作流管理页面"),
        ("frontend/src/views/Sessions.vue", "会话管理页面")
    ]
    
    for file_path, desc in frontend_files:
        if not check_file_exists(file_path, desc):
            issues.append(f"缺失前端文件: {file_path}")
    
    # 检查Python模块导入
    print("\n🐍 Python模块导入检查:")
    
    # 添加项目路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    modules_to_check = [
        ("src.a2a.protocol", "A2A协议模块"),
        ("src.a2a.agent", "A2A智能体模块"),
        ("src.agents.qwen_agent", "千问智能体模块"),
        ("src.workflow.engine", "工作流引擎模块"),
        ("src.database.database", "数据库模块"),
        ("src.config.settings", "配置模块")
    ]
    
    for module_name, desc in modules_to_check:
        if not check_import(module_name, desc):
            issues.append(f"模块导入失败: {module_name}")
    
    # 总结
    print("\n" + "=" * 50)
    if issues:
        print(f"❌ 发现 {len(issues)} 个问题:")
        for issue in issues:
            print(f"   • {issue}")
        print("\n请修复这些问题后重新检查。")
        return False
    else:
        print("✅ 系统完整性检查通过！")
        print("🚀 系统已准备就绪，可以启动。")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
