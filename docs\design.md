# A2A多智能体系统设计文档

## 1. 项目概述

### 1.1 项目目标
基于Google A2A（Agent2Agent）框架，设计并实现一个可配置的多智能体协作系统，支持智能体间的通信协调、任务编排和MCP服务集成。

### 1.2 核心功能
- 配置化的智能体管理（LLM、Token、提示词等）
- 多智能体协作和调用关系配置
- **智能体工作流执行模式**：顺序、并发、循环、分支
- MCP（Model Context Protocol）服务集成
- FastAPI后端服务
- 前端用户界面和结果展示

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI服务   │    │   智能体层      │
│   (Vue.js)     │◄──►│   (Python)     │◄──►│   (A2A协议)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   配置管理      │    │   MCP服务      │
                       │   (YAML/JSON)   │    │   (外部工具)    │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈
- **后端**: Python 3.9+, FastAPI, Pydantic, asyncio
- **前端**: Vue.js 3, Element Plus, Axios
- **配置**: YAML/JSON
- **数据库**: MySQL 5.7+
- **智能体框架**: A2A协议实现
- **LLM集成**: OpenAI API, Anthropic Claude, Google Gemini, 阿里千问, 深度求索, 月之暗面, 智谱AI
- **MCP**: Model Context Protocol客户端

## 3. 智能体工作流设计

### 3.1 工作流执行模式

#### 3.1.1 顺序执行（Sequential）
**特点**：智能体按照定义的顺序依次执行，前一个智能体完成后才开始下一个。

**适用场景**：
- 任务之间有明确的依赖关系
- 需要前一步的输出作为下一步的输入
- 对执行顺序有严格要求

**配置示例**：
```yaml
- step_id: "code_review"
  agent: "code_reviewer"
  execution_mode: "sequential"
  input_mapping:
    generated_code: "${code_results}"
    review_criteria: "代码规范、安全性、性能优化"
  output_mapping:
    review_result: "review_output"
```

#### 3.1.2 并发执行（Parallel）
**特点**：多个智能体同时执行，提高处理效率，支持结果聚合。

**适用场景**：
- 任务之间相互独立
- 需要提高执行效率
- 多角度分析同一问题

**配置示例**：
```yaml
- step_id: "code_generation"
  agent: "code_generator"
  execution_mode: "parallel"
  parallel_agents:
    - agent: "code_generator"
      input_mapping:
        task_description: "${decomposed_tasks[0].description}"
        language: "${decomposed_tasks[0].language}"
    - agent: "code_generator"
      input_mapping:
        task_description: "${decomposed_tasks[1].description}"
        language: "${decomposed_tasks[1].language}"
  aggregation_strategy: "merge_all"
  parallel_timeout: 120
  output_mapping:
    generated_code: "code_results"
```

**并发结果聚合策略**：
- **merge_all**: 合并所有结果
- **select_best**: 选择最佳结果（基于评分机制）
- **voting**: 投票决策（适用于分类任务）
- **weighted_average**: 加权平均（适用于数值结果）

#### 3.1.3 循环执行（Loop）
**特点**：根据条件重复执行某些步骤，支持迭代优化。

**适用场景**：
- 需要多次迭代优化结果
- 基于验证结果决定是否重试
- 渐进式改进任务执行

**配置示例**：
```yaml
- step_id: "retry_loop"
  type: "loop"
  condition: "${current_iteration} < ${max_iterations}"
  loop_target: "task_decomposition"  # 跳转到任务分解步骤
  input_override:
    user_request: "${optimized_requirement}"  # 使用优化后的需求
```

**循环控制**：
- 最大迭代次数限制
- 条件表达式评估
- 输入参数覆盖
- 循环目标步骤指定

#### 3.1.4 分支执行（Branch）
**特点**：根据条件选择不同的执行路径，支持复杂的业务逻辑。

**适用场景**：
- 基于验证结果选择后续操作
- 不同输入类型需要不同处理流程
- 错误处理和异常分支

**配置示例**：
```yaml
- step_id: "validation_branch"
  type: "branch"
  condition: "${validation_output.validation_passed}"
  branches:
    true:  # 验证通过，结束流程
      - step_id: "workflow_complete"
        type: "end"
        output_mapping:
          final_result: "${code_results}"
          validation_report: "${validation_output}"
    false:  # 验证失败，进入优化循环
      - step_id: "requirement_refinement"
        agent: "requirement_refiner"
        input_mapping:
          original_requirement: "${user_request}"
          validation_feedback: "${validation_output}"
        output_mapping:
          refined_requirement: "optimized_requirement"
```

### 3.2 智能体配置

#### 3.2.1 智能体类型

**意图识别智能体**
- 功能：分析用户输入，识别用户意图
- LLM：OpenAI GPT-4
- 能力：intent_recognition

**任务分解智能体**
- 功能：将复杂任务分解为具体子任务
- LLM：阿里千问 qwen-max
- 能力：task_decomposition

**代码生成智能体**
- 功能：根据任务描述生成高质量代码
- LLM：OpenAI GPT-4
- 能力：code_generation
- MCP服务：file_operations, git_operations

**市场调研智能体**
- 功能：生成专业的市场调研报告
- LLM：Anthropic Claude-3-Sonnet
- 能力：market_research, data_analysis
- MCP服务：web_search, data_analysis

**产品分析智能体**
- 功能：同品类产品分析和竞品对比
- LLM：深度求索 deepseek-chat
- 能力：product_analysis, competitive_analysis
- MCP服务：web_search, data_analysis

**旅游规划智能体**
- 功能：制定详细的旅游规划方案
- LLM：月之暗面 moonshot-v1-8k
- 能力：travel_planning, itinerary_generation
- MCP服务：web_search, map_services

**数据分析智能体**
- 功能：专业数据分析和可视化建议
- LLM：智谱AI glm-4
- 能力：data_analysis, statistical_analysis, data_visualization
- MCP服务：data_processing, chart_generation

**结果验证智能体**
- 功能：验证任务执行结果质量
- LLM：OpenAI GPT-4
- 能力：result_validation, quality_assessment, requirement_matching

**需求优化智能体**
- 功能：根据验证反馈优化需求描述
- LLM：Anthropic Claude-3-Sonnet
- 能力：requirement_analysis, requirement_refinement, execution_guidance

### 3.3 工作流示例

#### 3.3.1 代码生成工作流
**流程设计**：
1. **顺序执行**：意图识别 → 任务分解
2. **并发执行**：多个代码生成智能体并行工作
3. **顺序执行**：代码审查 → 结果验证
4. **分支执行**：根据验证结果选择路径
   - 验证通过：结束流程
   - 验证失败：需求优化
5. **循环执行**：使用优化需求重新执行（最多3次）

#### 3.3.2 市场调研工作流
**流程设计**：
1. **顺序执行**：意图识别 → 任务分解 → 市场调研
2. **顺序执行**：结果验证
3. **分支执行**：根据验证结果选择路径
4. **循环执行**：支持迭代优化调研报告

#### 3.3.3 产品分析工作流
**流程设计**：
1. **顺序执行**：意图识别 → 任务分解 → 产品分析
2. **顺序执行**：结果验证
3. **分支执行**：验证通过/失败的不同处理
4. **循环执行**：支持分析报告的迭代完善

#### 3.3.4 旅游规划工作流
**流程设计**：
1. **顺序执行**：意图识别 → 任务分解 → 旅游规划
2. **顺序执行**：结果验证
3. **分支执行**：基于验证结果的路径选择
4. **循环执行**：支持旅游方案的多次优化

## 4. MCP服务集成

### 4.1 远程MCP服务
- **文件操作服务**：read_file, write_file, list_directory
- **网络搜索服务**：search_web, fetch_content, extract_data
- **数据分析服务**：process_data, statistical_analysis, trend_analysis
- **数据处理服务**：clean_data, transform_data, aggregate_data
- **图表生成服务**：create_chart, generate_visualization, export_chart
- **地图服务**：get_location_info, calculate_distance, find_nearby_places
- **Git操作服务**：clone_repository, commit_changes, push_changes

### 4.2 本地MCP服务器
- **深度维基百科搜索**：search_wikipedia, get_article_content, extract_summary
- **网络内容获取**：fetch_url, download_content, parse_html
- **本地文件系统**：read_file, write_file, list_directory, create_directory
- **SQLite数据库**：execute_query, create_table, insert_data, update_data
- **Brave搜索引擎**：web_search, news_search, image_search

## 5. 数据库设计

### 5.1 核心表结构

**会话表（sessions）**
- 记录工作流执行会话
- 包含用户ID、工作流名称、状态、输入输出数据
- 支持会话状态跟踪：pending, running, completed, failed

**任务表（tasks）**
- 记录具体的智能体任务执行
- 包含会话ID、智能体名称、任务描述、执行状态
- 支持执行模式字段：execution_mode, parallel_agents, aggregation_strategy, parallel_timeout

**智能体消息表（agent_messages）**
- 记录智能体间的通信消息
- 包含发送者、接收者、消息类型、内容
- 支持A2A协议的消息追踪

**工作流执行日志表（workflow_logs）**
- 记录工作流执行过程的详细日志
- 包含日志级别、消息内容、上下文信息
- 支持调试和问题排查

## 6. 系统特性

### 6.1 工作流引擎特性
- **多执行模式支持**：顺序、并发、循环、分支
- **条件表达式评估**：支持复杂的业务逻辑判断
- **结果聚合策略**：多种并发结果处理方式
- **超时控制**：并发执行的超时保护机制
- **迭代控制**：循环次数限制和条件控制
- **上下文管理**：工作流执行过程中的数据传递
- **错误处理**：异常情况的捕获和处理

### 6.2 智能体管理特性
- **多LLM支持**：OpenAI、Anthropic、阿里千问、深度求索等
- **配置化管理**：YAML/JSON配置文件
- **能力定义**：智能体功能和服务能力声明
- **MCP集成**：外部工具和服务的无缝集成
- **消息协议**：A2A协议的标准化通信

### 6.3 系统可扩展性
- **插件化架构**：支持新智能体和MCP服务的动态添加
- **配置驱动**：通过配置文件扩展系统功能
- **API接口**：RESTful API支持外部系统集成
- **前端组件化**：Vue.js组件化的用户界面
- **数据库扩展**：支持新表结构和字段的添加

## 7. 部署和运维

### 7.1 环境要求
- Python 3.9+
- MySQL 5.7+
- Node.js 16+（前端构建）
- Docker（可选，容器化部署）

### 7.2 配置管理
- 环境变量配置：API密钥、数据库连接等
- YAML配置文件：智能体和工作流定义
- 前端配置：API端点和界面设置

### 7.3 监控和日志
- 工作流执行状态监控
- 智能体性能指标收集
- 详细的执行日志记录
- 错误告警和通知机制

## 8. 总结

本A2A多智能体系统通过支持**顺序、并发、循环、分支**四种工作流执行模式，实现了灵活且强大的智能体协作框架。系统具备以下核心优势：

1. **执行模式丰富**：满足不同场景的任务编排需求
2. **智能体多样化**：支持多种LLM和专业能力
3. **MCP服务集成**：无缝对接外部工具和服务
4. **配置化管理**：通过配置文件灵活定制系统行为
5. **可扩展架构**：支持新功能和服务的快速集成
6. **完整的监控体系**：全面的执行状态跟踪和日志记录

该系统为构建复杂的多智能体协作应用提供了坚实的技术基础，能够适应各种业务场景的需求。