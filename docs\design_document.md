# A2A多智能体系统Demo设计文档

## 1. 项目概述

### 1.1 项目目标
基于Google A2A（Agent2Agent）框架，设计并实现一个可配置的多智能体协作系统，支持智能体间的通信协调、任务编排和MCP服务集成。

### 1.2 核心功能
- 配置化的智能体管理（LLM、Token、提示词等）
- 多智能体协作和调用关系配置
- MCP（Model Context Protocol）服务集成
- FastAPI后端服务
- 前端用户界面和结果展示

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI服务   │    │   智能体层      │
│   (Vue.js)     │◄──►│   (Python)     │◄──►│   (A2A协议)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   配置管理      │    │   MCP服务      │
                       │   (YAML/JSON)   │    │   (外部工具)    │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈
- **后端**: Python 3.12+, FastAPI, Pydantic, asyncio
- **前端**: Vue.js 3, Element Plus, Axios
- **配置**: YAML/JSON
- **数据库**: MySQL 5.7+
- **智能体框架**: A2A协议实现
- **LLM集成**: OpenAI API, Anthropic Claude, Google Gemini, 阿里千问, 深度求索, 月之暗面, 智谱AI (支持OpenAI兼容API)
- **MCP**: Model Context Protocol客户端

## 3. 模块设计

### 3.1 配置管理模块

#### 3.1.1 智能体配置
```yaml
agents:
  intent_recognizer:
    name: "意图识别智能体"
    llm:
      provider: "openai"
      model: "gpt-4"
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"  # 支持自定义API端点
      temperature: 0.1
    prompt_template: |
      分析用户输入，识别用户意图。
      用户输入：{user_input}
      可能的意图类型：{intent_types}
      请返回最匹配的意图类型和置信度。
    capabilities:
      - "intent_recognition"
      
  task_decomposer:
    name: "任务分解智能体"
    llm:
      provider: "qwen"  # 阿里千问
      model: "qwen-max"
      api_key: "${QWEN_API_KEY}"
      base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      temperature: 0.2
    prompt_template: |
      根据识别的意图，将任务分解为具体的子任务。
      意图：{intent}
      最大任务数：{max_tasks}
      请提供详细的任务分解方案。
    capabilities:
      - "task_decomposition"
      
  code_generator:
    name: "代码生成智能体"
    llm:
      provider: "openai"
      model: "gpt-4"
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      temperature: 0.3
    prompt_template: |
      你是一个专业的代码生成专家。
      任务描述：{task_description}
      编程语言：{language}
      请生成高质量的代码。
    capabilities:
      - "code_generation"
    mcp_services:
      - "file_operations"
      - "git_operations"
      
  market_researcher:
    name: "市场调研智能体"
    llm:
      provider: "anthropic"
      model: "claude-3-sonnet"
      api_key: "${ANTHROPIC_API_KEY}"
      temperature: 0.4
    prompt_template: |
      你是一个专业的市场调研分析师。
      调研主题：{research_topic}
      调研范围：{research_scope}
      目标市场：{target_market}
      请提供详细的市场调研报告，包括：
      1. 市场规模和增长趋势
      2. 竞争格局分析
      3. 目标客户画像
      4. 市场机会和挑战
      5. 建议和策略
    capabilities:
      - "market_research"
      - "data_analysis"
    mcp_services:
      - "web_search"
      - "data_analysis"
      
  product_analyzer:
    name: "产品分析智能体"
    llm:
      provider: "deepseek"  # 深度求索
      model: "deepseek-chat"
      api_key: "${DEEPSEEK_API_KEY}"
      base_url: "https://api.deepseek.com/v1"
      temperature: 0.3
    prompt_template: |
      你是一个专业的产品分析师。
      产品名称：{product_name}
      产品类别：{product_category}
      分析维度：{analysis_dimensions}
      请提供详细的同品类产品分析报告，包括：
      1. 产品功能对比
      2. 价格策略分析
      3. 用户体验评估
      4. 技术特点比较
      5. 市场定位分析
      6. 优劣势总结
    capabilities:
      - "product_analysis"
      - "competitive_analysis"
    mcp_services:
      - "web_search"
      - "data_analysis"
      
  travel_planner:
    name: "旅游规划智能体"
    llm:
      provider: "moonshot"  # 月之暗面
      model: "moonshot-v1-8k"
      api_key: "${MOONSHOT_API_KEY}"
      base_url: "https://api.moonshot.cn/v1"
      temperature: 0.5
    prompt_template: |
      你是一个专业的旅游规划师。
      目的地：{destination}
      旅行时间：{travel_duration}
      预算范围：{budget_range}
      旅行偏好：{travel_preferences}
      人数：{traveler_count}
      请制定详细的旅游规划报告，包括：
      1. 行程安排（逐日计划）
      2. 景点推荐和介绍
      3. 住宿建议
      4. 交通方案
      5. 美食推荐
      6. 预算分配
      7. 注意事项和建议
    capabilities:
      - "travel_planning"
      - "itinerary_generation"
    mcp_services:
      - "web_search"
      - "map_services"
      
  data_analyst:
    name: "数据分析智能体"
    llm:
      provider: "zhipu"  # 智谱AI
      model: "glm-4"
      api_key: "${ZHIPU_API_KEY}"
      base_url: "https://open.bigmodel.cn/api/paas/v4"
      temperature: 0.2
    prompt_template: |
      你是一个专业的数据分析师。
      数据源：{data_source}
      分析目标：{analysis_goal}
      数据类型：{data_type}
      分析维度：{analysis_dimensions}
      请提供详细的数据分析报告，包括：
      1. 数据概览和质量评估
      2. 描述性统计分析
      3. 趋势和模式识别
      4. 相关性分析
      5. 异常值检测
      6. 预测和建议
      7. 可视化图表建议
    capabilities:
      - "data_analysis"
      - "statistical_analysis"
      - "data_visualization"
    mcp_services:
      - "data_processing"
      - "chart_generation"
      
  result_validator:
    name: "结果验证智能体"
    llm:
      provider: "openai"
      model: "gpt-4"
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      temperature: 0.1
    prompt_template: |
      你是一个专业的质量验证专家，负责验证任务执行结果是否满足用户需求。
      原始需求：{original_requirement}
      执行结果：{execution_result}
      验证标准：{validation_criteria}
      
      请从以下维度进行验证：
      1. 完整性验证：结果是否完整回答了用户需求
      2. 准确性验证：结果内容是否准确可靠
      3. 相关性验证：结果是否与需求高度相关
      4. 质量验证：结果的专业性和实用性
      5. 格式验证：结果格式是否符合要求
      
      验证结果请返回：
      - validation_passed: true/false
      - validation_score: 0-100分
      - issues_found: 发现的问题列表
      - improvement_suggestions: 改进建议
      - retry_needed: 是否需要重新执行
    capabilities:
      - "result_validation"
      - "quality_assessment"
      - "requirement_matching"
      
  requirement_refiner:
    name: "需求优化智能体"
    llm:
      provider: "anthropic"
      model: "claude-3-sonnet"
      api_key: "${ANTHROPIC_API_KEY}"
      temperature: 0.3
    prompt_template: |
      你是一个专业的需求分析师，负责根据验证反馈优化和细化用户需求。
      原始需求：{original_requirement}
      验证反馈：{validation_feedback}
      执行历史：{execution_history}
      
      请分析验证反馈，优化需求描述：
      1. 识别需求中的模糊或不完整部分
      2. 根据验证反馈补充具体要求
      3. 调整任务分解策略
      4. 提供更明确的执行指导
      5. 设定更具体的验证标准
      
      输出优化后的需求描述和执行建议。
    capabilities:
      - "requirement_analysis"
      - "requirement_refinement"
      - "execution_guidance"
```

#### 3.1.2 工作流配置
```yaml
workflows:
  code_generation_workflow:
    name: "代码生成工作流"
    description: "用于生成代码的智能体协作工作流，支持循环验证和优化"
    max_iterations: 3  # 最大循环次数
    steps:
      - step_id: "intent_recognition"
        agent: "intent_recognizer"
        input_mapping:
          user_input: "${user_request}"
          intent_types: ["generate_code", "create_report", "analyze_data", "market_research", "product_analysis", "travel_planning"]
        output_mapping:
          intent: "recognized_intent"
          
      - step_id: "task_decomposition"
        agent: "task_decomposer"
        condition: "${recognized_intent} == 'generate_code'"
        input_mapping:
          intent: "${recognized_intent}"
          max_tasks: 5
        output_mapping:
          tasks: "decomposed_tasks"
          
      - step_id: "code_generation"
        agent: "code_generator"
        execution_mode: "parallel"
        parallel_agents:
          - agent: "code_generator"
            input_mapping:
              task_description: "${decomposed_tasks[0].description}"
              language: "${decomposed_tasks[0].language}"
          - agent: "code_generator"
            input_mapping:
              task_description: "${decomposed_tasks[1].description}"
              language: "${decomposed_tasks[1].language}"
        aggregation_strategy: "merge_all"
        parallel_timeout: 120
        output_mapping:
          generated_code: "code_results"
          
      # 顺序执行示例：代码审查
      - step_id: "code_review"
        agent: "code_reviewer"
        execution_mode: "sequential"
        input_mapping:
          generated_code: "${code_results}"
          review_criteria: "代码规范、安全性、性能优化"
        output_mapping:
          review_result: "review_output"
          
      - step_id: "result_validation"
        agent: "result_validator"
        execution_mode: "sequential"
        input_mapping:
          original_requirement: "${user_request}"
          execution_result: "${code_results}"
          review_result: "${review_output}"
          validation_criteria: "代码质量、功能完整性、可读性、最佳实践"
        output_mapping:
          validation_result: "validation_output"
          
      # 分支：根据验证结果决定下一步
      - step_id: "validation_branch"
        type: "branch"
        condition: "${validation_output.validation_passed}"
        branches:
          true:  # 验证通过，结束流程
            - step_id: "workflow_complete"
              type: "end"
              output_mapping:
                final_result: "${code_results}"
                validation_report: "${validation_output}"
          false:  # 验证失败，进入优化循环
            - step_id: "requirement_refinement"
              agent: "requirement_refiner"
              input_mapping:
                original_requirement: "${user_request}"
                validation_feedback: "${validation_output}"
                execution_history: "${workflow_history}"
              output_mapping:
                refined_requirement: "optimized_requirement"
                
            # 循环：使用优化后的需求重新执行
            - step_id: "retry_loop"
              type: "loop"
              condition: "${current_iteration} < ${max_iterations}"
              loop_target: "task_decomposition"  # 跳转到任务分解步骤
              input_override:
                user_request: "${optimized_requirement}"  # 使用优化后的需求
                
            # 达到最大循环次数后的处理
            - step_id: "max_iterations_reached"
              type: "end"
              condition: "${current_iteration} >= ${max_iterations}"
              output_mapping:
                final_result: "${code_results}"
                validation_report: "${validation_output}"
                status: "max_iterations_reached"
          
  market_research_workflow:
    name: "市场调研工作流"
    description: "用于生成市场调研报告的智能体协作工作流，支持循环验证和优化"
    max_iterations: 3
    steps:
      - step_id: "intent_recognition"
        agent: "intent_recognizer"
        input_mapping:
          user_input: "${user_request}"
          intent_types: ["market_research", "competitive_analysis", "industry_analysis"]
        output_mapping:
          intent: "recognized_intent"
          
      - step_id: "task_decomposition"
        agent: "task_decomposer"
        condition: "${recognized_intent} == 'market_research'"
        input_mapping:
          intent: "${recognized_intent}"
          max_tasks: 8
        output_mapping:
          research_tasks: "decomposed_research_tasks"
          
      - step_id: "market_research"
        agent: "market_researcher"
        input_mapping:
          research_topic: "${research_topic}"
          research_scope: "${research_scope}"
          target_market: "${target_market}"
        output_mapping:
          research_report: "market_research_results"
          
      - step_id: "result_validation"
        agent: "result_validator"
        input_mapping:
          original_requirement: "${user_request}"
          execution_result: "${market_research_results}"
          validation_criteria: "市场数据准确性、分析深度、报告完整性、实用性"
        output_mapping:
          validation_result: "validation_output"
          
      - step_id: "validation_branch"
        type: "branch"
        condition: "${validation_output.validation_passed}"
        branches:
          true:
            - step_id: "workflow_complete"
              type: "end"
              output_mapping:
                final_result: "${market_research_results}"
                validation_report: "${validation_output}"
          false:
            - step_id: "requirement_refinement"
              agent: "requirement_refiner"
              input_mapping:
                original_requirement: "${user_request}"
                validation_feedback: "${validation_output}"
                execution_history: "${workflow_history}"
              output_mapping:
                refined_requirement: "optimized_requirement"
                
            - step_id: "retry_loop"
              type: "loop"
              condition: "${current_iteration} < ${max_iterations}"
              loop_target: "task_decomposition"
              input_override:
                user_request: "${optimized_requirement}"
                
            - step_id: "max_iterations_reached"
              type: "end"
              condition: "${current_iteration} >= ${max_iterations}"
              output_mapping:
                final_result: "${market_research_results}"
                validation_report: "${validation_output}"
                status: "max_iterations_reached"
          
  product_analysis_workflow:
    name: "产品分析工作流"
    description: "用于生成同品类产品分析报告的智能体协作工作流，支持循环验证和优化"
    max_iterations: 3
    steps:
      - step_id: "intent_recognition"
        agent: "intent_recognizer"
        input_mapping:
          user_input: "${user_request}"
          intent_types: ["product_analysis", "competitive_analysis", "feature_comparison"]
        output_mapping:
          intent: "recognized_intent"
          
      - step_id: "task_decomposition"
        agent: "task_decomposer"
        condition: "${recognized_intent} == 'product_analysis'"
        input_mapping:
          intent: "${recognized_intent}"
          max_tasks: 6
        output_mapping:
          analysis_tasks: "decomposed_analysis_tasks"
          
      - step_id: "product_analysis"
        agent: "product_analyzer"
        input_mapping:
          product_name: "${product_name}"
          product_category: "${product_category}"
          analysis_dimensions: "${analysis_dimensions}"
        output_mapping:
          analysis_report: "product_analysis_results"
          
      - step_id: "result_validation"
        agent: "result_validator"
        input_mapping:
          original_requirement: "${user_request}"
          execution_result: "${product_analysis_results}"
          validation_criteria: "分析全面性、数据准确性、对比客观性、结论实用性"
        output_mapping:
          validation_result: "validation_output"
          
      - step_id: "validation_branch"
        type: "branch"
        condition: "${validation_output.validation_passed}"
        branches:
          true:
            - step_id: "workflow_complete"
              type: "end"
              output_mapping:
                final_result: "${product_analysis_results}"
                validation_report: "${validation_output}"
          false:
            - step_id: "requirement_refinement"
              agent: "requirement_refiner"
              input_mapping:
                original_requirement: "${user_request}"
                validation_feedback: "${validation_output}"
                execution_history: "${workflow_history}"
              output_mapping:
                refined_requirement: "optimized_requirement"
                
            - step_id: "retry_loop"
              type: "loop"
              condition: "${current_iteration} < ${max_iterations}"
              loop_target: "task_decomposition"
              input_override:
                user_request: "${optimized_requirement}"
                
            - step_id: "max_iterations_reached"
              type: "end"
              condition: "${current_iteration} >= ${max_iterations}"
              output_mapping:
                final_result: "${product_analysis_results}"
                validation_report: "${validation_output}"
                status: "max_iterations_reached"
          
  travel_planning_workflow:
    name: "旅游规划工作流"
    description: "用于生成旅游规划报告的智能体协作工作流，支持循环验证和优化"
    max_iterations: 3
    steps:
      - step_id: "intent_recognition"
        agent: "intent_recognizer"
        input_mapping:
          user_input: "${user_request}"
          intent_types: ["travel_planning", "itinerary_generation", "destination_recommendation"]
        output_mapping:
          intent: "recognized_intent"
          
      - step_id: "task_decomposition"
        agent: "task_decomposer"
        condition: "${recognized_intent} == 'travel_planning'"
        input_mapping:
          intent: "${recognized_intent}"
          max_tasks: 7
        output_mapping:
          planning_tasks: "decomposed_planning_tasks"
          
      - step_id: "travel_planning"
        agent: "travel_planner"
        input_mapping:
          destination: "${destination}"
          travel_duration: "${travel_duration}"
          budget_range: "${budget_range}"
          travel_preferences: "${travel_preferences}"
          traveler_count: "${traveler_count}"
        output_mapping:
          travel_plan: "travel_planning_results"
          
      - step_id: "result_validation"
        agent: "result_validator"
        input_mapping:
          original_requirement: "${user_request}"
          execution_result: "${travel_planning_results}"
          validation_criteria: "行程合理性、预算准确性、实用性、个性化程度"
        output_mapping:
          validation_result: "validation_output"
          
      - step_id: "validation_branch"
        type: "branch"
        condition: "${validation_output.validation_passed}"
        branches:
          true:
            - step_id: "workflow_complete"
              type: "end"
              output_mapping:
                final_result: "${travel_planning_results}"
                validation_report: "${validation_output}"
          false:
            - step_id: "requirement_refinement"
              agent: "requirement_refiner"
              input_mapping:
                original_requirement: "${user_request}"
                validation_feedback: "${validation_output}"
                execution_history: "${workflow_history}"
              output_mapping:
                refined_requirement: "optimized_requirement"
                
            - step_id: "retry_loop"
              type: "loop"
              condition: "${current_iteration} < ${max_iterations}"
              loop_target: "task_decomposition"
              input_override:
                user_request: "${optimized_requirement}"
                
            - step_id: "max_iterations_reached"
              type: "end"
              condition: "${current_iteration} >= ${max_iterations}"
              output_mapping:
                final_result: "${travel_planning_results}"
                validation_report: "${validation_output}"
                status: "max_iterations_reached"
          
  data_analysis_workflow:
    name: "数据分析工作流"
    description: "用于生成数据分析报告的智能体协作工作流，支持循环验证和优化"
    max_iterations: 3
    steps:
      - step_id: "intent_recognition"
        agent: "intent_recognizer"
        input_mapping:
          user_input: "${user_request}"
          intent_types: ["data_analysis", "statistical_analysis", "data_visualization"]
        output_mapping:
          intent: "recognized_intent"
          
      - step_id: "task_decomposition"
        agent: "task_decomposer"
        condition: "${recognized_intent} == 'data_analysis'"
        input_mapping:
          intent: "${recognized_intent}"
          max_tasks: 6
        output_mapping:
          analysis_tasks: "decomposed_analysis_tasks"
          
      - step_id: "data_analysis"
        agent: "data_analyst"
        input_mapping:
          data_source: "${data_source}"
          analysis_goal: "${analysis_goal}"
          data_type: "${data_type}"
          analysis_dimensions: "${analysis_dimensions}"
        output_mapping:
          analysis_report: "data_analysis_results"
          
      - step_id: "result_validation"
        agent: "result_validator"
        input_mapping:
          original_requirement: "${user_request}"
          execution_result: "${data_analysis_results}"
          validation_criteria: "数据处理准确性、分析方法合理性、结论可靠性、可视化效果"
        output_mapping:
          validation_result: "validation_output"
          
      - step_id: "validation_branch"
        type: "branch"
        condition: "${validation_output.validation_passed}"
        branches:
          true:
            - step_id: "workflow_complete"
              type: "end"
              output_mapping:
                final_result: "${data_analysis_results}"
                validation_report: "${validation_output}"
          false:
            - step_id: "requirement_refinement"
              agent: "requirement_refiner"
              input_mapping:
                original_requirement: "${user_request}"
                validation_feedback: "${validation_output}"
                execution_history: "${workflow_history}"
              output_mapping:
                refined_requirement: "optimized_requirement"
                
            - step_id: "retry_loop"
              type: "loop"
              condition: "${current_iteration} < ${max_iterations}"
              loop_target: "task_decomposition"
              input_override:
                user_request: "${optimized_requirement}"
                
            - step_id: "max_iterations_reached"
              type: "end"
              condition: "${current_iteration} >= ${max_iterations}"
              output_mapping:
                final_result: "${data_analysis_results}"
                validation_report: "${validation_output}"
                status: "max_iterations_reached"
```

#### 3.1.3 MCP服务配置

##### ******* 远程MCP服务配置
```yaml
mcp_services:
  file_operations:
    name: "文件操作服务"
    endpoint: "mcp://localhost:8001/file-ops"
    capabilities:
      - "read_file"
      - "write_file"
      - "list_directory"
    authentication:
      type: "api_key"
      key: "${FILE_OPS_API_KEY}"
      
  web_search:
    name: "网络搜索服务"
    endpoint: "mcp://localhost:8002/web-search"
    capabilities:
      - "search_web"
      - "fetch_content"
      - "extract_data"
    authentication:
      type: "bearer_token"
      token: "${SEARCH_API_TOKEN}"
      
  data_analysis:
    name: "数据分析服务"
    endpoint: "mcp://localhost:8003/data-analysis"
    capabilities:
      - "process_data"
      - "statistical_analysis"
      - "trend_analysis"
      - "correlation_analysis"
    authentication:
      type: "api_key"
      key: "${DATA_ANALYSIS_API_KEY}"
      
  data_processing:
    name: "数据处理服务"
    endpoint: "mcp://localhost:8004/data-processing"
    capabilities:
      - "clean_data"
      - "transform_data"
      - "aggregate_data"
      - "export_data"
    authentication:
      type: "api_key"
      key: "${DATA_PROCESSING_API_KEY}"
      
  chart_generation:
    name: "图表生成服务"
    endpoint: "mcp://localhost:8005/chart-generation"
    capabilities:
      - "create_chart"
      - "generate_visualization"
      - "export_chart"
    authentication:
      type: "api_key"
      key: "${CHART_API_KEY}"
      
  map_services:
    name: "地图服务"
    endpoint: "mcp://localhost:8006/map-services"
    capabilities:
      - "get_location_info"
      - "calculate_distance"
      - "find_nearby_places"
      - "get_directions"
    authentication:
      type: "api_key"
      key: "${MAP_API_KEY}"
      
  git_operations:
    name: "Git操作服务"
    endpoint: "mcp://localhost:8007/git-ops"
    capabilities:
      - "clone_repository"
      - "commit_changes"
      - "push_changes"
      - "create_branch"
    authentication:
      type: "token"
      token: "${GIT_TOKEN}"
```

##### ******* 本地MCP服务器配置
```yaml
# 本地MCP服务器配置，支持stdio方式调用
local_mcp_servers:
  mcp-deepwiki:
    id: "ZuvNebUft3E1S4xUWwSgH"
    isActive: true
    name: "mcp-deepwiki"
    description: "深度维基百科搜索服务"
    type: "stdio"  # stdio | http | websocket
    command: "npx"
    args:
      - "-y"
      - "mcp-deepwiki@latest"
    env: {}
    capabilities:
      - "search_wikipedia"
      - "get_article_content"
      - "extract_summary"
    
  fetch:
    id: "fetch"
    isActive: true
    name: "fetch"
    description: "网络内容获取服务"
    type: "stdio"
    registryUrl: ""
    command: "uvx"
    args:
      - "mcp-server-fetch"
    env: {}
    capabilities:
      - "fetch_url"
      - "download_content"
      - "parse_html"
    
  filesystem:
    id: "filesystem"
    isActive: true
    name: "filesystem"
    description: "本地文件系统操作服务"
    type: "stdio"
    command: "npx"
    args:
      - "-y"
      - "@modelcontextprotocol/server-filesystem"
      - "D:/Box/Prompt"  # 工作目录路径
    env: {}
    capabilities:
      - "read_file"
      - "write_file"
      - "list_directory"
      - "create_directory"
      - "delete_file"
    
  sqlite:
    id: "sqlite"
    isActive: true
    name: "sqlite"
    description: "SQLite数据库操作服务"
    type: "stdio"
    command: "uvx"
    args:
      - "mcp-server-sqlite"
      - "--db-path"
      - "./data/app.db"
    env: {}
    capabilities:
      - "execute_query"
      - "create_table"
      - "insert_data"
      - "update_data"
      - "delete_data"
    
  brave-search:
    id: "brave-search"
    isActive: true
    name: "brave-search"
    description: "Brave搜索引擎服务"
    type: "stdio"
    command: "npx"
    args:
      - "-y"
      - "@modelcontextprotocol/server-brave-search"
    env:
      BRAVE_API_KEY: "${BRAVE_API_KEY}"
    capabilities:
      - "web_search"
      - "news_search"
      - "image_search"
```

### 3.2 智能体管理模块

#### 3.2.1 智能体基类
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import openai
from openai import OpenAI

class AgentConfig(BaseModel):
    name: str
    llm: Dict[str, Any]
    prompt_template: str
    capabilities: List[str]
    mcp_services: Optional[List[str]] = []

class AgentMessage(BaseModel):
    sender: str
    receiver: str
    message_type: str
    content: Dict[str, Any]
    timestamp: float

class LLMClientFactory:
    """LLM客户端工厂类，支持多种OpenAI兼容的模型提供商"""
    
    @staticmethod
    def create_client(llm_config: Dict[str, Any]):
        """根据配置创建LLM客户端"""
        provider = llm_config.get("provider", "openai")
        api_key = llm_config.get("api_key")
        base_url = llm_config.get("base_url")
        
        if provider in ["openai", "qwen", "deepseek", "moonshot", "zhipu"]:
            # 所有这些提供商都使用OpenAI兼容的API
            client = OpenAI(
                api_key=api_key,
                base_url=base_url
            )
            return client
        elif provider == "anthropic":
            # Anthropic使用自己的SDK
            import anthropic
            return anthropic.Anthropic(api_key=api_key)
        else:
            raise ValueError(f"不支持的LLM提供商: {provider}")

class BaseAgent(ABC):
    def __init__(self, config: AgentConfig):
        self.config = config
        self.llm_client = self._init_llm_client()
        self.mcp_clients = self._init_mcp_clients()
    
    @abstractmethod
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        pass
    
    @abstractmethod
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体任务"""
        pass
    
    def _init_llm_client(self):
        """初始化LLM客户端"""
        return LLMClientFactory.create_client(self.config.llm)
    
    def _init_mcp_clients(self):
        """初始化MCP客户端"""
        # 根据配置初始化MCP服务客户端
        mcp_clients = {}
        for service_name in self.config.mcp_services:
            # 这里会根据MCP服务配置初始化客户端
            mcp_clients[service_name] = self._create_mcp_client(service_name)
        return mcp_clients
    
    def _create_mcp_client(self, service_name: str):
        """创建MCP服务客户端"""
        # 实现MCP客户端创建逻辑
        pass
    
    async def call_llm(self, prompt: str, **kwargs) -> str:
        """调用LLM生成响应"""
        provider = self.config.llm.get("provider", "openai")
        model = self.config.llm.get("model")
        temperature = self.config.llm.get("temperature", 0.7)
        
        if provider == "anthropic":
            response = await self.llm_client.messages.create(
                model=model,
                max_tokens=4000,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        else:
            # OpenAI兼容的API调用
            response = await self.llm_client.chat.completions.create(
                model=model,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}],
                **kwargs
            )
            return response.choices[0].message.content
```

#### 3.2.2 A2A协议实现
```python
class A2AProtocol:
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.message_queue = asyncio.Queue()
        self.running = False
    
    async def register_agent(self, agent_id: str, agent: BaseAgent):
        """注册智能体"""
        self.agents[agent_id] = agent
    
    async def send_message(self, message: AgentMessage):
        """发送消息"""
        await self.message_queue.put(message)
    
    async def start_protocol(self):
        """启动A2A协议处理"""
        self.running = True
        while self.running:
            try:
                message = await asyncio.wait_for(
                    self.message_queue.get(), timeout=1.0
                )
                await self._route_message(message)
            except asyncio.TimeoutError:
                continue
    
    async def _route_message(self, message: AgentMessage):
        """路由消息到目标智能体"""
        if message.receiver in self.agents:
            agent = self.agents[message.receiver]
            response = await agent.process_message(message)
            if response:
                await self.send_message(response)
```

### 3.3 工作流引擎

```python
import time
import asyncio
from typing import Dict, List, Any, Optional

class WorkflowEngine:
    def __init__(self, a2a_protocol: A2AProtocol):
        self.a2a_protocol = a2a_protocol
        self.workflows: Dict[str, Dict] = {}
    
    async def execute_workflow(self, workflow_name: str, initial_input: Dict[str, Any]):
        """执行工作流，支持循环、分支和验证"""
        workflow = self.workflows[workflow_name]
        context = {
            "input": initial_input,
            "user_request": initial_input.get("user_request", ""),
            "current_iteration": 0,
            "max_iterations": workflow.get("max_iterations", 3),
            "workflow_history": []
        }
        
        step_index = 0
        while step_index < len(workflow["steps"]):
            step = workflow["steps"][step_index]
            
            # 记录步骤执行历史
            context["workflow_history"].append({
                "step_id": step.get("step_id", f"step_{step_index}"),
                "iteration": context["current_iteration"],
                "timestamp": time.time()
            })
            
            if self._evaluate_condition(step.get("condition"), context):
                result = await self._execute_step(step, context)
                
                # 处理不同类型的步骤
                step_type = step.get("type", "agent")
                
                if step_type == "end":
                    # 结束步骤
                    context.update(result)
                    break
                elif step_type == "branch":
                    # 分支步骤
                    next_step_index = await self._handle_branch(step, context, step_index)
                    if next_step_index is not None:
                        step_index = next_step_index
                        continue
                elif step_type == "loop":
                    # 循环步骤
                    next_step_index = await self._handle_loop(step, context, workflow)
                    if next_step_index is not None:
                        step_index = next_step_index
                        continue
                else:
                    # 普通智能体步骤
                    context.update(result)
            
            step_index += 1
        
        return context
    
    async def _handle_branch(self, step: Dict, context: Dict, current_index: int) -> Optional[int]:
        """处理分支逻辑"""
        condition = step.get("condition")
        branches = step.get("branches", {})
        
        condition_result = self._evaluate_condition(condition, context)
        branch_key = str(condition_result).lower()
        
        if branch_key in branches:
            branch_steps = branches[branch_key]
            # 执行分支中的步骤
            for branch_step in branch_steps:
                if branch_step.get("type") == "end":
                    # 如果是结束步骤，更新上下文并返回None表示结束
                    if "output_mapping" in branch_step:
                        mapped_output = self._map_output(branch_step["output_mapping"], context)
                        context.update(mapped_output)
                    return None
                else:
                    # 执行分支步骤
                    result = await self._execute_step(branch_step, context)
                    context.update(result)
        
        return current_index + 1
    
    async def _handle_loop(self, step: Dict, context: Dict, workflow: Dict) -> Optional[int]:
        """处理循环逻辑"""
        condition = step.get("condition")
        loop_target = step.get("loop_target")
        input_override = step.get("input_override", {})
        
        if self._evaluate_condition(condition, context):
            # 增加迭代计数
            context["current_iteration"] += 1
            
            # 应用输入覆盖
            for key, value in input_override.items():
                context[key] = self._evaluate_expression(value, context)
            
            # 找到目标步骤的索引
            target_index = self._find_step_index(workflow["steps"], loop_target)
            if target_index is not None:
                return target_index
        
        return None
    
    def _find_step_index(self, steps: List[Dict], step_id: str) -> Optional[int]:
        """根据step_id找到步骤索引"""
        for i, step in enumerate(steps):
            if step.get("step_id") == step_id:
                return i
        return None
    
    async def _execute_step(self, step: Dict, context: Dict) -> Dict:
        """执行工作流步骤"""
        step_type = step.get("type", "agent")
        
        if step_type in ["branch", "loop", "end"]:
            # 控制流步骤，返回空结果
            return {}
        
        agent_id = step["agent"]
        input_data = self._map_input(step["input_mapping"], context)
        
        execution_mode = step.get("execution_mode", "sequential")
        if execution_mode == "parallel":
            return await self._execute_parallel_step(step, input_data, context)
        else:
            return await self._execute_sequential_step(agent_id, input_data, step)
    
    async def _execute_sequential_step(self, agent_id: str, input_data: Dict, step: Dict) -> Dict:
        """执行顺序步骤"""
        # 调用智能体
        response = await self.a2a_protocol.send_message(agent_id, input_data)
        return {"output": response}
    
    async def _execute_parallel_step(self, step: Dict, input_data: Dict, context: Dict) -> Dict:
        """执行并行步骤"""
        parallel_agents = step.get("parallel_agents", [])
        aggregation_strategy = step.get("aggregation_strategy", "merge_all")
        timeout = step.get("parallel_timeout", 60)
        
        tasks = []
        
        for agent_config in parallel_agents:
            agent_id = agent_config["agent"]
            agent_input = self._map_input(agent_config.get("input_mapping", {}), context)
            task = self.a2a_protocol.send_message(agent_id, agent_input)
            tasks.append((agent_id, task))
        
        # 等待所有并行任务完成，带超时处理
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks]), 
                timeout=timeout
            )
            
            # 根据聚合策略处理结果
            aggregated_result = self._aggregate_parallel_results(
                results, aggregation_strategy, [agent_id for agent_id, _ in tasks]
            )
            
            return {"output": aggregated_result, "parallel_outputs": results}
            
        except asyncio.TimeoutError:
            # 处理超时情况
            return {"error": "Parallel execution timeout", "timeout": timeout}
        except Exception as e:
             return {"error": f"Parallel execution failed: {str(e)}"}
     
     def _aggregate_parallel_results(self, results: List[Dict], strategy: str, agent_ids: List[str]) -> Dict:
         """聚合并行执行结果"""
         if not results:
             return {}
         
         if strategy == "merge_all":
             # 合并所有结果
             merged = {"agents_results": {}}
             for i, result in enumerate(results):
                 agent_id = agent_ids[i] if i < len(agent_ids) else f"agent_{i}"
                 merged["agents_results"][agent_id] = result
             
             # 如果所有结果都有相同的字段，尝试合并
             if all(isinstance(r, dict) for r in results):
                 common_fields = set(results[0].keys())
                 for result in results[1:]:
                     common_fields &= set(result.keys())
                 
                 for field in common_fields:
                     if field not in merged:
                         values = [r[field] for r in results]
                         if all(isinstance(v, str) for v in values):
                             merged[field] = "\n\n".join(values)
                         elif all(isinstance(v, (int, float)) for v in values):
                             merged[field] = sum(values) / len(values)
                         else:
                             merged[field] = values
             
             return merged
         
         elif strategy == "select_best":
             # 选择最佳结果（基于某种评分机制）
             best_result = results[0]
             best_score = self._calculate_result_score(best_result)
             
             for result in results[1:]:
                 score = self._calculate_result_score(result)
                 if score > best_score:
                     best_result = result
                     best_score = score
             
             return {"best_result": best_result, "score": best_score}
         
         elif strategy == "voting":
             # 投票决策（适用于分类或选择任务）
             votes = {}
             for result in results:
                 # 假设结果包含一个"decision"字段
                 decision = result.get("decision", str(result))
                 votes[decision] = votes.get(decision, 0) + 1
             
             # 选择得票最多的结果
             winner = max(votes.keys(), key=lambda k: votes[k])
             return {"decision": winner, "votes": votes, "confidence": votes[winner] / len(results)}
         
         elif strategy == "weighted_average":
             # 加权平均（适用于数值结果）
             if all(isinstance(r, dict) and "value" in r and "confidence" in r for r in results):
                 total_weight = sum(r["confidence"] for r in results)
                 if total_weight > 0:
                     weighted_sum = sum(r["value"] * r["confidence"] for r in results)
                     return {"weighted_average": weighted_sum / total_weight, "total_weight": total_weight}
             
             # 如果不符合加权平均的格式，回退到简单平均
             numeric_results = [r for r in results if isinstance(r, (int, float))]
             if numeric_results:
                 return {"average": sum(numeric_results) / len(numeric_results)}
         
         # 默认返回第一个结果
         return results[0] if results else {}
     
     def _calculate_result_score(self, result: Dict) -> float:
         """计算结果质量分数"""
         if isinstance(result, dict):
             # 如果结果包含置信度分数
             if "confidence" in result:
                 return float(result["confidence"])
             
             # 如果结果包含质量分数
             if "quality_score" in result:
                 return float(result["quality_score"])
             
             # 基于结果完整性评分
             completeness = len([v for v in result.values() if v is not None and v != ""])
             return completeness / max(len(result), 1)
         
         # 对于非字典结果，返回默认分数
         return 0.5
     
     def _evaluate_condition(self, condition: Optional[str], context: Dict) -> bool:
        """评估条件表达式"""
        if not condition:
            return True
        
        # 安全的条件评估实现
        try:
            # 支持常见的条件表达式
            if "validation_result" in condition:
                # 验证结果条件
                validation_result = context.get("validation_result", {})
                if "is_valid" in condition:
                    return validation_result.get("is_valid", False)
                elif "score" in condition:
                    score = validation_result.get("score", 0)
                    # 解析分数条件，如 "validation_result.score >= 0.8"
                    if ">= 0.8" in condition:
                        return score >= 0.8
                    elif ">= 0.7" in condition:
                        return score >= 0.7
                    elif "< 0.7" in condition:
                        return score < 0.7
            
            # 迭代次数条件
            if "current_iteration" in condition:
                current_iteration = context.get("current_iteration", 0)
                max_iterations = context.get("max_iterations", 3)
                if "< max_iterations" in condition:
                    return current_iteration < max_iterations
                elif ">= max_iterations" in condition:
                    return current_iteration >= max_iterations
            
            # 默认返回True
            return True
        except Exception:
            return False
    
    def _evaluate_expression(self, expression: str, context: Dict) -> Any:
        """安全地评估表达式"""
        if isinstance(expression, str) and expression.startswith("$"):
            # 从上下文中获取值
            context_key = expression[1:]
            return context.get(context_key)
        elif expression == "validation_result.feedback":
            validation_result = context.get("validation_result", {})
            return validation_result.get("feedback", "")
        else:
            return expression
    
    def _map_input(self, mapping: Dict, context: Dict) -> Dict:
        """映射输入数据"""
        result = {}
        for key, value in mapping.items():
            if isinstance(value, str) and value.startswith("$"):
                # 从上下文中获取值
                context_key = value[1:]
                result[key] = context.get(context_key)
            else:
                result[key] = value
        return result
    
    def _map_output(self, mapping: Dict, context: Dict) -> Dict:
        """映射输出数据"""
        result = {}
        for key, value in mapping.items():
            if isinstance(value, str):
                result[key] = self._evaluate_expression(value, context)
            else:
                result[key] = value
        return result
```

### 3.4 数据库设计

#### 3.4.1 数据库表结构

```sql
-- 会话表
CREATE TABLE sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(100),
    workflow_name VARCHAR(100) NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    input_data JSON,
    output_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 任务表
CREATE TABLE tasks (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    agent_name VARCHAR(100) NOT NULL,
    task_name VARCHAR(200),
    task_description TEXT,
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    input_data JSON,
    output_data JSON,
    error_message TEXT,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_agent_name (agent_name),
    INDEX idx_status (status)
);

-- 智能体消息表
CREATE TABLE agent_messages (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    sender VARCHAR(100) NOT NULL,
    receiver VARCHAR(100) NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    content JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_sender (sender),
    INDEX idx_receiver (receiver),
    INDEX idx_timestamp (timestamp)
);

-- 工作流执行日志表
CREATE TABLE workflow_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR') DEFAULT 'INFO',
    message TEXT NOT NULL,
    context JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_level (level),
    INDEX idx_timestamp (timestamp)
);
```

#### 3.4.2 数据模型

```python
from sqlalchemy import Column, String, Text, JSON, Enum, TIMESTAMP, ForeignKey, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

Base = declarative_base()

class TaskStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class LogLevel(enum.Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"

class Session(Base):
    __tablename__ = "sessions"
    
    id = Column(String(36), primary_key=True)
    user_id = Column(String(100))
    workflow_name = Column(String(100), nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    input_data = Column(JSON)
    output_data = Column(JSON)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    tasks = relationship("Task", back_populates="session", cascade="all, delete-orphan")
    messages = relationship("AgentMessage", back_populates="session", cascade="all, delete-orphan")
    logs = relationship("WorkflowLog", back_populates="session", cascade="all, delete-orphan")

class Task(Base):
    __tablename__ = "tasks"
    
    id = Column(String(36), primary_key=True)
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    agent_name = Column(String(100), nullable=False)
    task_name = Column(String(200))
    task_description = Column(Text)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    input_data = Column(JSON)
    output_data = Column(JSON)
    error_message = Column(Text)
    started_at = Column(TIMESTAMP)
    completed_at = Column(TIMESTAMP)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    session = relationship("Session", back_populates="tasks")

class AgentMessage(Base):
    __tablename__ = "agent_messages"
    
    id = Column(String(36), primary_key=True)
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    sender = Column(String(100), nullable=False)
    receiver = Column(String(100), nullable=False)
    message_type = Column(String(50), nullable=False)
    content = Column(JSON)
    timestamp = Column(TIMESTAMP, default=datetime.utcnow)
    
    # 关系
    session = relationship("Session", back_populates="messages")

class WorkflowLog(Base):
    __tablename__ = "workflow_logs"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    level = Column(Enum(LogLevel), default=LogLevel.INFO)
    message = Column(Text, nullable=False)
    context = Column(JSON)
    timestamp = Column(TIMESTAMP, default=datetime.utcnow)
    
    # 关系
    session = relationship("Session", back_populates="logs")
```

### 3.5 MCP集成模块

```python
class MCPClient:
    def __init__(self, service_config: Dict[str, Any]):
        self.name = service_config["name"]
        self.service_type = service_config.get("type", "http")  # stdio | http | websocket
        self.capabilities = service_config["capabilities"]
        
        if self.service_type == "stdio":
            self.command = service_config["command"]
            self.args = service_config.get("args", [])
            self.env = service_config.get("env", {})
            self.process = None
        else:
            self.endpoint = service_config["endpoint"]
    
    async def call_service(self, capability: str, parameters: Dict[str, Any]):
        """调用MCP服务"""
        if capability not in self.capabilities:
            raise ValueError(f"Capability {capability} not supported")
        
        if self.service_type == "stdio":
            return await self._call_stdio_service(capability, parameters)
        else:
            return await self._call_http_service(capability, parameters)
    
    async def _call_stdio_service(self, capability: str, parameters: Dict[str, Any]):
        """通过stdio方式调用本地MCP服务"""
        import asyncio
        import json
        import os
        
        # 启动MCP服务进程
        if self.process is None or self.process.returncode is not None:
            env = os.environ.copy()
            env.update(self.env)
            
            self.process = await asyncio.create_subprocess_exec(
                self.command,
                *self.args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env
            )
        
        # 构造MCP请求
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": capability,
            "params": parameters
        }
        
        # 发送请求
        request_data = json.dumps(request) + "\n"
        self.process.stdin.write(request_data.encode())
        await self.process.stdin.drain()
        
        # 读取响应
        response_line = await self.process.stdout.readline()
        response = json.loads(response_line.decode().strip())
        
        if "error" in response:
            raise Exception(f"MCP服务错误: {response['error']}")
        
        return response.get("result")
    
    async def _call_http_service(self, capability: str, parameters: Dict[str, Any]):
        """通过HTTP方式调用远程MCP服务"""
        # 实现HTTP MCP协议调用
        # 这里需要根据MCP协议规范实现具体的通信逻辑
        pass
    
    async def close(self):
        """关闭MCP客户端"""
        if self.service_type == "stdio" and self.process:
            self.process.terminate()
            await self.process.wait()

class MCPManager:
    def __init__(self):
        self.services: Dict[str, MCPClient] = {}
        self.local_servers: Dict[str, MCPClient] = {}
    
    def register_service(self, service_name: str, service_config: Dict[str, Any]):
        """注册远程MCP服务"""
        self.services[service_name] = MCPClient(service_config)
    
    def register_local_server(self, server_id: str, server_config: Dict[str, Any]):
        """注册本地MCP服务器"""
        if server_config.get("isActive", True):
            self.local_servers[server_id] = MCPClient(server_config)
    
    def load_local_servers_config(self, config_data: Dict[str, Any]):
        """从配置数据加载本地MCP服务器"""
        mcp_servers = config_data.get("mcpServers", {})
        
        for server_id, server_config in mcp_servers.items():
            if server_config.get("isActive", True):
                # 标准化配置格式
                normalized_config = {
                    "name": server_config.get("name", server_id),
                    "description": server_config.get("description", ""),
                    "type": server_config.get("type", "stdio"),
                    "command": server_config.get("command"),
                    "args": server_config.get("args", []),
                    "env": server_config.get("env", {}),
                    "capabilities": self._infer_capabilities(server_config)
                }
                
                self.local_servers[server_id] = MCPClient(normalized_config)
    
    def _infer_capabilities(self, server_config: Dict[str, Any]) -> List[str]:
        """根据服务器名称推断能力"""
        name = server_config.get("name", "").lower()
        
        capability_map = {
            "filesystem": ["read_file", "write_file", "list_directory", "create_directory", "delete_file"],
            "fetch": ["fetch_url", "download_content", "parse_html"],
            "sqlite": ["execute_query", "create_table", "insert_data", "update_data", "delete_data"],
            "brave-search": ["web_search", "news_search", "image_search"],
            "deepwiki": ["search_wikipedia", "get_article_content", "extract_summary"]
        }
        
        for key, capabilities in capability_map.items():
            if key in name:
                return capabilities
        
        return ["general_capability"]
    
    async def call_service(self, service_name: str, capability: str, parameters: Dict[str, Any]):
        """调用指定的MCP服务（远程）"""
        if service_name not in self.services:
            raise ValueError(f"Service {service_name} not found")
        
        return await self.services[service_name].call_service(capability, parameters)
    
    async def call_local_server(self, server_id: str, capability: str, parameters: Dict[str, Any]):
        """调用指定的本地MCP服务器"""
        if server_id not in self.local_servers:
            raise ValueError(f"Local server {server_id} not found")
        
        return await self.local_servers[server_id].call_service(capability, parameters)
    
    def list_active_servers(self) -> Dict[str, Dict[str, Any]]:
        """列出所有活跃的MCP服务器"""
        result = {}
        
        # 远程服务
        for name, client in self.services.items():
            result[name] = {
                "type": "remote",
                "name": client.name,
                "capabilities": client.capabilities
            }
        
        # 本地服务器
        for server_id, client in self.local_servers.items():
            result[server_id] = {
                "type": "local",
                "name": client.name,
                "capabilities": client.capabilities
            }
        
        return result
    
    async def close_all(self):
        """关闭所有MCP客户端"""
        for client in self.services.values():
            await client.close()
        
        for client in self.local_servers.values():
            await client.close()
```

## 4. FastAPI后端设计

### 4.1 API结构

```python
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional

app = FastAPI(title="A2A多智能体系统", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class UserRequest(BaseModel):
    content: str
    workflow_type: Optional[str] = "default"
    parameters: Optional[Dict[str, Any]] = {}

class TaskModification(BaseModel):
    task_id: str
    new_description: str
    parameters: Optional[Dict[str, Any]] = {}

# 响应模型
class IntentResponse(BaseModel):
    intent: str
    confidence: float
    suggested_workflow: str

class TaskDecomposition(BaseModel):
    tasks: List[Dict[str, Any]]
    estimated_time: int
    dependencies: List[Dict[str, str]]

class ExecutionStatus(BaseModel):
    session_id: str
    status: str  # "pending", "running", "completed", "failed"
    progress: float
    current_task: Optional[str]
    results: List[Dict[str, Any]]
```

### 4.2 主要API端点

```python
# 意图识别
@app.post("/api/v1/intent/recognize", response_model=IntentResponse)
async def recognize_intent(request: UserRequest):
    """识别用户意图"""
    # 调用意图识别智能体
    pass

# 任务分解
@app.post("/api/v1/tasks/decompose", response_model=TaskDecomposition)
async def decompose_tasks(intent: str, parameters: Dict[str, Any]):
    """根据意图分解任务"""
    # 调用任务分解智能体
    pass

# 修改任务
@app.put("/api/v1/tasks/modify")
async def modify_tasks(modifications: List[TaskModification]):
    """用户修改任务"""
    # 更新任务配置
    pass

# 执行工作流
@app.post("/api/v1/workflow/execute")
async def execute_workflow(session_id: str, background_tasks: BackgroundTasks):
    """执行工作流"""
    background_tasks.add_task(run_workflow, session_id)
    return {"message": "工作流已启动", "session_id": session_id}

# 获取执行状态
@app.get("/api/v1/workflow/status/{session_id}", response_model=ExecutionStatus)
async def get_execution_status(session_id: str):
    """获取工作流执行状态"""
    # 返回当前执行状态
    pass

# 获取结果
@app.get("/api/v1/results/{session_id}")
async def get_results(session_id: str):
    """获取执行结果"""
    # 返回最终结果
    pass

# WebSocket连接（实时状态更新）
@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket连接，实时推送执行状态"""
    await websocket.accept()
    # 实现实时状态推送
    pass

# MCP配置管理API
class MCPServerConfig(BaseModel):
    isActive: bool
    name: str
    description: Optional[str] = ""
    baseUrl: Optional[str] = ""
    command: str
    args: List[str]
    env: Dict[str, str] = {}
    type: Optional[str] = "stdio"
    registryUrl: Optional[str] = ""

class MCPServersConfig(BaseModel):
    mcpServers: Dict[str, MCPServerConfig]

# 获取本地MCP服务器配置
@app.get("/api/v1/mcp/servers")
async def get_mcp_servers():
    """获取当前本地MCP服务器配置"""
    # 从配置文件或数据库读取MCP服务器配置
    return mcp_manager.list_active_servers()

# 更新本地MCP服务器配置
@app.post("/api/v1/mcp/servers")
async def update_mcp_servers(config: MCPServersConfig):
    """更新本地MCP服务器配置"""
    try:
        # 关闭现有连接
        await mcp_manager.close_all()
        
        # 加载新配置
        mcp_manager.load_local_servers_config(config.dict())
        
        return {"message": "MCP服务器配置已更新", "status": "success"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")

# 测试MCP服务器连接
@app.post("/api/v1/mcp/servers/{server_id}/test")
async def test_mcp_server(server_id: str):
    """测试指定MCP服务器的连接"""
    try:
        # 尝试调用服务器的基本能力
        result = await mcp_manager.call_local_server(
            server_id, 
            "ping", 
            {}
        )
        return {"status": "success", "message": "连接正常", "result": result}
    except Exception as e:
        return {"status": "error", "message": f"连接失败: {str(e)}"}

# 获取MCP服务器能力
@app.get("/api/v1/mcp/servers/{server_id}/capabilities")
async def get_mcp_server_capabilities(server_id: str):
    """获取指定MCP服务器的能力列表"""
    if server_id not in mcp_manager.local_servers:
        raise HTTPException(status_code=404, detail="MCP服务器未找到")
    
    client = mcp_manager.local_servers[server_id]
    return {
        "server_id": server_id,
        "name": client.name,
        "capabilities": client.capabilities
    }

# 调用MCP服务器功能
@app.post("/api/v1/mcp/servers/{server_id}/call")
async def call_mcp_server_capability(
    server_id: str, 
    capability: str, 
    parameters: Dict[str, Any]
):
    """调用指定MCP服务器的特定能力"""
    try:
        result = await mcp_manager.call_local_server(
            server_id, 
            capability, 
            parameters
        )
        return {"status": "success", "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"调用失败: {str(e)}")
```

## 5. 前端设计

### 5.1 页面结构

```
src/
├── components/
│   ├── IntentRecognition.vue      # 意图识别组件
│   ├── TaskDecomposition.vue      # 任务分解组件
│   ├── TaskEditor.vue             # 任务编辑组件
│   ├── ExecutionMonitor.vue       # 执行监控组件
│   ├── ResultDisplay.vue          # 结果展示组件
│   ├── MCPConfiguration.vue       # MCP配置管理组件
│   ├── AgentConfiguration.vue     # 智能体配置组件
│   ├── MCPServerCard.vue          # MCP服务器卡片组件
│   ├── AgentCard.vue              # 智能体卡片组件
│   ├── MCPAssignment.vue          # MCP分配组件
│   └── common/
│       ├── LoadingSpinner.vue
│       ├── ProgressBar.vue
│       └── StatusIndicator.vue
├── views/
│   ├── Home.vue                   # 主页面
│   ├── WorkflowDesigner.vue       # 工作流设计器
│   └── History.vue                # 历史记录
├── stores/
│   ├── workflow.js                # 工作流状态管理
│   ├── results.js                 # 结果状态管理
│   ├── mcp.js                     # MCP配置状态管理
│   └── agents.js                  # 智能体状态管理
└── utils/
    ├── api.js                     # API调用封装
    ├── websocket.js               # WebSocket连接
    ├── formatters.js              # 结果格式化
    └── mcpApi.js                  # MCP相关API调用
```

### 5.2 主要组件设计

#### 5.2.1 主页面组件
```vue
<template>
  <div class="main-container">
    <!-- 导航栏 -->
    <el-menu mode="horizontal" :default-active="activeMenu" @select="handleMenuSelect">
      <el-menu-item index="home">主页</el-menu-item>
      <el-menu-item index="mcp-config">MCP配置</el-menu-item>
      <el-menu-item index="agent-config">智能体配置</el-menu-item>
      <el-menu-item index="workflow">工作流设计</el-menu-item>
      <el-menu-item index="history">历史记录</el-menu-item>
    </el-menu>

    <!-- 主页面内容 -->
    <div v-if="activeMenu === 'home'" class="home-content">
      <!-- 用户输入区域 -->
      <el-card class="input-section">
        <h3>请描述您的需求</h3>
        <el-input
          v-model="userInput"
          type="textarea"
          :rows="4"
          placeholder="例如：生成一个用户管理系统的代码"
        />
        <el-button @click="submitRequest" type="primary" :loading="loading">
          提交需求
        </el-button>
      </el-card>

      <!-- 意图识别结果 -->
      <IntentRecognition
        v-if="intentResult"
        :result="intentResult"
        @confirm="onIntentConfirm"
      />

      <!-- 任务分解 -->
      <TaskDecomposition
        v-if="tasks"
        :tasks="tasks"
        @modify="onTaskModify"
        @confirm="onTaskConfirm"
      />

      <!-- 执行监控 -->
      <ExecutionMonitor
        v-if="executing"
        :session-id="sessionId"
        @complete="onExecutionComplete"
      />

      <!-- 结果展示 -->
      <ResultDisplay
        v-if="results"
        :results="results"
      />
    </div>

    <!-- MCP配置页面 -->
    <MCPConfiguration v-if="activeMenu === 'mcp-config'" />

    <!-- 智能体配置页面 -->
    <AgentConfiguration v-if="activeMenu === 'agent-config'" />

    <!-- 工作流设计页面 -->
    <WorkflowDesigner v-if="activeMenu === 'workflow'" />

    <!-- 历史记录页面 -->
    <History v-if="activeMenu === 'history'" />
  </div>
</template>

<script>
import { ref } from 'vue'
import IntentRecognition from '@/components/IntentRecognition.vue'
import TaskDecomposition from '@/components/TaskDecomposition.vue'
import ExecutionMonitor from '@/components/ExecutionMonitor.vue'
import ResultDisplay from '@/components/ResultDisplay.vue'
import MCPConfiguration from '@/components/MCPConfiguration.vue'
import AgentConfiguration from '@/components/AgentConfiguration.vue'
import WorkflowDesigner from '@/views/WorkflowDesigner.vue'
import History from '@/views/History.vue'

export default {
  name: 'MainApp',
  components: {
    IntentRecognition,
    TaskDecomposition,
    ExecutionMonitor,
    ResultDisplay,
    MCPConfiguration,
    AgentConfiguration,
    WorkflowDesigner,
    History
  },
  setup() {
    const activeMenu = ref('home')
    const userInput = ref('')
    const loading = ref(false)
    const intentResult = ref(null)
    const tasks = ref(null)
    const executing = ref(false)
    const sessionId = ref('')
    const results = ref(null)

    const handleMenuSelect = (index) => {
      activeMenu.value = index
    }

    const submitRequest = async () => {
      // 提交用户需求的逻辑
    }

    const onIntentConfirm = (result) => {
      // 意图确认的逻辑
    }

    const onTaskModify = (modifiedTasks) => {
      // 任务修改的逻辑
    }

    const onTaskConfirm = (confirmedTasks) => {
      // 任务确认的逻辑
    }

    const onExecutionComplete = (result) => {
      // 执行完成的逻辑
    }

    return {
      activeMenu,
      userInput,
      loading,
      intentResult,
      tasks,
      executing,
      sessionId,
      results,
      handleMenuSelect,
      submitRequest,
      onIntentConfirm,
      onTaskModify,
      onTaskConfirm,
      onExecutionComplete
    }
  }
}
</script>
```

#### 5.2.2 MCP配置管理组件
```vue
<template>
  <div class="mcp-configuration">
    <el-card class="config-header">
      <div class="header-content">
        <h2>MCP服务配置管理</h2>
        <div class="header-actions">
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加MCP服务
          </el-button>
          <el-button @click="refreshServers">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- MCP服务器列表 -->
    <div class="server-grid">
      <MCPServerCard
        v-for="server in mcpServers"
        :key="server.id"
        :server="server"
        @edit="editServer"
        @delete="deleteServer"
        @test="testServer"
        @toggle="toggleServer"
      />
    </div>

    <!-- 添加/编辑MCP服务对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingServer ? '编辑MCP服务' : '添加MCP服务'"
      width="600px"
    >
      <el-form :model="serverForm" :rules="serverRules" ref="serverFormRef" label-width="120px">
        <el-form-item label="服务名称" prop="name">
          <el-input v-model="serverForm.name" placeholder="请输入服务名称" />
        </el-form-item>
        
        <el-form-item label="服务描述" prop="description">
          <el-input v-model="serverForm.description" type="textarea" placeholder="请输入服务描述" />
        </el-form-item>
        
        <el-form-item label="服务类型" prop="type">
          <el-select v-model="serverForm.type" placeholder="请选择服务类型">
            <el-option label="本地stdio" value="stdio" />
            <el-option label="HTTP服务" value="http" />
            <el-option label="WebSocket" value="websocket" />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="serverForm.type === 'stdio'" label="启动命令" prop="command">
          <el-input v-model="serverForm.command" placeholder="如: npx, uvx, python" />
        </el-form-item>
        
        <el-form-item v-if="serverForm.type === 'stdio'" label="命令参数">
          <el-tag
            v-for="(arg, index) in serverForm.args"
            :key="index"
            closable
            @close="removeArg(index)"
            class="arg-tag"
          >
            {{ arg }}
          </el-tag>
          <el-input
            v-if="showArgInput"
            ref="argInputRef"
            v-model="newArg"
            size="small"
            @keyup.enter="addArg"
            @blur="addArg"
            class="arg-input"
          />
          <el-button v-else size="small" @click="showNewArgInput">
            + 添加参数
          </el-button>
        </el-form-item>
        
        <el-form-item v-if="serverForm.type === 'http'" label="服务URL" prop="baseUrl">
          <el-input v-model="serverForm.baseUrl" placeholder="http://localhost:8080" />
        </el-form-item>
        
        <el-form-item label="环境变量">
          <div v-for="(value, key) in serverForm.env" :key="key" class="env-item">
            <el-input v-model="key" placeholder="变量名" class="env-key" disabled />
            <el-input v-model="serverForm.env[key]" placeholder="变量值" class="env-value" />
            <el-button type="danger" size="small" @click="removeEnvVar(key)">
              删除
            </el-button>
          </div>
          <div class="add-env">
            <el-input v-model="newEnvKey" placeholder="变量名" class="env-key" />
            <el-input v-model="newEnvValue" placeholder="变量值" class="env-value" />
            <el-button @click="addEnvVar">添加</el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveServer">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import MCPServerCard from './MCPServerCard.vue'
import { useMCPStore } from '@/stores/mcp'

export default {
  name: 'MCPConfiguration',
  components: {
    MCPServerCard,
    Plus,
    Refresh
  },
  setup() {
    const mcpStore = useMCPStore()
    const showAddDialog = ref(false)
    const editingServer = ref(null)
    const serverFormRef = ref(null)
    const showArgInput = ref(false)
    const argInputRef = ref(null)
    const newArg = ref('')
    const newEnvKey = ref('')
    const newEnvValue = ref('')
    
    const serverForm = reactive({
      name: '',
      description: '',
      type: 'stdio',
      command: '',
      args: [],
      baseUrl: '',
      env: {}
    })
    
    const serverRules = {
      name: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
      type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
      command: [{ required: true, message: '请输入启动命令', trigger: 'blur' }]
    }
    
    const mcpServers = ref([])
    
    const refreshServers = async () => {
      try {
        mcpServers.value = await mcpStore.fetchServers()
      } catch (error) {
        ElMessage.error('获取MCP服务列表失败')
      }
    }
    
    const editServer = (server) => {
      editingServer.value = server
      Object.assign(serverForm, server)
      showAddDialog.value = true
    }
    
    const deleteServer = async (serverId) => {
      try {
        await ElMessageBox.confirm('确定要删除这个MCP服务吗？', '确认删除', {
          type: 'warning'
        })
        await mcpStore.deleteServer(serverId)
        await refreshServers()
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    const testServer = async (serverId) => {
      try {
        const result = await mcpStore.testServer(serverId)
        if (result.success) {
          ElMessage.success('连接测试成功')
        } else {
          ElMessage.error(`连接测试失败: ${result.error}`)
        }
      } catch (error) {
        ElMessage.error('连接测试失败')
      }
    }
    
    const toggleServer = async (serverId, isActive) => {
      try {
        await mcpStore.toggleServer(serverId, isActive)
        await refreshServers()
        ElMessage.success(isActive ? '服务已启用' : '服务已禁用')
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
    
    const saveServer = async () => {
      try {
        await serverFormRef.value.validate()
        if (editingServer.value) {
          await mcpStore.updateServer(editingServer.value.id, serverForm)
        } else {
          await mcpStore.createServer(serverForm)
        }
        showAddDialog.value = false
        await refreshServers()
        ElMessage.success('保存成功')
        resetForm()
      } catch (error) {
        ElMessage.error('保存失败')
      }
    }
    
    const resetForm = () => {
      Object.assign(serverForm, {
        name: '',
        description: '',
        type: 'stdio',
        command: '',
        args: [],
        baseUrl: '',
        env: {}
      })
      editingServer.value = null
    }
    
    const showNewArgInput = () => {
      showArgInput.value = true
      nextTick(() => {
        argInputRef.value?.focus()
      })
    }
    
    const addArg = () => {
      if (newArg.value.trim()) {
        serverForm.args.push(newArg.value.trim())
        newArg.value = ''
      }
      showArgInput.value = false
    }
    
    const removeArg = (index) => {
      serverForm.args.splice(index, 1)
    }
    
    const addEnvVar = () => {
      if (newEnvKey.value.trim() && newEnvValue.value.trim()) {
        serverForm.env[newEnvKey.value.trim()] = newEnvValue.value.trim()
        newEnvKey.value = ''
        newEnvValue.value = ''
      }
    }
    
    const removeEnvVar = (key) => {
      delete serverForm.env[key]
    }
    
    onMounted(() => {
      refreshServers()
    })
    
    return {
      mcpServers,
      showAddDialog,
      editingServer,
      serverForm,
      serverRules,
      serverFormRef,
      showArgInput,
      argInputRef,
      newArg,
      newEnvKey,
      newEnvValue,
      refreshServers,
      editServer,
      deleteServer,
      testServer,
      toggleServer,
      saveServer,
      resetForm,
      showNewArgInput,
      addArg,
      removeArg,
      addEnvVar,
      removeEnvVar
    }
  }
}
</script>

<style scoped>
.mcp-configuration {
  padding: 20px;
}

.config-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.server-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.arg-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.arg-input {
  width: 120px;
  margin-right: 8px;
}

.env-item {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.add-env {
  display: flex;
  gap: 10px;
  align-items: center;
}

.env-key {
  width: 150px;
}

.env-value {
  flex: 1;
}
</style>
```

#### 5.2.3 智能体配置组件
```vue
<template>
  <div class="agent-configuration">
    <el-card class="config-header">
      <div class="header-content">
        <h2>智能体配置管理</h2>
        <div class="header-actions">
          <el-button type="primary" @click="showAddAgentDialog = true">
            <el-icon><Plus /></el-icon>
            添加智能体
          </el-button>
          <el-button @click="refreshAgents">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 智能体列表 -->
    <div class="agent-grid">
      <AgentCard
        v-for="agent in agents"
        :key="agent.id"
        :agent="agent"
        @edit="editAgent"
        @delete="deleteAgent"
        @configure-mcp="configureMCP"
        @toggle="toggleAgent"
      />
    </div>

    <!-- 添加/编辑智能体对话框 -->
    <el-dialog
      v-model="showAddAgentDialog"
      :title="editingAgent ? '编辑智能体' : '添加智能体'"
      width="700px"
    >
      <el-form :model="agentForm" :rules="agentRules" ref="agentFormRef" label-width="120px">
        <el-form-item label="智能体名称" prop="name">
          <el-input v-model="agentForm.name" placeholder="请输入智能体名称" />
        </el-form-item>
        
        <el-form-item label="智能体描述" prop="description">
          <el-input v-model="agentForm.description" type="textarea" placeholder="请输入智能体描述" />
        </el-form-item>
        
        <el-form-item label="模型提供商" prop="provider">
          <el-select v-model="agentForm.provider" placeholder="请选择模型提供商">
            <el-option label="OpenAI" value="openai" />
            <el-option label="Anthropic" value="anthropic" />
            <el-option label="Google" value="google" />
            <el-option label="阿里千问" value="qwen" />
            <el-option label="深度求索" value="deepseek" />
            <el-option label="月之暗面" value="moonshot" />
            <el-option label="智谱AI" value="zhipu" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模型名称" prop="model">
          <el-input v-model="agentForm.model" placeholder="如: gpt-4, claude-3-opus" />
        </el-form-item>
        
        <el-form-item label="系统提示词" prop="systemPrompt">
          <el-input 
            v-model="agentForm.systemPrompt" 
            type="textarea" 
            :rows="4"
            placeholder="请输入系统提示词"
          />
        </el-form-item>
        
        <el-form-item label="温度参数">
          <el-slider 
            v-model="agentForm.temperature" 
            :min="0" 
            :max="2" 
            :step="0.1"
            show-input
          />
        </el-form-item>
        
        <el-form-item label="最大令牌数">
          <el-input-number 
            v-model="agentForm.maxTokens" 
            :min="1" 
            :max="32000"
            placeholder="最大输出令牌数"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddAgentDialog = false">取消</el-button>
          <el-button type="primary" @click="saveAgent">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- MCP分配对话框 -->
    <el-dialog
      v-model="showMCPDialog"
      title="配置MCP服务"
      width="600px"
    >
      <MCPAssignment
        :agent-id="currentAgentId"
        :assigned-mcps="assignedMCPs"
        :available-mcps="availableMCPs"
        @save="saveMCPAssignment"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import AgentCard from './AgentCard.vue'
import MCPAssignment from './MCPAssignment.vue'
import { useAgentStore } from '@/stores/agents'
import { useMCPStore } from '@/stores/mcp'

export default {
  name: 'AgentConfiguration',
  components: {
    AgentCard,
    MCPAssignment,
    Plus,
    Refresh
  },
  setup() {
    const agentStore = useAgentStore()
    const mcpStore = useMCPStore()
    const showAddAgentDialog = ref(false)
    const showMCPDialog = ref(false)
    const editingAgent = ref(null)
    const currentAgentId = ref('')
    const agentFormRef = ref(null)
    
    const agentForm = reactive({
      name: '',
      description: '',
      provider: '',
      model: '',
      systemPrompt: '',
      temperature: 0.7,
      maxTokens: 2000
    })
    
    const agentRules = {
      name: [{ required: true, message: '请输入智能体名称', trigger: 'blur' }],
      provider: [{ required: true, message: '请选择模型提供商', trigger: 'change' }],
      model: [{ required: true, message: '请输入模型名称', trigger: 'blur' }]
    }
    
    const agents = ref([])
    const assignedMCPs = ref([])
    const availableMCPs = ref([])
    
    const refreshAgents = async () => {
      try {
        agents.value = await agentStore.fetchAgents()
      } catch (error) {
        ElMessage.error('获取智能体列表失败')
      }
    }
    
    const editAgent = (agent) => {
      editingAgent.value = agent
      Object.assign(agentForm, agent)
      showAddAgentDialog.value = true
    }
    
    const deleteAgent = async (agentId) => {
      try {
        await ElMessageBox.confirm('确定要删除这个智能体吗？', '确认删除', {
          type: 'warning'
        })
        await agentStore.deleteAgent(agentId)
        await refreshAgents()
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    const toggleAgent = async (agentId, isActive) => {
      try {
        await agentStore.toggleAgent(agentId, isActive)
        await refreshAgents()
        ElMessage.success(isActive ? '智能体已启用' : '智能体已禁用')
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
    
    const configureMCP = async (agentId) => {
      currentAgentId.value = agentId
      try {
        assignedMCPs.value = await agentStore.getAgentMCPs(agentId)
        availableMCPs.value = await mcpStore.fetchServers()
        showMCPDialog.value = true
      } catch (error) {
        ElMessage.error('获取MCP配置失败')
      }
    }
    
    const saveAgent = async () => {
      try {
        await agentFormRef.value.validate()
        if (editingAgent.value) {
          await agentStore.updateAgent(editingAgent.value.id, agentForm)
        } else {
          await agentStore.createAgent(agentForm)
        }
        showAddAgentDialog.value = false
        await refreshAgents()
        ElMessage.success('保存成功')
        resetForm()
      } catch (error) {
        ElMessage.error('保存失败')
      }
    }
    
    const saveMCPAssignment = async (mcpAssignments) => {
      try {
        await agentStore.updateAgentMCPs(currentAgentId.value, mcpAssignments)
        showMCPDialog.value = false
        ElMessage.success('MCP配置保存成功')
      } catch (error) {
        ElMessage.error('MCP配置保存失败')
      }
    }
    
    const resetForm = () => {
      Object.assign(agentForm, {
        name: '',
        description: '',
        provider: '',
        model: '',
        systemPrompt: '',
        temperature: 0.7,
        maxTokens: 2000
      })
      editingAgent.value = null
    }
    
    onMounted(() => {
      refreshAgents()
    })
    
    return {
      agents,
      showAddAgentDialog,
      showMCPDialog,
      editingAgent,
      currentAgentId,
      agentForm,
      agentRules,
      agentFormRef,
      assignedMCPs,
      availableMCPs,
      refreshAgents,
      editAgent,
      deleteAgent,
      toggleAgent,
      configureMCP,
      saveAgent,
      saveMCPAssignment,
      resetForm
    }
  }
}
</script>

<style scoped>
.agent-configuration {
  padding: 20px;
}

.config-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}
</style>
```

#### 5.2.4 MCP服务器卡片组件
```vue
<template>
  <el-card class="mcp-server-card" :class="{ 'inactive': !server.isActive }">
    <template #header>
      <div class="card-header">
        <div class="server-info">
          <h3>{{ server.name }}</h3>
          <el-tag :type="server.isActive ? 'success' : 'info'" size="small">
            {{ server.isActive ? '已启用' : '已禁用' }}
          </el-tag>
        </div>
        <div class="server-actions">
          <el-switch
            v-model="server.isActive"
            @change="$emit('toggle', server.id, $event)"
          />
          <el-dropdown trigger="click">
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="$emit('edit', server)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('test', server.id)">
                  <el-icon><Connection /></el-icon>
                  测试连接
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('delete', server.id)" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>
    
    <div class="server-content">
      <p class="server-description">{{ server.description || '暂无描述' }}</p>
      
      <div class="server-details">
        <div class="detail-item">
          <span class="label">类型:</span>
          <el-tag size="small">{{ getTypeLabel(server.type) }}</el-tag>
        </div>
        
        <div v-if="server.type === 'stdio'" class="detail-item">
          <span class="label">命令:</span>
          <code>{{ server.command }} {{ server.args?.join(' ') }}</code>
        </div>
        
        <div v-if="server.type === 'http'" class="detail-item">
          <span class="label">URL:</span>
          <code>{{ server.baseUrl }}</code>
        </div>
        
        <div v-if="server.capabilities?.length" class="detail-item">
          <span class="label">能力:</span>
          <div class="capabilities">
            <el-tag
              v-for="capability in server.capabilities"
              :key="capability"
              size="small"
              type="info"
            >
              {{ capability }}
            </el-tag>
          </div>
        </div>
        
        <div v-if="Object.keys(server.env || {}).length" class="detail-item">
          <span class="label">环境变量:</span>
          <div class="env-vars">
            <el-tag
              v-for="(value, key) in server.env"
              :key="key"
              size="small"
              type="warning"
            >
              {{ key }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { MoreFilled, Edit, Connection, Delete } from '@element-plus/icons-vue'

export default {
  name: 'MCPServerCard',
  components: {
    MoreFilled,
    Edit,
    Connection,
    Delete
  },
  props: {
    server: {
      type: Object,
      required: true
    }
  },
  emits: ['edit', 'delete', 'test', 'toggle'],
  methods: {
    getTypeLabel(type) {
      const typeMap = {
        stdio: '本地stdio',
        http: 'HTTP服务',
        websocket: 'WebSocket'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style scoped>
.mcp-server-card {
  transition: all 0.3s ease;
}

.mcp-server-card.inactive {
  opacity: 0.6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.server-info h3 {
  margin: 0;
  font-size: 16px;
}

.server-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.server-content {
  padding-top: 10px;
}

.server-description {
  color: #666;
  margin-bottom: 15px;
  font-size: 14px;
}

.server-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: #333;
  min-width: 60px;
  font-size: 12px;
}

.capabilities,
.env-vars {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  word-break: break-all;
}
</style>
```

#### 5.2.5 智能体卡片组件
```vue
<template>
  <el-card class="agent-card" :class="{ 'inactive': !agent.isActive }">
    <template #header>
      <div class="card-header">
        <div class="agent-info">
          <h3>{{ agent.name }}</h3>
          <el-tag :type="agent.isActive ? 'success' : 'info'" size="small">
            {{ agent.isActive ? '已启用' : '已禁用' }}
          </el-tag>
        </div>
        <div class="agent-actions">
          <el-switch
            v-model="agent.isActive"
            @change="$emit('toggle', agent.id, $event)"
          />
          <el-dropdown trigger="click">
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="$emit('edit', agent)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('configure-mcp', agent.id)">
                  <el-icon><Setting /></el-icon>
                  配置MCP
                </el-dropdown-item>
                <el-dropdown-item @click="$emit('delete', agent.id)" divided>
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>
    
    <div class="agent-content">
      <p class="agent-description">{{ agent.description || '暂无描述' }}</p>
      
      <div class="agent-details">
        <div class="detail-item">
          <span class="label">提供商:</span>
          <el-tag size="small" :type="getProviderType(agent.provider)">
            {{ getProviderLabel(agent.provider) }}
          </el-tag>
        </div>
        
        <div class="detail-item">
          <span class="label">模型:</span>
          <code>{{ agent.model }}</code>
        </div>
        
        <div class="detail-item">
          <span class="label">温度:</span>
          <span>{{ agent.temperature }}</span>
        </div>
        
        <div class="detail-item">
          <span class="label">最大令牌:</span>
          <span>{{ agent.maxTokens }}</span>
        </div>
        
        <div v-if="agent.assignedMCPs?.length" class="detail-item">
          <span class="label">MCP服务:</span>
          <div class="mcp-services">
            <el-tag
              v-for="mcp in agent.assignedMCPs"
              :key="mcp.id"
              size="small"
              type="success"
            >
              {{ mcp.name }}
            </el-tag>
          </div>
        </div>
        
        <div v-if="agent.systemPrompt" class="detail-item system-prompt">
          <span class="label">系统提示:</span>
          <div class="prompt-content">
            {{ truncateText(agent.systemPrompt, 100) }}
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { MoreFilled, Edit, Setting, Delete } from '@element-plus/icons-vue'

export default {
  name: 'AgentCard',
  components: {
    MoreFilled,
    Edit,
    Setting,
    Delete
  },
  props: {
    agent: {
      type: Object,
      required: true
    }
  },
  emits: ['edit', 'delete', 'configure-mcp', 'toggle'],
  methods: {
    getProviderLabel(provider) {
      const providerMap = {
        openai: 'OpenAI',
        anthropic: 'Anthropic',
        google: 'Google',
        qwen: '阿里千问',
        deepseek: '深度求索',
        moonshot: '月之暗面',
        zhipu: '智谱AI'
      }
      return providerMap[provider] || provider
    },
    
    getProviderType(provider) {
      const typeMap = {
        openai: 'primary',
        anthropic: 'success',
        google: 'warning',
        qwen: 'info',
        deepseek: 'danger',
        moonshot: 'primary',
        zhipu: 'success'
      }
      return typeMap[provider] || 'info'
    },
    
    truncateText(text, maxLength) {
      if (!text) return ''
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
    }
  }
}
</script>

<style scoped>
.agent-card {
  transition: all 0.3s ease;
}

.agent-card.inactive {
  opacity: 0.6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.agent-info h3 {
  margin: 0;
  font-size: 16px;
}

.agent-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.agent-content {
  padding-top: 10px;
}

.agent-description {
  color: #666;
  margin-bottom: 15px;
  font-size: 14px;
}

.agent-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detail-item.system-prompt {
  flex-direction: column;
  align-items: flex-start;
}

.label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
  font-size: 12px;
}

.mcp-services {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.prompt-content {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-top: 4px;
  width: 100%;
}

code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}
</style>
```

#### 5.2.6 MCP分配组件
```vue
<template>
  <div class="mcp-assignment">
    <div class="assignment-header">
      <h4>为智能体分配MCP服务</h4>
      <p class="description">选择要分配给此智能体的MCP服务，智能体将能够调用这些服务的功能。</p>
    </div>
    
    <div class="assignment-content">
      <div class="available-mcps">
        <h5>可用的MCP服务</h5>
        <div class="mcp-list">
          <div
            v-for="mcp in availableMCPs"
            :key="mcp.id"
            class="mcp-item"
            :class="{ 'assigned': isAssigned(mcp.id) }"
            @click="toggleAssignment(mcp)"
          >
            <div class="mcp-info">
              <div class="mcp-header">
                <span class="mcp-name">{{ mcp.name }}</span>
                <el-tag :type="mcp.isActive ? 'success' : 'info'" size="small">
                  {{ mcp.isActive ? '已启用' : '已禁用' }}
                </el-tag>
              </div>
              <p class="mcp-description">{{ mcp.description || '暂无描述' }}</p>
              <div class="mcp-capabilities">
                <el-tag
                  v-for="capability in mcp.capabilities?.slice(0, 3)"
                  :key="capability"
                  size="small"
                  type="info"
                >
                  {{ capability }}
                </el-tag>
                <span v-if="mcp.capabilities?.length > 3" class="more-capabilities">
                  +{{ mcp.capabilities.length - 3 }}个能力
                </span>
              </div>
            </div>
            <div class="assignment-control">
              <el-checkbox
                :model-value="isAssigned(mcp.id)"
                @change="toggleAssignment(mcp)"
                :disabled="!mcp.isActive"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="assigned-mcps">
        <h5>已分配的MCP服务 ({{ assignedMCPsList.length }})</h5>
        <div v-if="assignedMCPsList.length === 0" class="empty-state">
          <el-empty description="暂未分配任何MCP服务" :image-size="80" />
        </div>
        <div v-else class="assigned-list">
          <div
            v-for="mcp in assignedMCPsList"
            :key="mcp.id"
            class="assigned-item"
          >
            <div class="assigned-info">
              <span class="assigned-name">{{ mcp.name }}</span>
              <el-tag size="small" type="success">已分配</el-tag>
            </div>
            <el-button
              type="text"
              size="small"
              @click="removeAssignment(mcp.id)"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="assignment-footer">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button type="primary" @click="saveAssignments">保存配置</el-button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { Close } from '@element-plus/icons-vue'

export default {
  name: 'MCPAssignment',
  components: {
    Close
  },
  props: {
    agentId: {
      type: String,
      required: true
    },
    assignedMcps: {
      type: Array,
      default: () => []
    },
    availableMcps: {
      type: Array,
      default: () => []
    }
  },
  emits: ['save', 'cancel'],
  setup(props, { emit }) {
    const currentAssignments = ref(new Set())
    
    const assignedMCPsList = computed(() => {
      return props.availableMcps.filter(mcp => currentAssignments.value.has(mcp.id))
    })
    
    const isAssigned = (mcpId) => {
      return currentAssignments.value.has(mcpId)
    }
    
    const toggleAssignment = (mcp) => {
      if (!mcp.isActive) return
      
      if (currentAssignments.value.has(mcp.id)) {
        currentAssignments.value.delete(mcp.id)
      } else {
        currentAssignments.value.add(mcp.id)
      }
    }
    
    const removeAssignment = (mcpId) => {
      currentAssignments.value.delete(mcpId)
    }
    
    const saveAssignments = () => {
      const assignments = Array.from(currentAssignments.value).map(mcpId => {
        const mcp = props.availableMcps.find(m => m.id === mcpId)
        return {
          mcpId,
          mcpName: mcp?.name,
          capabilities: mcp?.capabilities || []
        }
      })
      emit('save', assignments)
    }
    
    onMounted(() => {
      // 初始化已分配的MCP服务
      props.assignedMcps.forEach(mcp => {
        currentAssignments.value.add(mcp.mcpId || mcp.id)
      })
    })
    
    return {
      currentAssignments,
      assignedMCPsList,
      isAssigned,
      toggleAssignment,
      removeAssignment,
      saveAssignments
    }
  }
}
</script>

<style scoped>
.mcp-assignment {
  padding: 20px;
}

.assignment-header {
  margin-bottom: 20px;
}

.assignment-header h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.description {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.assignment-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.available-mcps h5,
.assigned-mcps h5 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.mcp-list {
  max-height: 400px;
  overflow-y: auto;
}

.mcp-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mcp-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.mcp-item.assigned {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.mcp-info {
  flex: 1;
}

.mcp-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.mcp-name {
  font-weight: 500;
  color: #333;
}

.mcp-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.mcp-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-capabilities {
  font-size: 12px;
  color: #999;
}

.assignment-control {
  margin-left: 15px;
}

.assigned-list {
  max-height: 400px;
  overflow-y: auto;
}

.assigned-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
}

.assigned-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.assigned-name {
  font-weight: 500;
  color: #333;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.assignment-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
```

#### 5.2.7 结果展示组件
```vue
<template>
  <div class="result-display">
    <el-tabs v-model="activeTab">
      <!-- Markdown结果 -->
      <el-tab-pane label="报告" name="markdown" v-if="hasMarkdown">
        <div class="markdown-content" v-html="renderedMarkdown"></div>
      </el-tab-pane>
      
      <!-- 代码结果 -->
      <el-tab-pane label="代码" name="code" v-if="hasCode">
        <div v-for="(code, index) in codeResults" :key="index">
          <h4>{{ code.filename }}</h4>
          <pre><code :class="code.language">{{ code.content }}</code></pre>
        </div>
      </el-tab-pane>
      
      <!-- 图片结果 -->
      <el-tab-pane label="图片" name="images" v-if="hasImages">
        <el-image
          v-for="(image, index) in imageResults"
          :key="index"
          :src="image.url"
          :preview-src-list="imageUrls"
          fit="contain"
        />
      </el-tab-pane>
      
      <!-- 流程图 -->
      <el-tab-pane label="流程图" name="flowchart" v-if="hasFlowchart">
        <div ref="flowchartContainer" class="flowchart-container"></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

### 5.3 状态管理

使用 Pinia 进行状态管理：

#### 5.3.1 主状态管理
```javascript
// stores/main.js
import { defineStore } from 'pinia'

export const useMainStore = defineStore('main', {
  state: () => ({
    userInput: '',
    currentTask: null,
    taskHistory: [],
    isProcessing: false,
    currentView: 'home' // 当前显示的页面
  }),
  
  actions: {
    async submitTask(input) {
      this.isProcessing = true
      try {
        const response = await fetch('/api/tasks', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ input })
        })
        const task = await response.json()
        this.currentTask = task
        this.taskHistory.push(task)
      } finally {
        this.isProcessing = false
      }
    },
    
    setCurrentView(view) {
      this.currentView = view
    }
  }
})
```

#### 5.3.2 MCP状态管理
```javascript
// stores/mcp.js
import { defineStore } from 'pinia'
import { mcpApi } from '@/utils/mcpApi'

export const useMCPStore = defineStore('mcp', {
  state: () => ({
    servers: [],
    loading: false,
    error: null,
    testResults: new Map() // 存储测试结果
  }),
  
  getters: {
    activeServers: (state) => state.servers.filter(s => s.isActive),
    inactiveServers: (state) => state.servers.filter(s => !s.isActive),
    getServerById: (state) => (id) => state.servers.find(s => s.id === id)
  },
  
  actions: {
    async fetchServers() {
      this.loading = true
      this.error = null
      try {
        const response = await mcpApi.getServers()
        this.servers = response.data
      } catch (error) {
        this.error = error.message
        console.error('获取MCP服务器列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async addServer(serverData) {
      this.loading = true
      try {
        const response = await mcpApi.addServer(serverData)
        this.servers.push(response.data)
        return response.data
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async updateServer(id, serverData) {
      this.loading = true
      try {
        const response = await mcpApi.updateServer(id, serverData)
        const index = this.servers.findIndex(s => s.id === id)
        if (index !== -1) {
          this.servers[index] = response.data
        }
        return response.data
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async deleteServer(id) {
      this.loading = true
      try {
        await mcpApi.deleteServer(id)
        this.servers = this.servers.filter(s => s.id !== id)
        this.testResults.delete(id)
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async toggleServer(id, isActive) {
      try {
        const server = this.getServerById(id)
        if (server) {
          server.isActive = isActive
          await this.updateServer(id, server)
        }
      } catch (error) {
        this.error = error.message
        throw error
      }
    },
    
    async testServer(id) {
      try {
        const response = await mcpApi.testServer(id)
        this.testResults.set(id, {
          success: response.success,
          message: response.message,
          timestamp: new Date()
        })
        return response
      } catch (error) {
        this.testResults.set(id, {
          success: false,
          message: error.message,
          timestamp: new Date()
        })
        throw error
      }
    },
    
    getTestResult(id) {
      return this.testResults.get(id)
    },
    
    clearError() {
      this.error = null
    }
  }
})
```

#### 5.3.3 智能体状态管理
```javascript
// stores/agents.js
import { defineStore } from 'pinia'
import { agentsApi } from '@/utils/agentsApi'

export const useAgentsStore = defineStore('agents', {
  state: () => ({
    agents: [],
    loading: false,
    error: null,
    currentAgent: null,
    agentMCPAssignments: new Map() // 存储智能体的MCP分配
  }),
  
  getters: {
    activeAgents: (state) => state.agents.filter(a => a.isActive),
    inactiveAgents: (state) => state.agents.filter(a => !a.isActive),
    getAgentById: (state) => (id) => state.agents.find(a => a.id === id),
    getAgentMCPs: (state) => (agentId) => state.agentMCPAssignments.get(agentId) || []
  },
  
  actions: {
    async fetchAgents() {
      this.loading = true
      this.error = null
      try {
        const response = await agentsApi.getAgents()
        this.agents = response.data
        // 同时获取每个智能体的MCP分配
        for (const agent of this.agents) {
          await this.fetchAgentMCPs(agent.id)
        }
      } catch (error) {
        this.error = error.message
        console.error('获取智能体列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    async addAgent(agentData) {
      this.loading = true
      try {
        const response = await agentsApi.addAgent(agentData)
        this.agents.push(response.data)
        return response.data
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async updateAgent(id, agentData) {
      this.loading = true
      try {
        const response = await agentsApi.updateAgent(id, agentData)
        const index = this.agents.findIndex(a => a.id === id)
        if (index !== -1) {
          this.agents[index] = response.data
        }
        return response.data
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async deleteAgent(id) {
      this.loading = true
      try {
        await agentsApi.deleteAgent(id)
        this.agents = this.agents.filter(a => a.id !== id)
        this.agentMCPAssignments.delete(id)
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    async toggleAgent(id, isActive) {
      try {
        const agent = this.getAgentById(id)
        if (agent) {
          agent.isActive = isActive
          await this.updateAgent(id, agent)
        }
      } catch (error) {
        this.error = error.message
        throw error
      }
    },
    
    async fetchAgentMCPs(agentId) {
      try {
        const response = await agentsApi.getAgentMCPs(agentId)
        this.agentMCPAssignments.set(agentId, response.data)
        return response.data
      } catch (error) {
        console.error(`获取智能体 ${agentId} 的MCP分配失败:`, error)
        return []
      }
    },
    
    async assignMCPsToAgent(agentId, mcpAssignments) {
      this.loading = true
      try {
        const response = await agentsApi.assignMCPs(agentId, mcpAssignments)
        this.agentMCPAssignments.set(agentId, response.data)
        
        // 更新智能体的assignedMCPs字段
        const agent = this.getAgentById(agentId)
        if (agent) {
          agent.assignedMCPs = response.data
        }
        
        return response.data
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    setCurrentAgent(agent) {
      this.currentAgent = agent
    },
    
    clearError() {
      this.error = null
    }
  }
})
```

### 5.4 API工具函数

#### 5.4.1 MCP API工具
```javascript
// utils/mcpApi.js
const API_BASE = '/api/mcp'

export const mcpApi = {
  // 获取所有MCP服务器
  async getServers() {
    const response = await fetch(`${API_BASE}/servers`)
    if (!response.ok) {
      throw new Error('获取MCP服务器列表失败')
    }
    return response.json()
  },
  
  // 添加MCP服务器
  async addServer(serverData) {
    const response = await fetch(`${API_BASE}/servers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(serverData)
    })
    if (!response.ok) {
      throw new Error('添加MCP服务器失败')
    }
    return response.json()
  },
  
  // 更新MCP服务器
  async updateServer(id, serverData) {
    const response = await fetch(`${API_BASE}/servers/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(serverData)
    })
    if (!response.ok) {
      throw new Error('更新MCP服务器失败')
    }
    return response.json()
  },
  
  // 删除MCP服务器
  async deleteServer(id) {
    const response = await fetch(`${API_BASE}/servers/${id}`, {
      method: 'DELETE'
    })
    if (!response.ok) {
      throw new Error('删除MCP服务器失败')
    }
    return response.json()
  },
  
  // 测试MCP服务器连接
  async testServer(id) {
    const response = await fetch(`${API_BASE}/servers/${id}/test`, {
      method: 'POST'
    })
    if (!response.ok) {
      throw new Error('测试MCP服务器失败')
    }
    return response.json()
  },
  
  // 调用MCP服务器功能
  async callServer(id, tool, args) {
    const response = await fetch(`${API_BASE}/servers/${id}/call`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ tool, args })
    })
    if (!response.ok) {
      throw new Error('调用MCP服务器失败')
    }
    return response.json()
  }
}
```

#### 5.4.2 智能体API工具
```javascript
// utils/agentsApi.js
const API_BASE = '/api/agents'

export const agentsApi = {
  // 获取所有智能体
  async getAgents() {
    const response = await fetch(`${API_BASE}`)
    if (!response.ok) {
      throw new Error('获取智能体列表失败')
    }
    return response.json()
  },
  
  // 添加智能体
  async addAgent(agentData) {
    const response = await fetch(`${API_BASE}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(agentData)
    })
    if (!response.ok) {
      throw new Error('添加智能体失败')
    }
    return response.json()
  },
  
  // 更新智能体
  async updateAgent(id, agentData) {
    const response = await fetch(`${API_BASE}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(agentData)
    })
    if (!response.ok) {
      throw new Error('更新智能体失败')
    }
    return response.json()
  },
  
  // 删除智能体
  async deleteAgent(id) {
    const response = await fetch(`${API_BASE}/${id}`, {
      method: 'DELETE'
    })
    if (!response.ok) {
      throw new Error('删除智能体失败')
    }
    return response.json()
  },
  
  // 获取智能体的MCP分配
  async getAgentMCPs(agentId) {
    const response = await fetch(`${API_BASE}/${agentId}/mcps`)
    if (!response.ok) {
      throw new Error('获取智能体MCP分配失败')
    }
    return response.json()
  },
  
  // 为智能体分配MCP服务
  async assignMCPs(agentId, mcpAssignments) {
    const response = await fetch(`${API_BASE}/${agentId}/mcps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ assignments: mcpAssignments })
    })
    if (!response.ok) {
      throw new Error('分配MCP服务失败')
    }
    return response.json()
  }
}
```

## 6. 部署方案

### 6.1 开发环境

#### 6.1.1 环境要求
- Python 3.9+
- Node.js 16+
- MySQL 5.7+
- Docker (可选)

#### 6.1.2 安装步骤

```bash
# 1. 克隆项目
git clone https://github.com/your-org/a2a-demo.git
cd a2a-demo

# 2. 后端环境设置
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 3. 前端环境设置
cd ../frontend
npm install

# 4. 配置环境变量
cp .env.example .env
# 编辑.env文件，填入API密钥等配置

# 5. 数据库初始化
# 创建数据库（需要先启动MySQL服务）
mysql -u root -p
CREATE DATABASE a2a CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# 6. 启动服务
# 后端
cd backend
uvicorn main:app --reload --port 8000

# 前端
cd frontend
npm run dev
```

#### 6.1.3 配置文件

```bash
# .env文件示例
# OpenAI和兼容模型API密钥
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key
QWEN_API_KEY=your_qwen_api_key                    # 阿里千问
DEEPSEEK_API_KEY=your_deepseek_api_key            # 深度求索
MOONSHOT_API_KEY=your_moonshot_api_key            # 月之暗面
ZHIPU_API_KEY=your_zhipu_api_key                  # 智谱AI

# MCP服务API密钥
FILE_OPS_API_KEY=your_file_ops_key
SEARCH_API_TOKEN=your_search_token
DATA_ANALYSIS_API_KEY=your_data_analysis_key
DATA_PROCESSING_API_KEY=your_data_processing_key
CHART_API_KEY=your_chart_generation_key
MAP_API_KEY=your_map_services_key
GIT_TOKEN=your_git_token

# 数据库配置
DATABASE_URL=mysql://root:root@127.0.0.1:3306/a2a
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=a2a

# MCP服务配置
MCP_FILE_OPS_ENDPOINT=mcp://localhost:8001/file-ops
MCP_SEARCH_ENDPOINT=mcp://localhost:8002/web-search

# 本地MCP服务器环境变量
BRAVE_API_KEY=your_brave_search_api_key           # Brave搜索API密钥
MCP_FILESYSTEM_PATH=D:/Box/Prompt                 # 文件系统MCP服务工作目录
MCP_SQLITE_DB_PATH=./data/app.db                  # SQLite数据库路径

# MCP服务器运行时配置
MCP_TIMEOUT=30                                    # MCP服务调用超时时间（秒）
MCP_MAX_RETRIES=3                                 # MCP服务调用最大重试次数
MCP_LOG_LEVEL=INFO                                # MCP服务日志级别

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/a2a_demo.log
```

### 6.2 生产环境

#### 6.2.1 Docker部署

```dockerfile
# Dockerfile.backend
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Dockerfile.frontend
FROM node:16-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://root:root@db:3306/a2a
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend

  db:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=a2a
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

## 7. 项目结构

```
a2a-demo/
├── README.md
├── docs/
│   ├── design_document.md         # 本设计文档
│   ├── api_documentation.md       # API文档
│   ├── user_guide.md             # 用户指南
│   └── deployment_guide.md       # 部署指南
├── backend/
│   ├── main.py                   # FastAPI应用入口
│   ├── requirements.txt          # Python依赖
│   ├── alembic.ini              # 数据库迁移配置
│   ├── alembic/                 # 数据库迁移脚本
│   ├── config/
│   │   ├── agents.yaml           # 智能体配置
│   │   ├── workflows.yaml        # 工作流配置
│   │   ├── mcp_services.yaml     # 远程MCP服务配置
│   │   └── local_mcp_servers.json # 本地MCP服务器配置
│   ├── src/
│   │   ├── agents/               # 智能体模块
│   │   ├── a2a/                  # A2A协议实现
│   │   ├── workflow/             # 工作流引擎
│   │   ├── mcp/                  # MCP集成
│   │   ├── api/                  # API路由
│   │   └── utils/                # 工具函数
│   ├── tests/                    # 测试文件
│   └── logs/                     # 日志文件
├── frontend/
│   ├── package.json              # Node.js依赖
│   ├── vite.config.js            # Vite配置
│   ├── src/
│   │   ├── main.js               # 应用入口
│   │   ├── App.vue               # 根组件
│   │   ├── components/           # Vue组件
│   │   ├── views/                # 页面组件
│   │   ├── stores/               # 状态管理
│   │   └── utils/                # 工具函数
│   └── public/                   # 静态资源
├── docker-compose.yml            # Docker编排
├── .env.example                  # 环境变量示例
└── .gitignore                    # Git忽略文件
```

## 8. 开发计划

### 8.1 第一阶段（基础框架）
- [ ] 项目结构搭建
- [ ] 数据库设计和初始化
- [ ] 基础配置管理
- [ ] 简单的智能体实现
- [ ] FastAPI基础API
- [ ] 前端基础界面

### 8.2 第二阶段（核心功能）
- [ ] A2A协议实现
- [ ] 工作流引擎
- [ ] 多智能体协作
- [ ] 任务分解和执行
- [ ] 实时状态更新

### 8.3 第三阶段（高级功能）
- [ ] MCP服务集成
- [ ] 本地MCP服务器配置功能
- [ ] stdio方式MCP服务调用
- [ ] MCP服务器进程管理
- [ ] MCP配置管理API
- [ ] 复杂结果展示
- [ ] 工作流设计器
- [ ] 性能优化
- [ ] 错误处理和恢复

### 8.4 第四阶段（完善和部署）
- [ ] 完整测试覆盖
- [ ] 文档完善
- [ ] 部署脚本
- [ ] 监控和日志
- [ ] 安全加固

## 9. 数据库说明

### 9.1 数据库的必要性

**是的，本系统必须需要数据库支持**，主要原因如下：

1. **会话状态管理**: 需要持久化存储用户会话信息，包括工作流执行状态、输入输出数据等
2. **任务跟踪**: 多智能体协作过程中需要跟踪每个任务的执行状态和结果
3. **消息历史**: A2A协议中智能体间的通信消息需要记录，便于调试和审计
4. **执行日志**: 工作流执行过程中的详细日志需要持久化存储
5. **结果缓存**: 复杂任务的执行结果需要缓存，避免重复计算

### 9.2 MySQL配置说明

根据您的要求，系统已配置为使用MySQL数据库：

- **数据库名**: a2a
- **用户名**: root
- **密码**: root
- **主机**: 127.0.0.1
- **端口**: 3306

### 9.3 Python依赖包

使用MySQL和多种LLM提供商需要安装以下Python包：

```txt
# 数据库相关
mysqlclient==2.2.0          # MySQL数据库驱动
sqlalchemy==2.0.23          # ORM框架
alembic==1.12.1             # 数据库迁移工具
pymysql==1.1.0              # 纯Python MySQL客户端（备选）

# FastAPI和Web相关
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
websockets==12.0

# 异步和任务队列
celery==5.3.4
redis==5.0.1

# AI和LLM相关（OpenAI兼容）
openai==1.3.0               # OpenAI官方SDK，也支持其他兼容API
anthropic==0.7.0            # Anthropic Claude
google-generativeai==0.3.0  # Google Gemini

# 注意：以下提供商使用OpenAI兼容API，无需额外SDK
# - 阿里千问 (qwen): 使用openai SDK + 自定义base_url
# - 深度求索 (deepseek): 使用openai SDK + 自定义base_url  
# - 月之暗面 (moonshot): 使用openai SDK + 自定义base_url
# - 智谱AI (zhipu): 使用openai SDK + 自定义base_url

# MCP协议和服务集成
mcp-client==0.1.0           # MCP客户端库
requests==2.31.0            # HTTP请求
httpx==0.25.0               # 异步HTTP客户端

# 本地MCP服务器支持
psutil==5.9.6               # 进程管理，用于MCP服务器进程监控
aiofiles==23.2.1            # 异步文件操作
watchdog==3.0.0             # 文件系统监控
jsonschema==4.20.0          # JSON模式验证

# 数据处理和分析
pandas==2.1.0               # 数据分析
numpy==1.24.0               # 数值计算
matplotlib==3.7.0           # 图表生成
seaborn==0.12.0             # 统计图表

# 其他工具
pyyaml==6.0.1               # YAML配置文件
jinja2==3.1.0               # 模板引擎
python-dotenv==1.0.0        # 环境变量管理
loguru==0.7.0               # 日志管理
```

### 9.4 数据库初始化脚本

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS a2a CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE a2a;

-- 创建所有表（参考3.4.1节的表结构）
-- 这些表会通过Alembic迁移工具自动创建
```

### 9.5 数据库连接配置

```python
# database.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import os

# 数据库连接URL
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "mysql://root:root@127.0.0.1:3306/a2a?charset=utf8mb4"
)

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False  # 生产环境设为False
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 基础模型类
Base = declarative_base()

# 依赖注入函数
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

## 10. 技术难点和解决方案

### 10.1 A2A协议实现
**难点**: A2A协议的具体实现细节
**解决方案**: 
- 参考Google A2A开源实现
- 实现基础的消息路由和智能体发现
- 逐步完善协议功能

### 10.2 异步任务管理
**难点**: 长时间运行的任务管理和状态跟踪
**解决方案**:
- 使用Celery或类似的任务队列
- 实现任务状态持久化
- WebSocket实时状态推送

### 10.3 MCP服务集成
**难点**: MCP协议的实现和服务发现
**解决方案**:
- 研究MCP协议规范
- 实现基础的MCP客户端
- 提供常用服务的适配器

### 10.4 前端复杂结果展示
**难点**: 多种格式结果的统一展示
**解决方案**:
- 使用组件化设计
- 集成Markdown渲染器
- 支持代码高亮和图片展示
- 集成流程图库（如Mermaid）

## 10. 本地MCP配置功能详细说明

### 10.1 功能概述

本地MCP配置功能允许用户通过JSON配置文件定义和管理本地MCP服务器，支持通过stdio方式与这些服务器进行通信。这种方式特别适合：

- 本地开发和测试
- 使用npm或pip安装的MCP服务器
- 需要特定环境变量的MCP服务
- 文件系统操作等本地服务

### 10.2 配置文件格式

本地MCP服务器配置文件 `local_mcp_servers.json` 的标准格式：

```json
{
  "mcpServers": {
    "ZuvNebUft3E1S4xUWwSgH": {
      "isActive": true,
      "name": "mcp-deepwiki",
      "description": "深度维基百科搜索服务",
      "baseUrl": "",
      "command": "npx",
      "args": [
        "-y",
        "mcp-deepwiki@latest"
      ],
      "env": {},
      "type": "stdio"
    },
    "fetch": {
      "isActive": true,
      "name": "fetch",
      "description": "网络内容获取服务",
      "type": "stdio",
      "registryUrl": "",
      "command": "uvx",
      "args": [
        "mcp-server-fetch"
      ],
      "env": {}
    },
    "filesystem": {
      "isActive": true,
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "D:/Box/Prompt"
      ],
      "name": "filesystem",
      "description": "本地文件系统操作服务",
      "env": {}
    }
  }
}
```

### 10.3 配置字段说明

| 字段 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `isActive` | boolean | 是 | 是否启用该MCP服务器 |
| `name` | string | 是 | 服务器显示名称 |
| `description` | string | 否 | 服务器描述信息 |
| `command` | string | 是 | 启动命令（如npx、uvx、python等） |
| `args` | array | 是 | 命令参数列表 |
| `env` | object | 否 | 环境变量键值对 |
| `type` | string | 否 | 通信类型，默认为"stdio" |
| `baseUrl` | string | 否 | 基础URL（HTTP类型时使用） |
| `registryUrl` | string | 否 | 注册表URL |

### 10.4 API使用示例

#### 10.4.1 获取所有MCP服务器
```bash
curl -X GET "http://localhost:8000/api/v1/mcp/servers"
```

#### 10.4.2 更新MCP服务器配置
```bash
curl -X POST "http://localhost:8000/api/v1/mcp/servers" \
  -H "Content-Type: application/json" \
  -d @local_mcp_servers.json
```

#### 10.4.3 测试MCP服务器连接
```bash
curl -X POST "http://localhost:8000/api/v1/mcp/servers/filesystem/test"
```

#### 10.4.4 调用MCP服务器功能
```bash
curl -X POST "http://localhost:8000/api/v1/mcp/servers/filesystem/call" \
  -H "Content-Type: application/json" \
  -d '{
    "capability": "read_file",
    "parameters": {
      "path": "./example.txt"
    }
  }'
```

### 10.5 最佳实践

1. **环境变量管理**: 敏感信息（如API密钥）应通过环境变量传递
2. **路径配置**: 使用相对路径或环境变量，避免硬编码绝对路径
3. **服务器命名**: 使用描述性的名称，便于识别和管理
4. **错误处理**: 配置合理的超时时间和重试机制
5. **日志记录**: 启用详细日志以便调试和监控

### 10.6 安全注意事项

1. **文件系统访问**: 限制文件系统MCP服务器的访问范围
2. **环境变量**: 不要在配置文件中硬编码敏感信息
3. **网络访问**: 谨慎配置网络相关的MCP服务器
4. **进程管理**: 确保MCP服务器进程的正确启动和关闭

通过本地MCP配置功能，A2A系统能够灵活地集成各种本地服务，为智能体提供丰富的工具和能力支持。

## 11. 工作流设计器实现

### 11.1 工作流设计器组件

为了支持循环、分支和长上下文管理功能，前端工作流设计器需要进行相应的增强：

```vue
<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button-group>
        <el-button @click="addStep('agent')" icon="User">添加智能体</el-button>
        <el-button @click="addStep('branch')" icon="Share">添加分支</el-button>
        <el-button @click="addStep('loop')" icon="Refresh">添加循环</el-button>
        <el-button @click="addStep('validation')" icon="CircleCheck">添加验证</el-button>
      </el-button-group>
      <el-button-group class="ml-4">
        <el-button @click="saveWorkflow" type="primary" icon="Document">保存工作流</el-button>
        <el-button @click="validateWorkflow" icon="CircleCheck">验证工作流</el-button>
        <el-button @click="previewWorkflow" icon="View">预览执行</el-button>
      </el-button-group>
    </div>

    <!-- 工作流画布 -->
    <div class="workflow-canvas" ref="canvasContainer">
      <div class="workflow-steps">
        <draggable 
          v-model="workflowSteps" 
          group="steps" 
          @change="onStepsChange"
          item-key="id"
        >
          <template #item="{element, index}">
            <div 
              class="workflow-step" 
              :class="{
                'step-agent': element.type === 'agent',
                'step-branch': element.type === 'branch',
                'step-loop': element.type === 'loop',
                'step-validation': element.type === 'validation',
                'step-selected': selectedStep?.id === element.id
              }"
              @click="selectStep(element)"
            >
              <!-- 步骤图标 -->
              <div class="step-icon">
                <el-icon v-if="element.type === 'agent'"><User /></el-icon>
                <el-icon v-else-if="element.type === 'branch'"><Share /></el-icon>
                <el-icon v-else-if="element.type === 'loop'"><Refresh /></el-icon>
                <el-icon v-else-if="element.type === 'validation'"><CircleCheck /></el-icon>
              </div>
              
              <!-- 步骤信息 -->
              <div class="step-info">
                <div class="step-title">{{ element.name || getStepTypeName(element.type) }}</div>
                <div class="step-subtitle">{{ element.description || element.agent }}</div>
              </div>
              
              <!-- 步骤操作 -->
              <div class="step-actions">
                <el-button size="small" @click.stop="editStep(element)" icon="Edit" circle />
                <el-button size="small" @click.stop="deleteStep(index)" icon="Delete" circle type="danger" />
              </div>
              
              <!-- 连接线 -->
              <div v-if="index < workflowSteps.length - 1" class="step-connector">
                <div class="connector-line"></div>
                <el-icon class="connector-arrow"><ArrowDown /></el-icon>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>

    <!-- 属性面板 -->
    <div class="properties-panel" v-if="selectedStep">
      <el-card>
        <template #header>
          <span>{{ getStepTypeName(selectedStep.type) }}配置</span>
        </template>
        
        <!-- 通用属性 -->
        <el-form :model="selectedStep" label-width="120px">
          <el-form-item label="步骤ID">
            <el-input v-model="selectedStep.step_id" placeholder="唯一标识符" />
          </el-form-item>
          
          <el-form-item label="步骤名称">
            <el-input v-model="selectedStep.name" placeholder="步骤显示名称" />
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input v-model="selectedStep.description" type="textarea" placeholder="步骤描述" />
          </el-form-item>
          
          <!-- 智能体步骤特有属性 -->
          <template v-if="selectedStep.type === 'agent'">
            <el-form-item label="智能体">
              <el-select v-model="selectedStep.agent" placeholder="选择智能体">
                <el-option 
                  v-for="agent in availableAgents" 
                  :key="agent.id" 
                  :label="agent.name" 
                  :value="agent.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="输入映射">
              <JsonEditor v-model="selectedStep.input_mapping" />
            </el-form-item>
            
            <el-form-item label="执行模式">
              <el-radio-group v-model="selectedStep.execution_mode">
                <el-radio label="sequential">顺序执行</el-radio>
                <el-radio label="parallel">并发执行</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="并发配置" v-if="selectedStep.execution_mode === 'parallel'">
              <el-form-item label="并发智能体列表">
                <ParallelAgentsEditor v-model="selectedStep.parallel_agents" :available-agents="availableAgents" />
              </el-form-item>
              
              <el-form-item label="聚合策略">
                <el-select v-model="selectedStep.aggregation_strategy" placeholder="选择结果聚合策略">
                  <el-option label="合并所有结果" value="merge_all" />
                  <el-option label="选择最佳结果" value="select_best" />
                  <el-option label="投票决策" value="voting" />
                  <el-option label="加权平均" value="weighted_average" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="超时设置">
                <el-input-number v-model="selectedStep.parallel_timeout" :min="10" :max="300" placeholder="并发执行超时(秒)" />
              </el-form-item>
            </el-form-item>
            
            <el-form-item label="上下文管理">
              <el-switch v-model="selectedStep.enable_context_compression" active-text="启用压缩" />
              <el-input-number 
                v-model="selectedStep.max_context_tokens" 
                :min="1000" 
                :max="200000" 
                placeholder="最大上下文令牌数"
                class="ml-2"
              />
            </el-form-item>
          </template>
          
          <!-- 分支步骤特有属性 -->
          <template v-if="selectedStep.type === 'branch'">
            <el-form-item label="分支条件">
              <el-input v-model="selectedStep.condition" placeholder="如: validation_result.is_valid" />
            </el-form-item>
            
            <el-form-item label="分支配置">
              <BranchEditor v-model="selectedStep.branches" />
            </el-form-item>
          </template>
          
          <!-- 循环步骤特有属性 -->
          <template v-if="selectedStep.type === 'loop'">
            <el-form-item label="循环条件">
              <el-input v-model="selectedStep.condition" placeholder="如: current_iteration < max_iterations" />
            </el-form-item>
            
            <el-form-item label="跳转目标">
              <el-select v-model="selectedStep.loop_target" placeholder="选择跳转步骤">
                <el-option 
                  v-for="step in workflowSteps" 
                  :key="step.step_id" 
                  :label="step.name || step.step_id" 
                  :value="step.step_id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="最大循环次数">
              <el-input-number v-model="selectedStep.max_iterations" :min="1" :max="10" />
            </el-form-item>
            
            <el-form-item label="输入覆盖">
              <JsonEditor v-model="selectedStep.input_override" />
            </el-form-item>
          </template>
          
          <!-- 验证步骤特有属性 -->
          <template v-if="selectedStep.type === 'validation'">
            <el-form-item label="验证智能体">
              <el-select v-model="selectedStep.agent" placeholder="选择验证智能体">
                <el-option 
                  v-for="agent in validationAgents" 
                  :key="agent.id" 
                  :label="agent.name" 
                  :value="agent.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="验证阈值">
              <el-slider v-model="selectedStep.validation_threshold" :min="0" :max="1" :step="0.1" show-input />
            </el-form-item>
          </template>
          
          <!-- 执行条件 -->
          <el-form-item label="执行条件">
            <el-input v-model="selectedStep.condition" placeholder="可选的执行条件" />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 工作流配置对话框 -->
    <el-dialog v-model="showWorkflowConfig" title="工作流配置" width="600px">
      <el-form :model="workflowConfig" label-width="120px">
        <el-form-item label="工作流名称">
          <el-input v-model="workflowConfig.name" />
        </el-form-item>
        
        <el-form-item label="工作流描述">
          <el-input v-model="workflowConfig.description" type="textarea" />
        </el-form-item>
        
        <el-form-item label="最大循环次数">
          <el-input-number v-model="workflowConfig.max_iterations" :min="1" :max="10" />
        </el-form-item>
        
        <el-form-item label="上下文管理">
          <el-switch v-model="workflowConfig.enable_context_management" active-text="启用" />
        </el-form-item>
        
        <el-form-item label="上下文压缩阈值">
          <el-input-number 
            v-model="workflowConfig.context_compression_threshold" 
            :min="10000" 
            :max="150000"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showWorkflowConfig = false">取消</el-button>
        <el-button type="primary" @click="saveWorkflowConfig">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import draggable from 'vuedraggable'

// 并发智能体编辑器组件
const ParallelAgentsEditor = {
  props: {
    modelValue: { type: Array, default: () => [] },
    availableAgents: { type: Array, default: () => [] }
  },
  emits: ['update:modelValue'],
  template: `
    <div class="parallel-agents-editor">
      <div v-for="(agentConfig, index) in localValue" :key="index" class="agent-config-item">
        <el-card>
          <template #header>
            <div class="agent-config-header">
              <span>智能体 {{ index + 1 }}</span>
              <el-button size="small" @click="removeAgent(index)" icon="Delete" circle type="danger" />
            </div>
          </template>
          
          <el-form :model="agentConfig" label-width="100px">
            <el-form-item label="智能体">
              <el-select v-model="agentConfig.agent" placeholder="选择智能体">
                <el-option v-for="agent in availableAgents" :key="agent.id" :label="agent.name" :value="agent.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="权重">
              <el-input-number v-model="agentConfig.weight" :min="0.1" :max="1" :step="0.1" />
            </el-form-item>
          </el-form>
        </el-card>
      </div>
      <el-button @click="addAgent" icon="Plus" type="primary" class="add-agent-btn">添加并发智能体</el-button>
    </div>
  `,
  setup(props, { emit }) {
    const localValue = ref([...props.modelValue])
    
    const addAgent = () => {
      localValue.value.push({ agent: '', weight: 1.0, input_mapping: {} })
      emit('update:modelValue', localValue.value)
    }
    
    const removeAgent = (index) => {
      localValue.value.splice(index, 1)
      emit('update:modelValue', localValue.value)
    }
    
    return { localValue, addAgent, removeAgent }
  }
}

export default {
  name: 'WorkflowDesigner',
  components: { draggable, ParallelAgentsEditor },
  setup() {
    const workflowSteps = ref([])
    const selectedStep = ref(null)
    const availableAgents = ref([])
    
    const addStep = (type) => {
      const newStep = {
        id: `step_${Date.now()}`,
        type,
        execution_mode: 'sequential',
        parallel_agents: [],
        aggregation_strategy: 'merge_all',
        parallel_timeout: 60
      }
      workflowSteps.value.push(newStep)
      selectedStep.value = newStep
    }
    
    const validateWorkflow = () => {
      const errors = []
      workflowSteps.value.forEach((step, index) => {
        if (step.execution_mode === 'parallel' && (!step.parallel_agents || step.parallel_agents.length === 0)) {
          errors.push(`步骤 ${index + 1}: 并发模式下未配置并发智能体`)
        }
      })
      
      if (errors.length > 0) {
        ElMessage.error('工作流验证失败：\n' + errors.join('\n'))
      } else {
        ElMessage.success('工作流验证通过')
      }
    }
    
    return { workflowSteps, selectedStep, availableAgents, addStep, validateWorkflow }
  }
}
</script>

<style scoped>
.workflow-step { background: white; border: 2px solid #e4e7ed; border-radius: 8px; padding: 16px; margin-bottom: 16px; }
.workflow-step.step-selected { border-color: #409eff; background: #f0f9ff; }
.step-agent { border-left: 4px solid #67c23a; }
.step-branch { border-left: 4px solid #e6a23c; }
.step-loop { border-left: 4px solid #f56c6c; }
.step-validation { border-left: 4px solid #909399; }
.parallel-agents-editor { border: 1px solid #e4e7ed; border-radius: 4px; padding: 12px; }
.agent-config-header { display: flex; justify-content: space-between; align-items: center; }
.add-agent-btn { width: 100%; }
</style>
```

### 11.2 上下文管理模块

为了处理长上下文问题，实现了专门的上下文管理器：

```python
import tiktoken
from typing import Dict, List, Any, Optional
from transformers import AutoTokenizer

class ContextManager:
    """上下文管理器，处理长上下文压缩和令牌计数"""
    
    def __init__(self):
        self.tokenizers = {}
        self.compression_strategies = {
            'summary': self._summarize_context,
            'sliding_window': self._sliding_window_context,
            'key_extraction': self._extract_key_context
        }
    
    def get_tokenizer(self, model_name: str):
        """获取对应模型的分词器"""
        if model_name not in self.tokenizers:
            try:
                if 'gpt' in model_name.lower():
                    self.tokenizers[model_name] = tiktoken.encoding_for_model(model_name)
                else:
                    self.tokenizers[model_name] = AutoTokenizer.from_pretrained(model_name)
            except Exception:
                # 使用默认分词器
                self.tokenizers[model_name] = tiktoken.get_encoding("cl100k_base")
        
        return self.tokenizers[model_name]
    
    def count_tokens(self, text: str, model_name: str) -> int:
        """计算文本的令牌数量"""
        tokenizer = self.get_tokenizer(model_name)
        
        if hasattr(tokenizer, 'encode'):
            return len(tokenizer.encode(text))
        else:
            return len(tokenizer.tokenize(text))
    
    def should_compress_context(self, context: Dict[str, Any], 
                              max_tokens: int, model_name: str) -> bool:
        """判断是否需要压缩上下文"""
        total_text = self._extract_text_from_context(context)
        token_count = self.count_tokens(total_text, model_name)
        
        return token_count > max_tokens * 0.8  # 80%阈值触发压缩
    
    def compress_context(self, context: Dict[str, Any], 
                        max_tokens: int, model_name: str,
                        strategy: str = 'summary') -> Dict[str, Any]:
        """压缩上下文"""
        if strategy not in self.compression_strategies:
            strategy = 'summary'
        
        return self.compression_strategies[strategy](context, max_tokens, model_name)
    
    def _extract_text_from_context(self, context: Dict[str, Any]) -> str:
        """从上下文中提取所有文本"""
        text_parts = []
        
        def extract_recursive(obj):
            if isinstance(obj, str):
                text_parts.append(obj)
            elif isinstance(obj, dict):
                for value in obj.values():
                    extract_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    extract_recursive(item)
        
        extract_recursive(context)
        return ' '.join(text_parts)
    
    def _summarize_context(self, context: Dict[str, Any], 
                          max_tokens: int, model_name: str) -> Dict[str, Any]:
        """通过摘要压缩上下文"""
        # 保留关键信息
        compressed_context = {
            'user_request': context.get('user_request', ''),
            'current_iteration': context.get('current_iteration', 0),
            'max_iterations': context.get('max_iterations', 3),
            'workflow_history': context.get('workflow_history', [])[-3:],  # 只保留最近3次
            'compressed': True
        }
        
        # 压缩输出历史
        if 'output' in context:
            output_text = str(context['output'])
            if len(output_text) > 1000:
                compressed_context['output_summary'] = output_text[:500] + '...[已压缩]...' + output_text[-500:]
            else:
                compressed_context['output'] = context['output']
        
        return compressed_context
    
    def _sliding_window_context(self, context: Dict[str, Any], 
                               max_tokens: int, model_name: str) -> Dict[str, Any]:
        """滑动窗口压缩"""
        # 保留最新的信息
        compressed_context = {
            'user_request': context.get('user_request', ''),
            'current_iteration': context.get('current_iteration', 0),
            'max_iterations': context.get('max_iterations', 3),
            'compressed': True
        }
        
        # 只保留最近的工作流历史
        workflow_history = context.get('workflow_history', [])
        compressed_context['workflow_history'] = workflow_history[-5:]  # 最近5步
        
        return compressed_context
    
    def _extract_key_context(self, context: Dict[str, Any], 
                            max_tokens: int, model_name: str) -> Dict[str, Any]:
        """提取关键上下文信息"""
        # 识别和保留关键信息
        key_fields = [
            'user_request', 'current_iteration', 'max_iterations',
            'validation_result', 'error_message'
        ]
        
        compressed_context = {'compressed': True}
        
        for field in key_fields:
            if field in context:
                compressed_context[field] = context[field]
        
        return compressed_context
```

### 11.3 增强的工作流引擎

集成上下文管理的工作流引擎：

```python
class EnhancedWorkflowEngine(WorkflowEngine):
    def __init__(self, a2a_protocol: A2AProtocol):
        super().__init__(a2a_protocol)
        self.context_manager = ContextManager()
    
    async def execute_workflow(self, workflow_name: str, initial_input: Dict[str, Any]):
        """增强的工作流执行，支持上下文管理"""
        workflow = self.workflows[workflow_name]
        context = {
            "input": initial_input,
            "user_request": initial_input.get("user_request", ""),
            "current_iteration": 0,
            "max_iterations": workflow.get("max_iterations", 3),
            "workflow_history": [],
            "context_compressed": False
        }
        
        step_index = 0
        while step_index < len(workflow["steps"]):
            step = workflow["steps"][step_index]
            
            # 检查上下文大小
            if step.get("enable_context_compression", True):
                max_tokens = step.get("max_context_tokens", 128000)
                model_name = self._get_step_model_name(step)
                
                if self.context_manager.should_compress_context(context, max_tokens, model_name):
                    context = self.context_manager.compress_context(
                        context, max_tokens, model_name
                    )
                    context["context_compressed"] = True
            
            # 记录步骤执行历史
            context["workflow_history"].append({
                "step_id": step.get("step_id", f"step_{step_index}"),
                "iteration": context["current_iteration"],
                "timestamp": time.time(),
                "context_size": self.context_manager.count_tokens(
                    self.context_manager._extract_text_from_context(context),
                    model_name
                )
            })
            
            # 执行步骤逻辑（与原来相同）
            if self._evaluate_condition(step.get("condition"), context):
                result = await self._execute_step(step, context)
                
                step_type = step.get("type", "agent")
                
                if step_type == "end":
                    context.update(result)
                    break
                elif step_type == "branch":
                    next_step_index = await self._handle_branch(step, context, step_index)
                    if next_step_index is not None:
                        step_index = next_step_index
                        continue
                elif step_type == "loop":
                    next_step_index = await self._handle_loop(step, context, workflow)
                    if next_step_index is not None:
                        step_index = next_step_index
                        continue
                else:
                    context.update(result)
            
            step_index += 1
        
        return context
    
    def _get_step_model_name(self, step: Dict) -> str:
        """获取步骤使用的模型名称"""
        agent_id = step.get("agent")
        if agent_id:
            # 从智能体配置中获取模型名称
            # 这里需要实际的智能体配置查询逻辑
            return "gpt-4"  # 默认值
        return "gpt-4"
```

### 11.4 功能特性总结

工作流设计器现在完全支持：

1. **循环控制**：
   - 可视化设置最大循环次数
   - 灵活的循环条件配置
   - 循环目标步骤选择
   - 输入参数覆盖

2. **分支控制**：
   - 多条件分支配置
   - 可视化分支流程
   - 分支结果映射

3. **长上下文管理**：
   - 自动令牌计数
   - 智能上下文压缩
   - 多种压缩策略（摘要、滑动窗口、关键信息提取）
   - 上下文大小监控

4. **验证机制**：
   - 专门的验证步骤类型
   - 验证阈值配置
   - 验证结果反馈

5. **数据库支持**：
   - 新增循环和分支相关字段
   - 上下文管理字段
   - 验证结果记录

6. **依赖管理**：
   - 添加tiktoken和transformers库
   - 支持多种模型的令牌计数

这些功能确保了A2A系统能够处理复杂的多智能体协作场景，同时有效管理长上下文问题，避免因上下文过长导致的调用失败。

## 12. 总结

本设计文档详细描述了基于A2A框架的多智能体系统demo的完整设计方案。系统采用模块化设计，支持配置化的智能体管理、灵活的工作流编排、MCP服务集成和友好的用户界面。

通过分阶段的开发计划，可以逐步实现从基础框架到完整功能的演进。项目采用现代化的技术栈，具有良好的可扩展性和维护性，为企业级多智能体应用提供了完整的解决方案参考。