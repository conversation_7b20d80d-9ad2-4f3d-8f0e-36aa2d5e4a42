# 开发环境配置

# 开发模式
NODE_ENV=development
VITE_MODE=development

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# 开发工具
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true
VITE_ENABLE_CONSOLE_LOG=true
VITE_ENABLE_DEVTOOLS=true

# 热重载配置
VITE_HMR_PORT=5173
VITE_HMR_HOST=localhost

# 代理配置（如果需要）
VITE_PROXY_TARGET=http://localhost:8000
VITE_PROXY_CHANGE_ORIGIN=true

# 开发服务器配置
VITE_DEV_SERVER_OPEN=true
VITE_DEV_SERVER_CORS=true
