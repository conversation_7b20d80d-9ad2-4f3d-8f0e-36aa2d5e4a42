# 生产环境配置

# 生产模式
NODE_ENV=production
VITE_MODE=production

# API配置（生产环境需要修改为实际地址）
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws

# 生产优化
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=false
VITE_ENABLE_CONSOLE_LOG=false
VITE_ENABLE_DEVTOOLS=false

# 构建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_GZIP=true

# 性能配置
VITE_CHUNK_SIZE_WARNING_LIMIT=1000
VITE_ASSET_SIZE_WARNING_LIMIT=500

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS=false
