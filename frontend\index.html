<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="A2A多智能体协作系统 - 基于Google Agent2Agent协议和阿里千问Plus的多智能体协作平台" />
  <meta name="keywords" content="多智能体,协作系统,Agent2Agent,千问,MCP,工作流" />
  <meta name="author" content="A2A Development Team" />
  
  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- 图标 -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  
  <!-- PWA配置 -->
  <link rel="manifest" href="/manifest.json" />
  <meta name="theme-color" content="#409EFF" />
  
  <!-- 安全策略 -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <meta http-equiv="X-Frame-Options" content="DENY" />
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  
  <title>A2A多智能体协作系统</title>
  
  <style>
    /* 初始加载样式 */
    #app {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    }
    
    /* 加载动画 */
    .initial-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: white;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
      animation: pulse 2s infinite;
    }
    
    .loading-text {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    
    .loading-subtitle {
      font-size: 14px;
      opacity: 0.8;
      margin-bottom: 30px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.1); opacity: 0.8; }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 隐藏加载动画 */
    .initial-loading.fade-out {
      opacity: 0;
      transition: opacity 0.5s ease-out;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 初始加载动画 -->
    <div class="initial-loading" id="initialLoading">
      <div class="loading-logo">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
      <div class="loading-text">A2A协作系统</div>
      <div class="loading-subtitle">多智能体协作平台正在启动...</div>
      <div class="loading-spinner"></div>
    </div>
  </div>
  
  <script type="module" src="/src/main.js"></script>
  
  <script>
    // 隐藏初始加载动画
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loading = document.getElementById('initialLoading');
        if (loading) {
          loading.classList.add('fade-out');
          setTimeout(() => {
            loading.style.display = 'none';
          }, 500);
        }
      }, 1000);
    });
    
    // 错误处理
    window.addEventListener('error', (event) => {
      console.error('页面错误:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise拒绝:', event.reason);
    });
  </script>
</body>
</html>
