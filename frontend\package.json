{"name": "a2a-frontend", "version": "1.0.0", "description": "A2A多智能体协作系统前端", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts --fix", "format": "prettier --write src/**/*.{vue,js,ts,css,scss,md}", "type-check": "vue-tsc --noEmit", "clean": "rimraf dist node_modules/.vite", "analyze": "vite build --mode production --analyze"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "axios": "^1.6.0", "dayjs": "^1.11.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "prettier": "^3.1.0", "vue-tsc": "^1.8.0", "typescript": "^5.3.0", "rimraf": "^5.0.0", "sass": "^1.69.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "keywords": ["vue3", "vite", "element-plus", "agent2agent", "multi-agent", "collaboration", "qwen", "mcp"], "author": "A2A Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/a2a-system.git"}, "bugs": {"url": "https://github.com/your-org/a2a-system/issues"}, "homepage": "https://github.com/your-org/a2a-system#readme"}