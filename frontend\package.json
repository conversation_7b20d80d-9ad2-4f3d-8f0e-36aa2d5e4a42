{"name": "a2a-frontend", "version": "1.0.0", "description": "A2A多智能体协作系统前端界面", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "monaco-editor": "^0.45.0", "highlight.js": "^11.9.0", "markdown-it": "^14.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "@types/lodash-es": "^4.17.12", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "sass": "^1.69.5"}, "engines": {"node": ">=18.0.0"}}