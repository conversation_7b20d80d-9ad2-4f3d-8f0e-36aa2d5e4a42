<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarCollapsed ? '64px' : '240px'" class="sidebar">
        <div class="sidebar-header">
          <div class="logo" v-if="!sidebarCollapsed">
            <el-icon><Connection /></el-icon>
            <span>A2A系统</span>
          </div>
          <div class="logo-mini" v-else>
            <el-icon><Connection /></el-icon>
          </div>
        </div>
        
        <el-menu
          :default-active="$route.path"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Odometer /></el-icon>
            <template #title>仪表板</template>
          </el-menu-item>
          
          <el-sub-menu index="workflows">
            <template #title>
              <el-icon><Operation /></el-icon>
              <span>工作流</span>
            </template>
            <el-menu-item index="/workflows">工作流列表</el-menu-item>
            <el-menu-item index="/workflows/designer">工作流设计器</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/agents">
            <el-icon><User /></el-icon>
            <template #title>智能体</template>
          </el-menu-item>
          
          <el-menu-item index="/sessions">
            <el-icon><Document /></el-icon>
            <template #title>会话管理</template>
          </el-menu-item>
          
          <el-menu-item index="/tasks">
            <el-icon><List /></el-icon>
            <template #title>任务监控</template>
          </el-menu-item>
          
          <el-menu-item index="/mcp">
            <el-icon><Connection /></el-icon>
            <template #title>MCP服务</template>
          </el-menu-item>
          
          <el-menu-item index="/logs">
            <el-icon><DocumentCopy /></el-icon>
            <template #title>系统日志</template>
          </el-menu-item>
          
          <el-menu-item index="/settings">
            <el-icon><Setting /></el-icon>
            <template #title>系统设置</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button 
              type="text" 
              @click="toggleSidebar"
              class="sidebar-toggle"
            >
              <el-icon><Expand v-if="sidebarCollapsed" /><Fold v-else /></el-icon>
            </el-button>
            
            <el-breadcrumb separator="/">
              <el-breadcrumb-item 
                v-for="breadcrumb in breadcrumbs" 
                :key="breadcrumb.path"
                :to="breadcrumb.path"
              >
                {{ breadcrumb.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- WebSocket连接状态 -->
            <div class="connection-status">
              <status-indicator 
                :status="wsConnected ? 'success' : 'error'" 
                :text="wsConnected ? '已连接' : '未连接'"
                :pulse="!wsConnected"
              />
            </div>
            
            <!-- 系统状态 -->
            <el-dropdown @command="handleSystemCommand">
              <span class="system-status">
                <el-icon><Monitor /></el-icon>
                <span v-if="!sidebarCollapsed">系统</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="status">系统状态</el-dropdown-item>
                  <el-dropdown-item command="restart">重启系统</el-dropdown-item>
                  <el-dropdown-item command="shutdown" divided>关闭系统</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            
            <!-- 帮助 -->
            <el-button type="text" @click="showHelp">
              <el-icon><QuestionFilled /></el-icon>
              <span v-if="!sidebarCollapsed">帮助</span>
            </el-button>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 全局加载遮罩 -->
    <div v-if="globalLoading" class="global-loading">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
    
    <!-- 系统状态对话框 -->
    <el-dialog v-model="statusDialogVisible" title="系统状态" width="600px">
      <div class="system-status-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
          <el-descriptions-item label="运行时间">{{ formatDuration(systemInfo.uptime) }}</el-descriptions-item>
          <el-descriptions-item label="CPU使用率">{{ systemInfo.cpu_usage }}%</el-descriptions-item>
          <el-descriptions-item label="内存使用率">{{ systemInfo.memory_usage }}%</el-descriptions-item>
          <el-descriptions-item label="活跃智能体">{{ systemInfo.active_agents }}</el-descriptions-item>
          <el-descriptions-item label="运行中任务">{{ systemInfo.running_tasks }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    
    <!-- 帮助对话框 -->
    <el-dialog v-model="helpDialogVisible" title="系统帮助" width="700px">
      <div class="help-content">
        <h3>A2A多智能体协作系统</h3>
        <p>基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统。</p>
        
        <h4>主要功能</h4>
        <ul>
          <li><strong>工作流管理</strong>: 创建、编辑和执行多智能体协作工作流</li>
          <li><strong>智能体管理</strong>: 管理和监控各种专门智能体</li>
          <li><strong>会话管理</strong>: 查看和管理工作流执行会话</li>
          <li><strong>任务监控</strong>: 实时监控智能体任务执行状态</li>
          <li><strong>MCP服务</strong>: 管理Model Context Protocol扩展服务</li>
          <li><strong>系统设置</strong>: 配置系统参数和千问API</li>
        </ul>
        
        <h4>快捷键</h4>
        <ul>
          <li><kbd>Ctrl + /</kbd>: 切换侧边栏</li>
          <li><kbd>Ctrl + R</kbd>: 刷新当前页面</li>
          <li><kbd>F1</kbd>: 显示帮助</li>
        </ul>
        
        <h4>技术支持</h4>
        <p>如需技术支持，请查看项目文档或联系开发团队。</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useWebSocket } from '@/utils/websocket'
import { systemApi } from '@/utils/api'
import { formatDuration } from '@/utils/formatters'
import StatusIndicator from '@/components/common/StatusIndicator.vue'

const route = useRoute()
const router = useRouter()
const { isConnected: wsConnected, connect } = useWebSocket()

// 响应式数据
const sidebarCollapsed = ref(false)
const globalLoading = ref(false)
const loadingText = ref('')
const statusDialogVisible = ref(false)
const helpDialogVisible = ref(false)

const systemInfo = ref({
  version: '1.0.0',
  uptime: 0,
  cpu_usage: 0,
  memory_usage: 0,
  active_agents: 0,
  running_tasks: 0
})

// 计算属性
const breadcrumbs = computed(() => {
  const pathSegments = route.path.split('/').filter(segment => segment)
  const breadcrumbs = [{ title: '首页', path: '/dashboard' }]

  const routeMap = {
    'dashboard': '仪表板',
    'workflows': '工作流',
    'designer': '设计器',
    'agents': '智能体',
    'sessions': '会话管理',
    'tasks': '任务监控',
    'mcp': 'MCP服务',
    'logs': '系统日志',
    'settings': '系统设置'
  }

  let currentPath = ''
  pathSegments.forEach(segment => {
    currentPath += `/${segment}`
    if (routeMap[segment]) {
      breadcrumbs.push({
        title: routeMap[segment],
        path: currentPath
      })
    }
  })

  return breadcrumbs
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
}

const handleSystemCommand = async (command) => {
  switch (command) {
    case 'status':
      await loadSystemStatus()
      statusDialogVisible.value = true
      break
    case 'restart':
      await handleSystemRestart()
      break
    case 'shutdown':
      await handleSystemShutdown()
      break
  }
}

const loadSystemStatus = async () => {
  try {
    const status = await systemApi.getStatus()
    systemInfo.value = {
      version: status.version || '1.0.0',
      uptime: status.uptime || 0,
      cpu_usage: status.metrics?.cpu_usage || 0,
      memory_usage: status.metrics?.memory_usage || 0,
      active_agents: status.agents?.active || 0,
      running_tasks: status.tasks?.running || 0
    }
  } catch (error) {
    console.error('加载系统状态失败:', error)
    ElMessage.error('加载系统状态失败')
  }
}

const handleSystemRestart = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重启系统吗？这将中断所有正在运行的任务。',
      '确认重启',
      {
        confirmButtonText: '重启',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    globalLoading.value = true
    loadingText.value = '正在重启系统...'

    // 这里应该调用重启API
    ElMessage.success('系统重启请求已发送')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('重启系统失败:', error)
      ElMessage.error('重启系统失败')
    }
  } finally {
    globalLoading.value = false
  }
}

const handleSystemShutdown = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要关闭系统吗？这将停止所有服务。',
      '确认关闭',
      {
        confirmButtonText: '关闭',
        cancelButtonText: '取消',
        type: 'danger'
      }
    )

    globalLoading.value = true
    loadingText.value = '正在关闭系统...'

    // 这里应该调用关闭API
    ElMessage.success('系统关闭请求已发送')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('关闭系统失败:', error)
      ElMessage.error('关闭系统失败')
    }
  } finally {
    globalLoading.value = false
  }
}

const showHelp = () => {
  helpDialogVisible.value = true
}

// 键盘快捷键
const handleKeydown = (event) => {
  if (event.ctrlKey && event.key === '/') {
    event.preventDefault()
    toggleSidebar()
  } else if (event.ctrlKey && event.key === 'r') {
    event.preventDefault()
    location.reload()
  } else if (event.key === 'F1') {
    event.preventDefault()
    showHelp()
  }
}

// 生命周期
onMounted(() => {
  // 恢复侧边栏状态
  const savedCollapsed = localStorage.getItem('sidebarCollapsed')
  if (savedCollapsed !== null) {
    sidebarCollapsed.value = savedCollapsed === 'true'
  }

  // 连接WebSocket
  if (!wsConnected.value) {
    connect()
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 加载系统状态
  loadSystemStatus()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
#app {
  height: 100vh;
  overflow: hidden;
}

.app-container {
  height: 100%;
}

.sidebar {
  background-color: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color-lighter);
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-color-primary);
  color: white;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
}

.logo-mini {
  font-size: 24px;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}

.main-container {
  flex: 1;
  overflow: hidden;
}

.header {
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  font-size: 18px;
  padding: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.system-status:hover {
  background-color: var(--el-fill-color-lighter);
}

.main-content {
  padding: 0;
  overflow-y: auto;
  background-color: var(--el-bg-color-page);
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  animation: rotate 2s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.system-status-content {
  padding: 10px 0;
}

.help-content {
  line-height: 1.6;
}

.help-content h3 {
  margin-top: 0;
  color: var(--el-color-primary);
}

.help-content h4 {
  margin: 20px 0 10px 0;
  color: var(--el-text-color-primary);
}

.help-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.help-content li {
  margin: 8px 0;
}

.help-content kbd {
  background-color: var(--el-fill-color-lighter);
  border: 1px solid var(--el-border-color);
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 10px;
  }

  .header-left,
  .header-right {
    gap: 8px;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-container {
    margin-left: 0;
  }
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-menu::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.sidebar-menu::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}
</style>
