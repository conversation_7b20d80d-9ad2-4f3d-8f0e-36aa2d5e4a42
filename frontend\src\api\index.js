import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    let message = '请求失败'
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = '资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = error.response.data?.detail || '请求失败'
      }
    } else if (error.request) {
      message = '网络连接失败'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 系统API
export const systemApi = {
  // 获取系统信息
  getInfo: () => request.get('/'),
  
  // 健康检查
  getHealth: () => request.get('/health'),
  
  // 获取统计信息
  getStatistics: () => request.get('/statistics')
}

// 智能体API
export const agentApi = {
  // 获取智能体列表
  getAgents: () => request.get('/agents'),
  
  // 获取智能体详情
  getAgent: (agentId) => request.get(`/agents/${agentId}`),
  
  // 创建智能体任务
  createTask: (agentId, taskData) => request.post(`/agents/${agentId}/tasks`, taskData),
  
  // 获取智能体健康状态
  getAgentHealth: (agentId) => request.get(`/agents/${agentId}/health`)
}

// 任务API
export const taskApi = {
  // 获取任务列表
  getTasks: (params = {}) => request.get('/tasks', { params }),
  
  // 获取任务详情
  getTask: (taskId) => request.get(`/tasks/${taskId}`),
  
  // 取消任务
  cancelTask: (taskId) => request.delete(`/tasks/${taskId}`)
}

// 工作流API
export const workflowApi = {
  // 获取工作流列表
  getWorkflows: () => request.get('/workflows'),
  
  // 获取工作流详情
  getWorkflow: (workflowId) => request.get(`/workflows/${workflowId}`),
  
  // 创建工作流
  createWorkflow: (workflowData) => request.post('/workflows', workflowData),
  
  // 更新工作流
  updateWorkflow: (workflowId, workflowData) => request.put(`/workflows/${workflowId}`, workflowData),
  
  // 删除工作流
  deleteWorkflow: (workflowId) => request.delete(`/workflows/${workflowId}`),
  
  // 执行工作流
  executeWorkflow: (workflowId, inputData) => request.post(`/workflows/${workflowId}/execute`, inputData)
}

// 会话API
export const sessionApi = {
  // 获取会话列表
  getSessions: (params = {}) => request.get('/sessions', { params }),
  
  // 获取会话详情
  getSession: (sessionId) => request.get(`/sessions/${sessionId}`),
  
  // 创建会话
  createSession: (sessionData) => request.post('/sessions', sessionData),
  
  // 获取会话任务
  getSessionTasks: (sessionId) => request.get(`/sessions/${sessionId}/tasks`),
  
  // 获取会话消息
  getSessionMessages: (sessionId) => request.get(`/sessions/${sessionId}/messages`),
  
  // 获取会话日志
  getSessionLogs: (sessionId, level = null) => {
    const params = level ? { level } : {}
    return request.get(`/sessions/${sessionId}/logs`, { params })
  }
}

// MCP服务API
export const mcpApi = {
  // 获取MCP服务列表
  getServices: () => request.get('/mcp/services'),
  
  // 获取MCP服务详情
  getService: (serviceId) => request.get(`/mcp/services/${serviceId}`),
  
  // 调用MCP服务工具
  callTool: (serviceId, toolName, parameters) => 
    request.post(`/mcp/services/${serviceId}/tools/${toolName}`, parameters),
  
  // 获取MCP服务健康状态
  getServiceHealth: (serviceId) => request.get(`/mcp/services/${serviceId}/health`),
  
  // 获取所有服务健康状态
  getAllServicesHealth: () => request.get('/mcp/health')
}

// 日志API
export const logApi = {
  // 获取系统日志
  getLogs: (params = {}) => request.get('/logs', { params }),
  
  // 获取日志统计
  getLogStats: () => request.get('/logs/stats')
}

export default request
