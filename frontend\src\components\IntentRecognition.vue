<template>
  <el-card class="intent-recognition-card">
    <template #header>
      <div class="card-header">
        <el-icon><BrainIcon /></el-icon>
        <span>意图识别结果</span>
        <el-tag :type="getConfidenceType(result.confidence)" size="small">
          置信度: {{ Math.round(result.confidence * 100) }}%
        </el-tag>
      </div>
    </template>
    
    <div class="intent-content">
      <!-- 识别的意图 -->
      <div class="intent-section">
        <h4>识别的意图</h4>
        <el-tag type="primary" size="large">{{ formatIntentType(result.intent) }}</el-tag>
        <p class="intent-description">{{ getIntentDescription(result.intent) }}</p>
      </div>
      
      <!-- 提取的实体 -->
      <div class="entities-section" v-if="result.entities && result.entities.length > 0">
        <h4>提取的实体</h4>
        <div class="entities-list">
          <el-tag 
            v-for="entity in result.entities" 
            :key="entity.name"
            :type="getEntityType(entity.type)"
            class="entity-tag"
          >
            <strong>{{ entity.name }}:</strong> {{ entity.value }}
          </el-tag>
        </div>
      </div>
      
      <!-- 参数信息 -->
      <div class="parameters-section" v-if="result.parameters">
        <h4>识别的参数</h4>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item 
            v-for="(value, key) in result.parameters" 
            :key="key"
            :label="formatParameterName(key)"
          >
            {{ value }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 建议的工作流 -->
      <div class="workflow-section" v-if="result.suggested_workflow">
        <h4>建议的工作流</h4>
        <el-alert 
          :title="result.suggested_workflow.name"
          :description="result.suggested_workflow.description"
          type="info"
          show-icon
          :closable="false"
        />
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="handleModify">
          <el-icon><Edit /></el-icon>
          修改意图
        </el-button>
        <el-button type="primary" @click="handleConfirm">
          <el-icon><Check /></el-icon>
          确认并继续
        </el-button>
      </div>
    </div>
    
    <!-- 修改意图对话框 -->
    <el-dialog v-model="modifyDialogVisible" title="修改意图识别" width="600px">
      <el-form :model="modifyForm" label-width="100px">
        <el-form-item label="意图类型">
          <el-select v-model="modifyForm.intent" placeholder="选择意图类型" style="width: 100%;">
            <el-option 
              v-for="intent in availableIntents" 
              :key="intent.value" 
              :label="intent.label" 
              :value="intent.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="置信度">
          <el-slider v-model="modifyForm.confidence" :min="0" :max="1" :step="0.01" show-input />
        </el-form-item>
        
        <el-form-item label="实体信息">
          <div class="entity-editor">
            <div v-for="(entity, index) in modifyForm.entities" :key="index" class="entity-item">
              <el-input v-model="entity.name" placeholder="实体名称" style="width: 30%;" />
              <el-select v-model="entity.type" placeholder="类型" style="width: 25%; margin: 0 10px;">
                <el-option label="文本" value="text" />
                <el-option label="数字" value="number" />
                <el-option label="日期" value="date" />
                <el-option label="技术" value="technology" />
                <el-option label="领域" value="domain" />
              </el-select>
              <el-input v-model="entity.value" placeholder="实体值" style="width: 30%;" />
              <el-button @click="removeEntity(index)" type="danger" size="small" style="margin-left: 10px;">
                删除
              </el-button>
            </div>
            <el-button @click="addEntity" type="primary" size="small">添加实体</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="参数">
          <el-input 
            v-model="modifyForm.parametersText" 
            type="textarea" 
            :rows="4" 
            placeholder="JSON格式的参数"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="modifyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveModification">保存修改</el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['confirm', 'modify'])

// 响应式数据
const modifyDialogVisible = ref(false)
const modifyForm = reactive({
  intent: '',
  confidence: 0,
  entities: [],
  parametersText: ''
})

// 可用意图类型
const availableIntents = [
  { label: '代码生成', value: 'generate_code' },
  { label: '市场调研', value: 'market_research' },
  { label: '数据分析', value: 'data_analysis' },
  { label: '文档生成', value: 'generate_document' },
  { label: '问题解答', value: 'question_answering' },
  { label: '任务规划', value: 'task_planning' }
]

// 方法
const getConfidenceType = (confidence) => {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  return 'danger'
}

const formatIntentType = (intent) => {
  const intentMap = {
    'generate_code': '代码生成',
    'market_research': '市场调研',
    'data_analysis': '数据分析',
    'generate_document': '文档生成',
    'question_answering': '问题解答',
    'task_planning': '任务规划'
  }
  return intentMap[intent] || intent
}

const getIntentDescription = (intent) => {
  const descriptionMap = {
    'generate_code': '用户希望生成代码或编程相关内容',
    'market_research': '用户需要进行市场调研或商业分析',
    'data_analysis': '用户需要分析数据或生成报告',
    'generate_document': '用户希望生成文档或文本内容',
    'question_answering': '用户提出了问题需要解答',
    'task_planning': '用户需要制定任务计划或工作流程'
  }
  return descriptionMap[intent] || '未知意图类型'
}

const getEntityType = (type) => {
  const typeMap = {
    'text': '',
    'number': 'warning',
    'date': 'info',
    'technology': 'success',
    'domain': 'primary'
  }
  return typeMap[type] || ''
}

const formatParameterName = (key) => {
  const nameMap = {
    'programming_language': '编程语言',
    'framework': '框架',
    'domain': '领域',
    'complexity': '复杂度',
    'deadline': '截止时间',
    'budget': '预算',
    'target_audience': '目标受众'
  }
  return nameMap[key] || key
}

const handleModify = () => {
  // 初始化修改表单
  modifyForm.intent = props.result.intent
  modifyForm.confidence = props.result.confidence
  modifyForm.entities = [...(props.result.entities || [])]
  modifyForm.parametersText = JSON.stringify(props.result.parameters || {}, null, 2)
  
  modifyDialogVisible.value = true
}

const handleConfirm = () => {
  emit('confirm', props.result)
}

const addEntity = () => {
  modifyForm.entities.push({
    name: '',
    type: 'text',
    value: ''
  })
}

const removeEntity = (index) => {
  modifyForm.entities.splice(index, 1)
}

const saveModification = () => {
  try {
    const modifiedResult = {
      ...props.result,
      intent: modifyForm.intent,
      confidence: modifyForm.confidence,
      entities: modifyForm.entities.filter(e => e.name && e.value),
      parameters: JSON.parse(modifyForm.parametersText || '{}')
    }
    
    emit('modify', modifiedResult)
    modifyDialogVisible.value = false
    ElMessage.success('意图识别结果已修改')
  } catch (error) {
    ElMessage.error('参数格式错误，请检查JSON格式')
  }
}
</script>

<style scoped>
.intent-recognition-card {
  margin: 20px 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.intent-content {
  padding: 10px 0;
}

.intent-section,
.entities-section,
.parameters-section,
.workflow-section {
  margin-bottom: 20px;
}

.intent-section h4,
.entities-section h4,
.parameters-section h4,
.workflow-section h4 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.intent-description {
  margin: 8px 0 0 0;
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.entities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.entity-tag {
  margin: 2px;
}

.actions {
  margin-top: 20px;
  text-align: right;
}

.actions .el-button {
  margin-left: 10px;
}

.entity-editor {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 10px;
}

.entity-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.entity-item:last-child {
  margin-bottom: 0;
}
</style>
