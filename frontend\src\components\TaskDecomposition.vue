<template>
  <el-card class="task-decomposition-card">
    <template #header>
      <div class="card-header">
        <el-icon><Operation /></el-icon>
        <span>任务分解结果</span>
        <el-tag type="info" size="small">
          共 {{ result.tasks?.length || 0 }} 个子任务
        </el-tag>
      </div>
    </template>
    
    <div class="decomposition-content">
      <!-- 主任务信息 -->
      <div class="main-task-section">
        <h4>主任务</h4>
        <el-alert 
          :title="result.main_task?.title || '未定义'"
          :description="result.main_task?.description"
          type="primary"
          show-icon
          :closable="false"
        />
      </div>
      
      <!-- 子任务列表 -->
      <div class="subtasks-section">
        <h4>子任务分解</h4>
        <div class="tasks-timeline">
          <div 
            v-for="(task, index) in result.tasks" 
            :key="task.id || index"
            class="task-item"
            :class="{ 'task-parallel': task.execution_mode === 'parallel' }"
          >
            <div class="task-number">{{ index + 1 }}</div>
            <div class="task-content">
              <div class="task-header">
                <h5>{{ task.title }}</h5>
                <div class="task-meta">
                  <el-tag 
                    :type="getExecutionModeType(task.execution_mode)" 
                    size="small"
                  >
                    {{ formatExecutionMode(task.execution_mode) }}
                  </el-tag>
                  <el-tag 
                    v-if="task.estimated_duration" 
                    type="info" 
                    size="small"
                  >
                    预计 {{ task.estimated_duration }}
                  </el-tag>
                  <el-tag 
                    v-if="task.priority" 
                    :type="getPriorityType(task.priority)" 
                    size="small"
                  >
                    {{ formatPriority(task.priority) }}
                  </el-tag>
                </div>
              </div>
              
              <p class="task-description">{{ task.description }}</p>
              
              <!-- 任务依赖 -->
              <div v-if="task.dependencies && task.dependencies.length > 0" class="task-dependencies">
                <span class="dependency-label">依赖任务:</span>
                <el-tag 
                  v-for="dep in task.dependencies" 
                  :key="dep"
                  size="small"
                  type="warning"
                >
                  任务 {{ dep }}
                </el-tag>
              </div>
              
              <!-- 所需技能 -->
              <div v-if="task.required_skills && task.required_skills.length > 0" class="task-skills">
                <span class="skills-label">所需技能:</span>
                <el-tag 
                  v-for="skill in task.required_skills" 
                  :key="skill"
                  size="small"
                  type="success"
                >
                  {{ skill }}
                </el-tag>
              </div>
              
              <!-- 输入输出 -->
              <div class="task-io" v-if="task.inputs || task.outputs">
                <div v-if="task.inputs" class="task-inputs">
                  <strong>输入:</strong> {{ formatIOData(task.inputs) }}
                </div>
                <div v-if="task.outputs" class="task-outputs">
                  <strong>输出:</strong> {{ formatIOData(task.outputs) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 执行策略 -->
      <div class="strategy-section" v-if="result.execution_strategy">
        <h4>执行策略</h4>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="执行模式">
            {{ formatExecutionMode(result.execution_strategy.mode) }}
          </el-descriptions-item>
          <el-descriptions-item label="并行度">
            {{ result.execution_strategy.parallelism || '自动' }}
          </el-descriptions-item>
          <el-descriptions-item label="错误处理">
            {{ formatErrorHandling(result.execution_strategy.error_handling) }}
          </el-descriptions-item>
          <el-descriptions-item label="超时设置">
            {{ result.execution_strategy.timeout || '默认' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="handleRedecompose">
          <el-icon><Refresh /></el-icon>
          重新分解
        </el-button>
        <el-button @click="handleModify">
          <el-icon><Edit /></el-icon>
          修改任务
        </el-button>
        <el-button type="primary" @click="handleConfirm">
          <el-icon><Check /></el-icon>
          确认分解
        </el-button>
      </div>
    </div>
    
    <!-- 修改任务对话框 -->
    <el-dialog v-model="modifyDialogVisible" title="修改任务分解" width="800px">
      <div class="modify-content">
        <el-form :model="modifyForm" label-width="100px">
          <el-form-item label="主任务标题">
            <el-input v-model="modifyForm.mainTask.title" />
          </el-form-item>
          <el-form-item label="主任务描述">
            <el-input v-model="modifyForm.mainTask.description" type="textarea" :rows="2" />
          </el-form-item>
        </el-form>
        
        <h4>子任务列表</h4>
        <div class="subtasks-editor">
          <div v-for="(task, index) in modifyForm.tasks" :key="index" class="subtask-editor">
            <el-card>
              <template #header>
                <div class="subtask-header">
                  <span>任务 {{ index + 1 }}</span>
                  <el-button @click="removeTask(index)" type="danger" size="small">删除</el-button>
                </div>
              </template>
              
              <el-form :model="task" label-width="80px" size="small">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="标题">
                      <el-input v-model="task.title" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="执行模式">
                      <el-select v-model="task.execution_mode" style="width: 100%;">
                        <el-option label="顺序执行" value="sequential" />
                        <el-option label="并行执行" value="parallel" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-form-item label="描述">
                  <el-input v-model="task.description" type="textarea" :rows="2" />
                </el-form-item>
                
                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item label="优先级">
                      <el-select v-model="task.priority" style="width: 100%;">
                        <el-option label="高" value="high" />
                        <el-option label="中" value="medium" />
                        <el-option label="低" value="low" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="预计时长">
                      <el-input v-model="task.estimated_duration" placeholder="如: 30分钟" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="依赖任务">
                      <el-input v-model="task.dependenciesText" placeholder="如: 1,2" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>
          </div>
          
          <el-button @click="addTask" type="primary" style="width: 100%; margin-top: 10px;">
            添加子任务
          </el-button>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="modifyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveModification">保存修改</el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  result: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['confirm', 'modify', 'redecompose'])

// 响应式数据
const modifyDialogVisible = ref(false)
const modifyForm = reactive({
  mainTask: {
    title: '',
    description: ''
  },
  tasks: []
})

// 方法
const getExecutionModeType = (mode) => {
  return mode === 'parallel' ? 'warning' : 'primary'
}

const formatExecutionMode = (mode) => {
  const modeMap = {
    'sequential': '顺序执行',
    'parallel': '并行执行'
  }
  return modeMap[mode] || mode
}

const getPriorityType = (priority) => {
  const typeMap = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return typeMap[priority] || 'info'
}

const formatPriority = (priority) => {
  const priorityMap = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return priorityMap[priority] || priority
}

const formatErrorHandling = (handling) => {
  const handlingMap = {
    'stop': '遇错停止',
    'continue': '继续执行',
    'retry': '自动重试'
  }
  return handlingMap[handling] || handling
}

const formatIOData = (data) => {
  if (Array.isArray(data)) {
    return data.join(', ')
  }
  if (typeof data === 'object') {
    return Object.keys(data).join(', ')
  }
  return String(data)
}

const handleRedecompose = () => {
  emit('redecompose')
}

const handleModify = () => {
  // 初始化修改表单
  modifyForm.mainTask = {
    title: props.result.main_task?.title || '',
    description: props.result.main_task?.description || ''
  }
  
  modifyForm.tasks = (props.result.tasks || []).map(task => ({
    ...task,
    dependenciesText: task.dependencies ? task.dependencies.join(',') : ''
  }))
  
  modifyDialogVisible.value = true
}

const handleConfirm = () => {
  emit('confirm', props.result)
}

const addTask = () => {
  modifyForm.tasks.push({
    title: '',
    description: '',
    execution_mode: 'sequential',
    priority: 'medium',
    estimated_duration: '',
    dependenciesText: '',
    required_skills: [],
    inputs: [],
    outputs: []
  })
}

const removeTask = (index) => {
  modifyForm.tasks.splice(index, 1)
}

const saveModification = () => {
  try {
    const modifiedResult = {
      ...props.result,
      main_task: modifyForm.mainTask,
      tasks: modifyForm.tasks.map(task => ({
        ...task,
        dependencies: task.dependenciesText ? 
          task.dependenciesText.split(',').map(d => parseInt(d.trim())).filter(d => !isNaN(d)) : 
          []
      }))
    }
    
    emit('modify', modifiedResult)
    modifyDialogVisible.value = false
    ElMessage.success('任务分解已修改')
  } catch (error) {
    ElMessage.error('保存修改失败')
  }
}
</script>

<style scoped>
.task-decomposition-card {
  margin: 20px 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.decomposition-content {
  padding: 10px 0;
}

.main-task-section,
.subtasks-section,
.strategy-section {
  margin-bottom: 20px;
}

.main-task-section h4,
.subtasks-section h4,
.strategy-section h4 {
  margin: 0 0 10px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.tasks-timeline {
  position: relative;
}

.task-item {
  display: flex;
  margin-bottom: 16px;
  position: relative;
}

.task-item::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 30px;
  bottom: -16px;
  width: 2px;
  background-color: var(--el-border-color-lighter);
}

.task-item:last-child::before {
  display: none;
}

.task-parallel::before {
  background-color: var(--el-color-warning);
}

.task-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  margin-right: 16px;
  z-index: 1;
}

.task-parallel .task-number {
  background-color: var(--el-color-warning);
}

.task-content {
  flex: 1;
  background-color: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-header h5 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.task-meta {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.task-description {
  margin: 8px 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.task-dependencies,
.task-skills {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.dependency-label,
.skills-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.task-io {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.task-inputs,
.task-outputs {
  margin: 4px 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.actions {
  margin-top: 20px;
  text-align: right;
}

.actions .el-button {
  margin-left: 10px;
}

.modify-content {
  max-height: 600px;
  overflow-y: auto;
}

.subtasks-editor {
  margin-top: 16px;
}

.subtask-editor {
  margin-bottom: 16px;
}

.subtask-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
