<template>
  <div class="progress-bar-container">
    <div class="progress-header" v-if="showHeader">
      <span class="progress-label">{{ label }}</span>
      <span class="progress-percentage">{{ Math.round(percentage) }}%</span>
    </div>
    <div class="progress-bar" :style="{ height: height + 'px' }">
      <div 
        class="progress-fill" 
        :class="{ 
          'progress-success': status === 'success',
          'progress-warning': status === 'warning',
          'progress-error': status === 'error'
        }"
        :style="{ width: percentage + '%' }"
      >
        <div v-if="animated" class="progress-animation"></div>
      </div>
    </div>
    <div v-if="description" class="progress-description">{{ description }}</div>
  </div>
</template>

<script setup>
defineProps({
  percentage: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0 && value <= 100
  },
  label: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'error'].includes(value)
  },
  height: {
    type: Number,
    default: 8
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  animated: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped>
.progress-bar-container {
  width: 100%;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.progress-percentage {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-family: 'Courier New', monospace;
}

.progress-bar {
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background-color: var(--el-color-primary);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill.progress-success {
  background-color: var(--el-color-success);
}

.progress-fill.progress-warning {
  background-color: var(--el-color-warning);
}

.progress-fill.progress-error {
  background-color: var(--el-color-danger);
}

.progress-animation {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
  animation: progress-stripes 1s linear infinite;
}

.progress-description {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

@keyframes progress-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}
</style>
