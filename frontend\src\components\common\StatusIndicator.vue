<template>
  <div class="status-indicator" :class="`status-${status}`">
    <div class="status-dot" :class="{ 'status-pulse': pulse }"></div>
    <span v-if="text" class="status-text">{{ text }}</span>
  </div>
</template>

<script setup>
defineProps({
  status: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'success', 'warning', 'error', 'info', 'processing'].includes(value)
  },
  text: {
    type: String,
    default: ''
  },
  pulse: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--el-color-info);
  transition: all 0.3s ease;
}

.status-pulse {
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 状态颜色 */
.status-success .status-dot {
  background-color: var(--el-color-success);
}

.status-warning .status-dot {
  background-color: var(--el-color-warning);
}

.status-error .status-dot {
  background-color: var(--el-color-danger);
}

.status-info .status-dot {
  background-color: var(--el-color-info);
}

.status-processing .status-dot {
  background-color: var(--el-color-primary);
}

.status-success .status-text {
  color: var(--el-color-success);
}

.status-warning .status-text {
  color: var(--el-color-warning);
}

.status-error .status-text {
  color: var(--el-color-danger);
}

.status-info .status-text {
  color: var(--el-color-info);
}

.status-processing .status-text {
  color: var(--el-color-primary);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
