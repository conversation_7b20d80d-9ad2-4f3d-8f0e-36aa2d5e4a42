<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
      <div class="logo">
        <img src="/logo.svg" alt="A2A" v-if="!isCollapse" />
        <span v-if="!isCollapse">A2A系统</span>
        <img src="/logo.svg" alt="A2A" v-else class="logo-mini" />
      </div>
      
      <el-menu
        :default-active="$route.path"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.path"
          :index="route.path"
        >
          <el-icon><component :is="route.meta.icon" /></el-icon>
          <template #title>{{ route.meta.title }}</template>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>A2A系统</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 系统状态 -->
          <el-badge :value="systemStatus.runningTasks" class="status-badge">
            <el-button type="text" @click="showSystemStatus">
              <el-icon><Monitor /></el-icon>
              系统状态
            </el-button>
          </el-badge>

          <!-- 主题切换 -->
          <el-switch
            v-model="isDark"
            @change="toggleTheme"
            inline-prompt
            :active-icon="Moon"
            :inactive-icon="Sunny"
            class="theme-switch"
          />

          <!-- 用户菜单 -->
          <el-dropdown>
            <el-button type="text" class="user-btn">
              <el-icon><User /></el-icon>
              管理员
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showAbout">关于系统</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>

  <!-- 系统状态对话框 -->
  <el-dialog
    v-model="statusDialogVisible"
    title="系统状态"
    width="600px"
  >
    <div class="status-info">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-statistic title="运行中任务" :value="systemStatus.runningTasks" />
        </el-col>
        <el-col :span="12">
          <el-statistic title="活跃智能体" :value="systemStatus.activeAgents" />
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-statistic title="总执行次数" :value="systemStatus.totalExecutions" />
        </el-col>
        <el-col :span="12">
          <el-statistic title="成功率" :value="systemStatus.successRate" suffix="%" />
        </el-col>
      </el-row>
    </div>
  </el-dialog>

  <!-- 关于对话框 -->
  <el-dialog
    v-model="aboutDialogVisible"
    title="关于A2A系统"
    width="500px"
  >
    <div class="about-info">
      <h3>A2A多智能体协作系统</h3>
      <p>版本: 1.0.0</p>
      <p>基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统</p>
      <br>
      <p><strong>技术栈:</strong></p>
      <ul>
        <li>后端: Python 3.12+ + FastAPI + A2A SDK</li>
        <li>前端: Vue.js 3 + Element Plus</li>
        <li>LLM: 阿里千问Plus (qwen-plus)</li>
        <li>协议: Google Agent2Agent (A2A)</li>
      </ul>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDark, useToggle } from '@vueuse/core'
import { systemApi } from '@/api'

const route = useRoute()
const router = useRouter()

// 响应式数据
const isCollapse = ref(false)
const statusDialogVisible = ref(false)
const aboutDialogVisible = ref(false)
const systemStatus = ref({
  runningTasks: 0,
  activeAgents: 0,
  totalExecutions: 0,
  successRate: 0
})

// 主题切换
const isDark = useDark()
const toggleTheme = useToggle(isDark)

// 菜单路由
const menuRoutes = computed(() => {
  return router.getRoutes()
    .find(r => r.name === 'Layout')
    ?.children?.filter(child => child.meta?.title) || []
})

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || '未知页面'
})

// 方法
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const showSystemStatus = async () => {
  try {
    const response = await systemApi.getHealth()
    systemStatus.value = {
      runningTasks: response.active_tasks || 0,
      activeAgents: response.total_agents || 0,
      totalExecutions: response.total_executions || 0,
      successRate: Math.round((response.success_rate || 0) * 100)
    }
    statusDialogVisible.value = true
  } catch (error) {
    console.error('获取系统状态失败:', error)
  }
}

const showAbout = () => {
  aboutDialogVisible.value = true
}

// 定期更新系统状态
const updateSystemStatus = async () => {
  try {
    const response = await systemApi.getHealth()
    systemStatus.value.runningTasks = response.active_tasks || 0
    systemStatus.value.activeAgents = response.total_agents || 0
  } catch (error) {
    console.error('更新系统状态失败:', error)
  }
}

onMounted(() => {
  // 初始加载系统状态
  updateSystemStatus()
  
  // 每30秒更新一次系统状态
  setInterval(updateSystemStatus, 30000)
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: var(--el-bg-color-page);
  transition: width 0.3s;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  border-bottom: 1px solid var(--el-border-color-light);
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.logo img {
  height: 32px;
  margin-right: 10px;
}

.logo-mini {
  height: 32px;
}

.sidebar-menu {
  border-right: none;
  height: calc(100vh - 60px);
}

.header {
  background-color: var(--el-bg-color);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 20px;
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-badge {
  margin-right: 10px;
}

.theme-switch {
  margin: 0 10px;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-content {
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
}

.status-info {
  padding: 20px 0;
}

.about-info h3 {
  color: var(--el-color-primary);
  margin-bottom: 10px;
}

.about-info ul {
  margin-left: 20px;
}

.about-info li {
  margin: 5px 0;
}
</style>
