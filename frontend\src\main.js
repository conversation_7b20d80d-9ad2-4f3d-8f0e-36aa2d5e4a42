import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'

// 创建应用实例
const app = createApp(App)

// 创建Pinia状态管理
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局配置
app.config.globalProperties.$ELEMENT = {
  size: 'default',
  zIndex: 3000
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
  
  // 这里可以添加错误上报逻辑
  if (import.meta.env.PROD) {
    // 生产环境错误上报
    // reportError(err, vm, info)
  }
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

// 挂载应用
app.mount('#app')

// 开发环境调试
if (import.meta.env.DEV) {
  window.__app__ = app
  console.log('A2A多智能体协作系统已启动')
  console.log('开发模式已启用')
}
