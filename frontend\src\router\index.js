import { createRouter, createWebHistory } from 'vue-router'

// 路由组件
const Dashboard = () => import('@/views/Dashboard.vue')
const Workflows = () => import('@/views/Workflows.vue')
const WorkflowDesigner = () => import('@/views/WorkflowDesigner.vue')
const Agents = () => import('@/views/Agents.vue')
const Sessions = () => import('@/views/Sessions.vue')
const Tasks = () => import('@/views/Tasks.vue')
const MCPServers = () => import('@/views/MCPServers.vue')
const Logs = () => import('@/views/Logs.vue')
const Settings = () => import('@/views/Settings.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      icon: 'Odometer'
    }
  },
  {
    path: '/workflows',
    name: 'Workflows',
    component: Workflows,
    meta: {
      title: '工作流管理',
      icon: 'Operation'
    }
  },
  {
    path: '/workflows/designer',
    name: 'WorkflowDesigner',
    component: WorkflowDesigner,
    meta: {
      title: '工作流设计器',
      icon: 'Operation',
      parent: 'Workflows'
    }
  },
  {
    path: '/workflows/:id',
    name: 'WorkflowDetail',
    component: () => import('@/views/WorkflowDetail.vue'),
    meta: {
      title: '工作流详情',
      icon: 'Operation',
      parent: 'Workflows'
    }
  },
  {
    path: '/agents',
    name: 'Agents',
    component: Agents,
    meta: {
      title: '智能体管理',
      icon: 'User'
    }
  },
  {
    path: '/agents/:id',
    name: 'AgentDetail',
    component: () => import('@/views/AgentDetail.vue'),
    meta: {
      title: '智能体详情',
      icon: 'User',
      parent: 'Agents'
    }
  },
  {
    path: '/sessions',
    name: 'Sessions',
    component: Sessions,
    meta: {
      title: '会话管理',
      icon: 'Document'
    }
  },
  {
    path: '/sessions/:id',
    name: 'SessionDetail',
    component: () => import('@/views/SessionDetail.vue'),
    meta: {
      title: '会话详情',
      icon: 'Document',
      parent: 'Sessions'
    }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: Tasks,
    meta: {
      title: '任务监控',
      icon: 'List'
    }
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetail',
    component: () => import('@/views/TaskDetail.vue'),
    meta: {
      title: '任务详情',
      icon: 'List',
      parent: 'Tasks'
    }
  },
  {
    path: '/mcp',
    name: 'MCPServers',
    component: MCPServers,
    meta: {
      title: 'MCP服务管理',
      icon: 'Connection'
    }
  },
  {
    path: '/logs',
    name: 'Logs',
    component: Logs,
    meta: {
      title: '系统日志',
      icon: 'DocumentCopy'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      icon: 'Setting'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - A2A多智能体协作系统`
  } else {
    document.title = 'A2A多智能体协作系统'
  }
  
  // 这里可以添加权限验证逻辑
  next()
})

router.afterEach((to, from) => {
  // 页面切换后的处理
  console.log(`路由切换: ${from.path} -> ${to.path}`)
})

export default router
