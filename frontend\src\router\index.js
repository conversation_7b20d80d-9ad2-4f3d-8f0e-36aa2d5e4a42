import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '系统概览', icon: 'Monitor' }
      },
      {
        path: '/agents',
        name: 'Agents',
        component: () => import('@/views/Agents.vue'),
        meta: { title: '智能体管理', icon: 'Avatar' }
      },
      {
        path: '/workflows',
        name: 'Workflows',
        component: () => import('@/views/Workflows.vue'),
        meta: { title: '工作流管理', icon: 'Connection' }
      },
      {
        path: '/sessions',
        name: 'Sessions',
        component: () => import('@/views/Sessions.vue'),
        meta: { title: '执行会话', icon: 'ChatDotSquare' }
      },
      {
        path: '/tasks',
        name: 'Tasks',
        component: () => import('@/views/Tasks.vue'),
        meta: { title: '任务监控', icon: 'List' }
      },
      {
        path: '/mcp',
        name: 'MCP',
        component: () => import('@/views/MCP.vue'),
        meta: { title: 'MCP服务', icon: 'Connection' }
      },
      {
        path: '/logs',
        name: 'Logs',
        component: () => import('@/views/Logs.vue'),
        meta: { title: '系统日志', icon: 'Document' }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: { title: '系统设置', icon: 'Setting' }
      }
    ]
  },
  {
    path: '/workflow-designer',
    name: 'WorkflowDesigner',
    component: () => import('@/views/WorkflowDesigner.vue'),
    meta: { title: '工作流设计器' }
  },
  {
    path: '/session/:id',
    name: 'SessionDetail',
    component: () => import('@/views/SessionDetail.vue'),
    meta: { title: '会话详情' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
