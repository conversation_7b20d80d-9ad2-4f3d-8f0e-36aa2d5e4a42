import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { agent<PERSON>pi } from '@/utils/api'
import { ElMessage } from 'element-plus'

export const useAgentsStore = defineStore('agents', () => {
  // 状态
  const agents = ref([])
  const currentAgent = ref(null)
  const tasks = ref([])
  const loading = ref(false)
  
  // 计算属性
  const agentCount = computed(() => agents.value.length)
  const activeAgents = computed(() => 
    agents.value.filter(a => a.is_active)
  )
  const runningTasks = computed(() =>
    tasks.value.filter(t => t.status === 'running')
  )
  
  // 智能体技能映射
  const agentSkills = {
    'intent_recognizer': [
      { label: '识别意图', value: 'recognize_intent' },
      { label: '提取实体', value: 'extract_entities' }
    ],
    'task_decomposer': [
      { label: '分解任务', value: 'decompose_task' },
      { label: '优化任务', value: 'optimize_tasks' }
    ],
    'code_generator': [
      { label: '生成代码', value: 'generate_code' },
      { label: '优化代码', value: 'optimize_code' },
      { label: '审查代码', value: 'review_code' }
    ],
    'market_researcher': [
      { label: '市场调研', value: 'conduct_market_research' },
      { label: '竞争分析', value: 'analyze_competition' },
      { label: '趋势分析', value: 'analyze_trends' }
    ],
    'result_validator': [
      { label: '验证结果', value: 'validate_result' },
      { label: '评估质量', value: 'assess_quality' },
      { label: '匹配需求', value: 'verify_requirement_match' }
    ]
  }
  
  // 获取所有智能体
  const fetchAgents = async () => {
    loading.value = true
    try {
      const data = await agentApi.getAgents()
      agents.value = data
      return data
    } catch (error) {
      console.error('获取智能体列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取智能体详情
  const fetchAgent = async (agentId) => {
    loading.value = true
    try {
      const data = await agentApi.getAgent(agentId)
      currentAgent.value = data
      return data
    } catch (error) {
      console.error('获取智能体详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 创建智能体任务
  const createTask = async (agentId, taskData) => {
    loading.value = true
    try {
      const data = await agentApi.createTask(agentId, taskData)
      tasks.value.push(data)
      ElMessage.success('任务创建成功')
      return data
    } catch (error) {
      console.error('创建任务失败:', error)
      ElMessage.error('创建任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取智能体任务列表
  const fetchTasks = async (agentId, params = {}) => {
    loading.value = true
    try {
      const data = await agentApi.getTasks(agentId, params)
      if (params.append) {
        tasks.value.push(...data)
      } else {
        tasks.value = data
      }
      return data
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取任务详情
  const fetchTask = async (agentId, taskId) => {
    try {
      const data = await agentApi.getTask(agentId, taskId)
      
      // 更新任务列表中的任务
      const index = tasks.value.findIndex(t => t.id === taskId)
      if (index > -1) {
        tasks.value[index] = data
      }
      
      return data
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw error
    }
  }
  
  // 取消任务
  const cancelTask = async (agentId, taskId) => {
    try {
      await agentApi.cancelTask(agentId, taskId)
      
      // 更新任务状态
      const task = tasks.value.find(t => t.id === taskId)
      if (task) {
        task.status = 'cancelled'
      }
      
      ElMessage.success('任务已取消')
    } catch (error) {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
      throw error
    }
  }
  
  // 根据ID查找智能体
  const findAgentById = (agentId) => {
    return agents.value.find(a => a.id === agentId)
  }
  
  // 获取智能体技能列表
  const getAgentSkills = (agentId) => {
    return agentSkills[agentId] || []
  }
  
  // 获取智能体显示名称
  const getAgentDisplayName = (agentId) => {
    const nameMap = {
      'intent_recognizer': '意图识别智能体',
      'task_decomposer': '任务分解智能体',
      'code_generator': '代码生成智能体',
      'market_researcher': '市场调研智能体',
      'result_validator': '结果验证智能体'
    }
    return nameMap[agentId] || agentId
  }
  
  // 获取智能体状态统计
  const getAgentStats = (agentId) => {
    const agentTasks = tasks.value.filter(t => t.agent_id === agentId)
    return {
      total: agentTasks.length,
      running: agentTasks.filter(t => t.status === 'running').length,
      completed: agentTasks.filter(t => t.status === 'completed').length,
      failed: agentTasks.filter(t => t.status === 'failed').length
    }
  }
  
  // 更新任务状态
  const updateTaskStatus = (taskId, status, result = null) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.status = status
      if (result) {
        task.result = result
      }
      if (status === 'completed' || status === 'failed') {
        task.completed_at = new Date().toISOString()
      }
    }
  }
  
  // 清空任务列表
  const clearTasks = () => {
    tasks.value = []
  }
  
  // 重置状态
  const reset = () => {
    agents.value = []
    currentAgent.value = null
    tasks.value = []
    loading.value = false
  }
  
  return {
    // 状态
    agents,
    currentAgent,
    tasks,
    loading,
    
    // 计算属性
    agentCount,
    activeAgents,
    runningTasks,
    
    // 方法
    fetchAgents,
    fetchAgent,
    createTask,
    fetchTasks,
    fetchTask,
    cancelTask,
    findAgentById,
    getAgentSkills,
    getAgentDisplayName,
    getAgentStats,
    updateTaskStatus,
    clearTasks,
    reset
  }
})
