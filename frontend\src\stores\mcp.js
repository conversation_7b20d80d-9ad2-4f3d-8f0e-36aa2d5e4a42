import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { mcpApi } from '@/utils/api'
import { ElMessage } from 'element-plus'

export const useMCPStore = defineStore('mcp', () => {
  // 状态
  const servers = ref([])
  const currentServer = ref(null)
  const loading = ref(false)
  
  // 计算属性
  const serverCount = computed(() => servers.value.length)
  const activeServers = computed(() => 
    servers.value.filter(s => s.is_active)
  )
  const connectedServers = computed(() =>
    servers.value.filter(s => s.status === 'connected')
  )
  
  // 获取所有MCP服务器
  const fetchServers = async () => {
    loading.value = true
    try {
      const data = await mcpApi.getServers()
      servers.value = data
      return data
    } catch (error) {
      console.error('获取MCP服务器列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 创建MCP服务器
  const createServer = async (serverData) => {
    loading.value = true
    try {
      const data = await mcpApi.createServer(serverData)
      servers.value.push(data)
      ElMessage.success('MCP服务器创建成功')
      return data
    } catch (error) {
      console.error('创建MCP服务器失败:', error)
      ElMessage.error('创建MCP服务器失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 更新MCP服务器
  const updateServer = async (serverId, serverData) => {
    loading.value = true
    try {
      const data = await mcpApi.updateServer(serverId, serverData)
      const index = servers.value.findIndex(s => s.id === serverId)
      if (index > -1) {
        servers.value[index] = data
      }
      if (currentServer.value?.id === serverId) {
        currentServer.value = data
      }
      ElMessage.success('MCP服务器更新成功')
      return data
    } catch (error) {
      console.error('更新MCP服务器失败:', error)
      ElMessage.error('更新MCP服务器失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 删除MCP服务器
  const deleteServer = async (serverId) => {
    loading.value = true
    try {
      await mcpApi.deleteServer(serverId)
      servers.value = servers.value.filter(s => s.id !== serverId)
      if (currentServer.value?.id === serverId) {
        currentServer.value = null
      }
      ElMessage.success('MCP服务器删除成功')
    } catch (error) {
      console.error('删除MCP服务器失败:', error)
      ElMessage.error('删除MCP服务器失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 测试MCP服务器连接
  const testServer = async (serverId) => {
    try {
      const result = await mcpApi.testServer(serverId)
      
      // 更新服务器状态
      const server = servers.value.find(s => s.id === serverId)
      if (server) {
        server.status = result.success ? 'connected' : 'error'
        server.last_test = new Date().toISOString()
        if (result.error) {
          server.error_message = result.error
        }
      }
      
      return result
    } catch (error) {
      console.error('测试MCP服务器连接失败:', error)
      throw error
    }
  }
  
  // 启用/禁用MCP服务器
  const toggleServer = async (serverId, isActive) => {
    try {
      await mcpApi.toggleServer(serverId, isActive)
      
      // 更新服务器状态
      const server = servers.value.find(s => s.id === serverId)
      if (server) {
        server.is_active = isActive
        if (!isActive) {
          server.status = 'disconnected'
        }
      }
      
      ElMessage.success(isActive ? 'MCP服务器已启用' : 'MCP服务器已禁用')
    } catch (error) {
      console.error('切换MCP服务器状态失败:', error)
      ElMessage.error('操作失败')
      throw error
    }
  }
  
  // 调用MCP服务器功能
  const callServer = async (serverId, capability, parameters) => {
    try {
      const result = await mcpApi.callServer(serverId, capability, parameters)
      return result
    } catch (error) {
      console.error('调用MCP服务器功能失败:', error)
      throw error
    }
  }
  
  // 获取MCP服务器能力
  const getServerCapabilities = async (serverId) => {
    try {
      const capabilities = await mcpApi.getServerCapabilities(serverId)
      
      // 更新服务器能力信息
      const server = servers.value.find(s => s.id === serverId)
      if (server) {
        server.capabilities = capabilities
      }
      
      return capabilities
    } catch (error) {
      console.error('获取MCP服务器能力失败:', error)
      throw error
    }
  }
  
  // 根据ID查找服务器
  const findServerById = (serverId) => {
    return servers.value.find(s => s.id === serverId)
  }
  
  // 获取服务器类型显示名称
  const getServerTypeDisplayName = (type) => {
    const typeMap = {
      'stdio': '本地stdio',
      'http': 'HTTP服务',
      'websocket': 'WebSocket',
      'file_operations': '文件操作',
      'web_search': '网络搜索',
      'data_analysis': '数据分析',
      'git_operations': 'Git操作',
      'database': '数据库',
      'api_client': 'API客户端',
      'custom': '自定义'
    }
    return typeMap[type] || type
  }
  
  // 获取服务器状态统计
  const getServerStats = () => {
    return {
      total: servers.value.length,
      active: activeServers.value.length,
      connected: connectedServers.value.length,
      disconnected: servers.value.filter(s => s.status === 'disconnected').length,
      error: servers.value.filter(s => s.status === 'error').length
    }
  }
  
  // 验证服务器配置
  const validateServerConfig = (config) => {
    const errors = []
    
    if (!config.name) {
      errors.push('服务器名称不能为空')
    }
    
    if (!config.type) {
      errors.push('服务器类型不能为空')
    }
    
    if (config.type === 'stdio') {
      if (!config.command) {
        errors.push('stdio类型服务器必须指定启动命令')
      }
    } else if (config.type === 'http') {
      if (!config.base_url) {
        errors.push('HTTP类型服务器必须指定基础URL')
      }
    }
    
    return errors
  }
  
  // 生成默认服务器配置
  const getDefaultServerConfig = (type = 'stdio') => {
    const baseConfig = {
      name: '',
      description: '',
      type: type,
      is_active: true,
      env: {}
    }
    
    if (type === 'stdio') {
      return {
        ...baseConfig,
        command: 'npx',
        args: []
      }
    } else if (type === 'http') {
      return {
        ...baseConfig,
        base_url: '',
        headers: {}
      }
    }
    
    return baseConfig
  }
  
  // 重置状态
  const reset = () => {
    servers.value = []
    currentServer.value = null
    loading.value = false
  }
  
  return {
    // 状态
    servers,
    currentServer,
    loading,
    
    // 计算属性
    serverCount,
    activeServers,
    connectedServers,
    
    // 方法
    fetchServers,
    createServer,
    updateServer,
    deleteServer,
    testServer,
    toggleServer,
    callServer,
    getServerCapabilities,
    findServerById,
    getServerTypeDisplayName,
    getServerStats,
    validateServerConfig,
    getDefaultServerConfig,
    reset
  }
})
