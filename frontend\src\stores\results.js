import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { sessionApi } from '@/utils/api'
import { ElMessage } from 'element-plus'

export const useResultsStore = defineStore('results', () => {
  // 状态
  const sessions = ref([])
  const currentSession = ref(null)
  const sessionMessages = ref([])
  const sessionTasks = ref([])
  const loading = ref(false)
  
  // 计算属性
  const sessionCount = computed(() => sessions.value.length)
  const completedSessions = computed(() =>
    sessions.value.filter(s => s.status === 'completed')
  )
  const runningSessions = computed(() =>
    sessions.value.filter(s => s.status === 'running')
  )
  
  // 获取所有会话
  const fetchSessions = async (params = {}) => {
    loading.value = true
    try {
      const data = await sessionApi.getSessions(params)
      if (params.append) {
        sessions.value.push(...data)
      } else {
        sessions.value = data
      }
      return data
    } catch (error) {
      console.error('获取会话列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取会话详情
  const fetchSession = async (sessionId) => {
    loading.value = true
    try {
      const data = await sessionApi.getSession(sessionId)
      currentSession.value = data
      
      // 更新会话列表中的会话
      const index = sessions.value.findIndex(s => s.id === sessionId)
      if (index > -1) {
        sessions.value[index] = data
      }
      
      return data
    } catch (error) {
      console.error('获取会话详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取会话消息
  const fetchSessionMessages = async (sessionId) => {
    loading.value = true
    try {
      const data = await sessionApi.getSessionMessages(sessionId)
      sessionMessages.value = data
      return data
    } catch (error) {
      console.error('获取会话消息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取会话任务
  const fetchSessionTasks = async (sessionId) => {
    loading.value = true
    try {
      const data = await sessionApi.getSessionTasks(sessionId)
      sessionTasks.value = data
      return data
    } catch (error) {
      console.error('获取会话任务失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 删除会话
  const deleteSession = async (sessionId) => {
    loading.value = true
    try {
      await sessionApi.deleteSession(sessionId)
      sessions.value = sessions.value.filter(s => s.id !== sessionId)
      if (currentSession.value?.id === sessionId) {
        currentSession.value = null
        sessionMessages.value = []
        sessionTasks.value = []
      }
      ElMessage.success('会话删除成功')
    } catch (error) {
      console.error('删除会话失败:', error)
      ElMessage.error('删除会话失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 根据ID查找会话
  const findSessionById = (sessionId) => {
    return sessions.value.find(s => s.id === sessionId)
  }
  
  // 获取会话统计信息
  const getSessionStats = () => {
    return {
      total: sessions.value.length,
      running: runningSessions.value.length,
      completed: completedSessions.value.length,
      failed: sessions.value.filter(s => s.status === 'failed').length,
      cancelled: sessions.value.filter(s => s.status === 'cancelled').length
    }
  }
  
  // 获取会话执行时长
  const getSessionDuration = (session) => {
    if (!session.started_at) return 0
    
    const endTime = session.completed_at || new Date().toISOString()
    const startTime = session.started_at
    
    return Math.floor((new Date(endTime) - new Date(startTime)) / 1000)
  }
  
  // 更新会话状态
  const updateSessionStatus = (sessionId, status, result = null) => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      session.status = status
      if (result) {
        session.result = result
      }
      if (status === 'completed' || status === 'failed') {
        session.completed_at = new Date().toISOString()
      }
    }
    
    // 如果是当前会话，也更新当前会话
    if (currentSession.value?.id === sessionId) {
      currentSession.value.status = status
      if (result) {
        currentSession.value.result = result
      }
      if (status === 'completed' || status === 'failed') {
        currentSession.value.completed_at = new Date().toISOString()
      }
    }
  }
  
  // 添加会话消息
  const addSessionMessage = (message) => {
    sessionMessages.value.push(message)
  }
  
  // 更新会话任务
  const updateSessionTask = (taskId, updates) => {
    const task = sessionTasks.value.find(t => t.id === taskId)
    if (task) {
      Object.assign(task, updates)
    }
  }
  
  // 获取会话结果摘要
  const getSessionResultSummary = (session) => {
    if (!session.result) return null
    
    const summary = {
      success: session.status === 'completed',
      duration: getSessionDuration(session),
      taskCount: session.task_count || 0,
      messageCount: session.message_count || 0
    }
    
    if (session.result.output) {
      summary.hasOutput = true
      summary.outputType = typeof session.result.output
    }
    
    if (session.result.error) {
      summary.hasError = true
      summary.errorMessage = session.result.error
    }
    
    return summary
  }
  
  // 格式化会话结果
  const formatSessionResult = (result) => {
    if (!result) return null
    
    // 如果结果是字符串，尝试解析为JSON
    if (typeof result === 'string') {
      try {
        return JSON.parse(result)
      } catch (error) {
        return { content: result }
      }
    }
    
    return result
  }
  
  // 清空当前会话数据
  const clearCurrentSession = () => {
    currentSession.value = null
    sessionMessages.value = []
    sessionTasks.value = []
  }
  
  // 重置状态
  const reset = () => {
    sessions.value = []
    currentSession.value = null
    sessionMessages.value = []
    sessionTasks.value = []
    loading.value = false
  }
  
  return {
    // 状态
    sessions,
    currentSession,
    sessionMessages,
    sessionTasks,
    loading,
    
    // 计算属性
    sessionCount,
    completedSessions,
    runningSessions,
    
    // 方法
    fetchSessions,
    fetchSession,
    fetchSessionMessages,
    fetchSessionTasks,
    deleteSession,
    findSessionById,
    getSessionStats,
    getSessionDuration,
    updateSessionStatus,
    addSessionMessage,
    updateSessionTask,
    getSessionResultSummary,
    formatSessionResult,
    clearCurrentSession,
    reset
  }
})
