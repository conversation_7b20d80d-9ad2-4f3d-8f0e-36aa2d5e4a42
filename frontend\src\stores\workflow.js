import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { workflowApi } from '@/utils/api'
import { ElMessage } from 'element-plus'

export const useWorkflowStore = defineStore('workflow', () => {
  // 状态
  const workflows = ref([])
  const currentWorkflow = ref(null)
  const executionSessions = ref([])
  const loading = ref(false)
  
  // 计算属性
  const workflowCount = computed(() => workflows.value.length)
  const activeWorkflows = computed(() => 
    workflows.value.filter(w => w.is_active)
  )
  
  // 获取所有工作流
  const fetchWorkflows = async () => {
    loading.value = true
    try {
      const data = await workflowApi.getWorkflows()
      workflows.value = data
      return data
    } catch (error) {
      console.error('获取工作流列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取工作流详情
  const fetchWorkflow = async (workflowId) => {
    loading.value = true
    try {
      const data = await workflowApi.getWorkflow(workflowId)
      currentWorkflow.value = data
      return data
    } catch (error) {
      console.error('获取工作流详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 创建工作流
  const createWorkflow = async (workflowData) => {
    loading.value = true
    try {
      const data = await workflowApi.createWorkflow(workflowData)
      workflows.value.push(data)
      ElMessage.success('工作流创建成功')
      return data
    } catch (error) {
      console.error('创建工作流失败:', error)
      ElMessage.error('创建工作流失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 更新工作流
  const updateWorkflow = async (workflowId, workflowData) => {
    loading.value = true
    try {
      const data = await workflowApi.updateWorkflow(workflowId, workflowData)
      const index = workflows.value.findIndex(w => w.id === workflowId)
      if (index > -1) {
        workflows.value[index] = data
      }
      if (currentWorkflow.value?.id === workflowId) {
        currentWorkflow.value = data
      }
      ElMessage.success('工作流更新成功')
      return data
    } catch (error) {
      console.error('更新工作流失败:', error)
      ElMessage.error('更新工作流失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 删除工作流
  const deleteWorkflow = async (workflowId) => {
    loading.value = true
    try {
      await workflowApi.deleteWorkflow(workflowId)
      workflows.value = workflows.value.filter(w => w.id !== workflowId)
      if (currentWorkflow.value?.id === workflowId) {
        currentWorkflow.value = null
      }
      ElMessage.success('工作流删除成功')
    } catch (error) {
      console.error('删除工作流失败:', error)
      ElMessage.error('删除工作流失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 执行工作流
  const executeWorkflow = async (workflowId, inputData) => {
    loading.value = true
    try {
      const data = await workflowApi.executeWorkflow(workflowId, inputData)
      
      // 添加到执行会话列表
      executionSessions.value.push({
        session_id: data.session_id,
        workflow_id: workflowId,
        status: 'running',
        created_at: new Date().toISOString(),
        input_data: inputData
      })
      
      ElMessage.success('工作流开始执行')
      return data
    } catch (error) {
      console.error('执行工作流失败:', error)
      ElMessage.error('执行工作流失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取执行状态
  const getExecutionStatus = async (sessionId) => {
    try {
      const data = await workflowApi.getExecutionStatus(sessionId)
      
      // 更新执行会话状态
      const session = executionSessions.value.find(s => s.session_id === sessionId)
      if (session) {
        Object.assign(session, data)
      }
      
      return data
    } catch (error) {
      console.error('获取执行状态失败:', error)
      throw error
    }
  }
  
  // 清空当前工作流
  const clearCurrentWorkflow = () => {
    currentWorkflow.value = null
  }
  
  // 根据ID查找工作流
  const findWorkflowById = (workflowId) => {
    return workflows.value.find(w => w.id === workflowId)
  }
  
  // 获取工作流步骤
  const getWorkflowSteps = (workflowId) => {
    const workflow = findWorkflowById(workflowId)
    return workflow?.steps || []
  }
  
  // 验证工作流配置
  const validateWorkflow = (workflow) => {
    const errors = []
    
    if (!workflow.name) {
      errors.push('工作流名称不能为空')
    }
    
    if (!workflow.steps || workflow.steps.length === 0) {
      errors.push('工作流至少需要一个步骤')
    }
    
    // 验证步骤配置
    workflow.steps?.forEach((step, index) => {
      if (!step.step_id) {
        errors.push(`步骤${index + 1}缺少ID`)
      }
      
      if (step.type === 'agent' && !step.agent) {
        errors.push(`步骤${index + 1}缺少智能体配置`)
      }
      
      if (step.type === 'branch' && !step.condition) {
        errors.push(`步骤${index + 1}缺少分支条件`)
      }
    })
    
    return errors
  }
  
  // 重置状态
  const reset = () => {
    workflows.value = []
    currentWorkflow.value = null
    executionSessions.value = []
    loading.value = false
  }
  
  return {
    // 状态
    workflows,
    currentWorkflow,
    executionSessions,
    loading,
    
    // 计算属性
    workflowCount,
    activeWorkflows,
    
    // 方法
    fetchWorkflows,
    fetchWorkflow,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    executeWorkflow,
    getExecutionStatus,
    clearCurrentWorkflow,
    findWorkflowById,
    getWorkflowSteps,
    validateWorkflow,
    reset
  }
})
