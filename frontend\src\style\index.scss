// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

// 页面容器
.page-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

// 卡片样式增强
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 16px 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: var(--el-fill-color-lighter);
    }
  }
}

// 按钮样式增强
.el-button {
  border-radius: 6px;
  
  &.is-circle {
    border-radius: 50%;
  }
}

// 状态标签
.status-tag {
  &.success {
    background-color: var(--el-color-success-light-9);
    color: var(--el-color-success);
    border: 1px solid var(--el-color-success-light-8);
  }
  
  &.warning {
    background-color: var(--el-color-warning-light-9);
    color: var(--el-color-warning);
    border: 1px solid var(--el-color-warning-light-8);
  }
  
  &.danger {
    background-color: var(--el-color-danger-light-9);
    color: var(--el-color-danger);
    border: 1px solid var(--el-color-danger-light-8);
  }
  
  &.info {
    background-color: var(--el-color-info-light-9);
    color: var(--el-color-info);
    border: 1px solid var(--el-color-info-light-8);
  }
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  
  .loading-text {
    margin-left: 10px;
    color: var(--el-text-color-regular);
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--el-text-color-secondary);
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .el-card .el-card__body {
    padding: 15px;
  }
  
  .overview-cards {
    .el-col {
      margin-bottom: 10px;
    }
  }
}

// 暗色主题适配
.dark {
  .el-card {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }
  
  .overview-card {
    .card-icon {
      opacity: 0.9;
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 特殊组件样式
.workflow-designer {
  height: calc(100vh - 120px);
  
  .designer-toolbar {
    height: 60px;
    border-bottom: 1px solid var(--el-border-color-light);
    padding: 0 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .designer-canvas {
    height: calc(100% - 60px);
    position: relative;
    overflow: hidden;
  }
}

.session-detail {
  .timeline-container {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .message-item {
    margin-bottom: 15px;
    
    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      
      .sender {
        font-weight: 600;
        color: var(--el-color-primary);
      }
      
      .timestamp {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .message-content {
      background-color: var(--el-fill-color-lighter);
      padding: 10px;
      border-radius: 6px;
      border-left: 3px solid var(--el-color-primary);
    }
  }
}
