import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const message = error.response?.data?.detail || error.message || '请求失败'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 工作流API
export const workflowApi = {
  // 获取所有工作流
  getWorkflows: () => api.get('/api/v1/workflows'),
  
  // 获取工作流详情
  getWorkflow: (workflowId) => api.get(`/api/v1/workflows/${workflowId}`),
  
  // 创建工作流
  createWorkflow: (data) => api.post('/api/v1/workflows', data),
  
  // 更新工作流
  updateWorkflow: (workflowId, data) => api.put(`/api/v1/workflows/${workflowId}`, data),
  
  // 删除工作流
  deleteWorkflow: (workflowId) => api.delete(`/api/v1/workflows/${workflowId}`),
  
  // 执行工作流
  executeWorkflow: (workflowId, inputData) => 
    api.post(`/api/v1/workflows/${workflowId}/execute`, { input_data: inputData }),
  
  // 获取工作流执行状态
  getExecutionStatus: (sessionId) => api.get(`/api/v1/sessions/${sessionId}/status`)
}

// 智能体API
export const agentApi = {
  // 获取所有智能体
  getAgents: () => api.get('/api/v1/agents'),
  
  // 获取智能体详情
  getAgent: (agentId) => api.get(`/api/v1/agents/${agentId}`),
  
  // 创建智能体任务
  createTask: (agentId, taskData) => api.post(`/api/v1/agents/${agentId}/tasks`, taskData),
  
  // 获取智能体任务列表
  getTasks: (agentId, params) => api.get(`/api/v1/agents/${agentId}/tasks`, { params }),
  
  // 获取任务详情
  getTask: (agentId, taskId) => api.get(`/api/v1/agents/${agentId}/tasks/${taskId}`),
  
  // 取消任务
  cancelTask: (agentId, taskId) => api.post(`/api/v1/agents/${agentId}/tasks/${taskId}/cancel`)
}

// 会话API
export const sessionApi = {
  // 获取所有会话
  getSessions: (params) => api.get('/api/v1/sessions', { params }),
  
  // 获取会话详情
  getSession: (sessionId) => api.get(`/api/v1/sessions/${sessionId}`),
  
  // 获取会话消息
  getSessionMessages: (sessionId) => api.get(`/api/v1/sessions/${sessionId}/messages`),
  
  // 获取会话任务
  getSessionTasks: (sessionId) => api.get(`/api/v1/sessions/${sessionId}/tasks`),
  
  // 删除会话
  deleteSession: (sessionId) => api.delete(`/api/v1/sessions/${sessionId}`)
}

// MCP API
export const mcpApi = {
  // 获取MCP服务器列表
  getServers: () => api.get('/api/v1/mcp/servers'),
  
  // 创建MCP服务器
  createServer: (data) => api.post('/api/v1/mcp/servers', data),
  
  // 更新MCP服务器
  updateServer: (serverId, data) => api.put(`/api/v1/mcp/servers/${serverId}`, data),
  
  // 删除MCP服务器
  deleteServer: (serverId) => api.delete(`/api/v1/mcp/servers/${serverId}`),
  
  // 测试MCP服务器连接
  testServer: (serverId) => api.post(`/api/v1/mcp/servers/${serverId}/test`),
  
  // 启用/禁用MCP服务器
  toggleServer: (serverId, isActive) => 
    api.post(`/api/v1/mcp/servers/${serverId}/toggle`, { is_active: isActive }),
  
  // 调用MCP服务器功能
  callServer: (serverId, capability, parameters) =>
    api.post(`/api/v1/mcp/servers/${serverId}/call`, { capability, parameters }),
  
  // 获取MCP服务器能力
  getServerCapabilities: (serverId) => api.get(`/api/v1/mcp/servers/${serverId}/capabilities`)
}

// 系统API
export const systemApi = {
  // 获取系统状态
  getStatus: () => api.get('/api/v1/system/status'),
  
  // 获取系统设置
  getSettings: () => api.get('/api/v1/system/settings'),
  
  // 保存系统设置
  saveSettings: (data) => api.post('/api/v1/system/settings', data),
  
  // 测试千问连接
  testQwenConnection: (config) => api.post('/api/v1/system/test-qwen', config),
  
  // 获取系统日志
  getLogs: (params) => api.get('/api/v1/system/logs', { params }),
  
  // 清空系统日志
  clearLogs: () => api.delete('/api/v1/system/logs')
}

export default api
