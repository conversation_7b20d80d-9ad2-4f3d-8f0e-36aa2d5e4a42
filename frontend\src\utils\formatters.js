import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 时间格式化
export const formatTime = (time, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!time) return '-'
  return dayjs(time).format(format)
}

// 相对时间
export const formatRelativeTime = (time) => {
  if (!time) return '-'
  return dayjs(time).fromNow()
}

// 持续时间格式化
export const formatDuration = (seconds) => {
  if (!seconds || seconds < 0) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 文件大小格式化
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 数字格式化
export const formatNumber = (num, precision = 2) => {
  if (num === null || num === undefined) return '-'
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(precision) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(precision) + 'K'
  } else {
    return num.toString()
  }
}

// 百分比格式化
export const formatPercentage = (value, total, precision = 1) => {
  if (!total || total === 0) return '0%'
  return ((value / total) * 100).toFixed(precision) + '%'
}

// JSON格式化
export const formatJSON = (obj, indent = 2) => {
  try {
    return JSON.stringify(obj, null, indent)
  } catch (error) {
    return String(obj)
  }
}

// 状态文本格式化
export const formatStatus = (status) => {
  const statusMap = {
    'pending': '等待中',
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消',
    'paused': '已暂停',
    'success': '成功',
    'error': '错误',
    'warning': '警告',
    'info': '信息',
    'connected': '已连接',
    'disconnected': '已断开',
    'active': '活跃',
    'inactive': '非活跃'
  }
  
  return statusMap[status] || status
}

// 智能体类型格式化
export const formatAgentType = (type) => {
  const typeMap = {
    'intent_recognizer': '意图识别智能体',
    'task_decomposer': '任务分解智能体',
    'code_generator': '代码生成智能体',
    'market_researcher': '市场调研智能体',
    'result_validator': '结果验证智能体'
  }
  
  return typeMap[type] || type
}

// MCP服务类型格式化
export const formatMCPType = (type) => {
  const typeMap = {
    'stdio': '本地stdio',
    'http': 'HTTP服务',
    'websocket': 'WebSocket',
    'file_operations': '文件操作',
    'web_search': '网络搜索',
    'data_analysis': '数据分析',
    'git_operations': 'Git操作',
    'database': '数据库',
    'api_client': 'API客户端',
    'custom': '自定义'
  }
  
  return typeMap[type] || type
}

// 工作流步骤类型格式化
export const formatStepType = (type) => {
  const typeMap = {
    'agent': '智能体',
    'branch': '分支',
    'loop': '循环',
    'parallel': '并行',
    'validation': '验证',
    'condition': '条件',
    'merge': '合并'
  }
  
  return typeMap[type] || type
}

// 日志级别格式化
export const formatLogLevel = (level) => {
  const levelMap = {
    'DEBUG': '调试',
    'INFO': '信息',
    'WARNING': '警告',
    'ERROR': '错误',
    'CRITICAL': '严重错误'
  }
  
  return levelMap[level] || level
}

// 错误信息格式化
export const formatError = (error) => {
  if (typeof error === 'string') {
    return error
  }
  
  if (error?.message) {
    return error.message
  }
  
  if (error?.detail) {
    return error.detail
  }
  
  return '未知错误'
}

// 截断文本
export const truncateText = (text, maxLength = 100, suffix = '...') => {
  if (!text || text.length <= maxLength) {
    return text
  }
  
  return text.substring(0, maxLength) + suffix
}

// 高亮搜索关键词
export const highlightKeyword = (text, keyword) => {
  if (!text || !keyword) {
    return text
  }
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 验证JSON格式
export const isValidJSON = (str) => {
  try {
    JSON.parse(str)
    return true
  } catch (error) {
    return false
  }
}

// 生成随机ID
export const generateId = (prefix = 'id') => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 深拷贝对象
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

export default {
  formatTime,
  formatRelativeTime,
  formatDuration,
  formatFileSize,
  formatNumber,
  formatPercentage,
  formatJSON,
  formatStatus,
  formatAgentType,
  formatMCPType,
  formatStepType,
  formatLogLevel,
  formatError,
  truncateText,
  highlightKeyword,
  isValidJSON,
  generateId,
  deepClone
}
