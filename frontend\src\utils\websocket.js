import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.isConnected = ref(false)
    this.listeners = reactive({})
    this.messageQueue = []
  }

  connect(url = null) {
    const wsUrl = url || `ws://localhost:8000/ws`
    
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.isConnected.value = true
        this.reconnectAttempts = 0
        
        // 发送队列中的消息
        while (this.messageQueue.length > 0) {
          const message = this.messageQueue.shift()
          this.send(message)
        }
      }
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(data)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }
      
      this.ws.onclose = (event) => {
        console.log('WebSocket连接已关闭', event.code, event.reason)
        this.isConnected.value = false
        
        // 如果不是主动关闭，尝试重连
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnect()
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        ElMessage.error('WebSocket连接错误')
      }
      
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      ElMessage.error('无法建立WebSocket连接')
    }
  }

  reconnect() {
    this.reconnectAttempts++
    console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, '主动断开连接')
      this.ws = null
    }
  }

  send(message) {
    if (this.isConnected.value && this.ws) {
      try {
        this.ws.send(JSON.stringify(message))
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
      }
    } else {
      // 如果未连接，将消息加入队列
      this.messageQueue.push(message)
    }
  }

  handleMessage(data) {
    const { type, payload } = data
    
    // 触发对应类型的监听器
    if (this.listeners[type]) {
      this.listeners[type].forEach(callback => {
        try {
          callback(payload)
        } catch (error) {
          console.error(`处理WebSocket消息失败 (${type}):`, error)
        }
      })
    }
  }

  on(type, callback) {
    if (!this.listeners[type]) {
      this.listeners[type] = []
    }
    this.listeners[type].push(callback)
  }

  off(type, callback) {
    if (this.listeners[type]) {
      const index = this.listeners[type].indexOf(callback)
      if (index > -1) {
        this.listeners[type].splice(index, 1)
      }
    }
  }

  // 订阅会话更新
  subscribeToSession(sessionId) {
    this.send({
      type: 'subscribe_session',
      session_id: sessionId
    })
  }

  // 取消订阅会话
  unsubscribeFromSession(sessionId) {
    this.send({
      type: 'unsubscribe_session',
      session_id: sessionId
    })
  }

  // 订阅任务更新
  subscribeToTask(taskId) {
    this.send({
      type: 'subscribe_task',
      task_id: taskId
    })
  }

  // 取消订阅任务
  unsubscribeFromTask(taskId) {
    this.send({
      type: 'unsubscribe_task',
      task_id: taskId
    })
  }

  // 订阅系统状态
  subscribeToSystemStatus() {
    this.send({
      type: 'subscribe_system_status'
    })
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager()

// 导出便捷函数
export const useWebSocket = () => {
  return {
    isConnected: wsManager.isConnected,
    connect: wsManager.connect.bind(wsManager),
    disconnect: wsManager.disconnect.bind(wsManager),
    send: wsManager.send.bind(wsManager),
    on: wsManager.on.bind(wsManager),
    off: wsManager.off.bind(wsManager),
    subscribeToSession: wsManager.subscribeToSession.bind(wsManager),
    unsubscribeFromSession: wsManager.unsubscribeFromSession.bind(wsManager),
    subscribeToTask: wsManager.subscribeToTask.bind(wsManager),
    unsubscribeFromTask: wsManager.unsubscribeFromTask.bind(wsManager),
    subscribeToSystemStatus: wsManager.subscribeToSystemStatus.bind(wsManager)
  }
}

export default wsManager
