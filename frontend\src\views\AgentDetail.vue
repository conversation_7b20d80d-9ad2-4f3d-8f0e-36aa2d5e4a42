<template>
  <div class="agent-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-row :gutter="20" align="middle">
        <el-col :span="18">
          <div class="header-left">
            <el-button 
              type="text" 
              @click="$router.go(-1)"
              class="back-btn"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div class="agent-info">
              <h1>{{ agent.name }}</h1>
              <div class="agent-meta">
                <el-tag :type="getStatusType(agent.status)">{{ agent.status }}</el-tag>
                <el-tag type="info">{{ agent.type }}</el-tag>
                <span class="meta-item">创建时间：{{ agent.created_at }}</span>
                <span class="meta-item">最后活动：{{ agent.last_active }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="header-actions">
            <el-button type="primary" @click="editAgent">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button 
              :type="agent.status === '运行中' ? 'danger' : 'success'"
              @click="toggleAgent"
            >
              <el-icon><VideoPlay v-if="agent.status !== '运行中'" /><VideoPause v-else /></el-icon>
              {{ agent.status === '运行中' ? '停止' : '启动' }}
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button>
                更多操作
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="duplicate">复制智能体</el-dropdown-item>
                  <el-dropdown-item command="export">导出配置</el-dropdown-item>
                  <el-dropdown-item command="test">测试对话</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除智能体</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 智能体详情内容 -->
    <div class="agent-content">
      <el-row :gutter="20">
        <!-- 左侧：基本信息和配置 -->
        <el-col :span="16">
          <!-- 基本信息 -->
          <el-card class="info-card">
            <template #header>
              <span>基本信息</span>
            </template>
            <div class="info-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <label>智能体ID：</label>
                    <span>{{ agent.id }}</span>
                  </div>
                  <div class="info-item">
                    <label>名称：</label>
                    <span>{{ agent.name }}</span>
                  </div>
                  <div class="info-item">
                    <label>类型：</label>
                    <span>{{ agent.type }}</span>
                  </div>
                  <div class="info-item">
                    <label>版本：</label>
                    <span>{{ agent.version }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>状态：</label>
                    <el-tag :type="getStatusType(agent.status)">{{ agent.status }}</el-tag>
                  </div>
                  <div class="info-item">
                    <label>优先级：</label>
                    <el-rate v-model="agent.priority" disabled show-score />
                  </div>
                  <div class="info-item">
                    <label>执行次数：</label>
                    <span>{{ agent.execution_count }}</span>
                  </div>
                  <div class="info-item">
                    <label>成功率：</label>
                    <span>{{ agent.success_rate }}%</span>
                  </div>
                </el-col>
              </el-row>
              <div class="info-item full-width">
                <label>描述：</label>
                <span>{{ agent.description || '暂无描述' }}</span>
              </div>
            </div>
          </el-card>

          <!-- 配置信息 -->
          <el-card class="config-card">
            <template #header>
              <div class="card-header">
                <span>配置信息</span>
                <el-button size="small" @click="editConfig">
                  <el-icon><Setting /></el-icon>
                  编辑配置
                </el-button>
              </div>
            </template>
            <div class="config-content">
              <el-tabs v-model="activeConfigTab">
                <el-tab-pane label="模型配置" name="model">
                  <div class="config-section">
                    <div class="config-item">
                      <label>模型提供商：</label>
                      <span>{{ agent.config.model_provider }}</span>
                    </div>
                    <div class="config-item">
                      <label>模型名称：</label>
                      <span>{{ agent.config.model_name }}</span>
                    </div>
                    <div class="config-item">
                      <label>温度参数：</label>
                      <span>{{ agent.config.temperature }}</span>
                    </div>
                    <div class="config-item">
                      <label>最大令牌数：</label>
                      <span>{{ agent.config.max_tokens }}</span>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="提示词" name="prompt">
                  <div class="prompt-content">
                    <h4>系统提示词</h4>
                    <el-input
                      type="textarea"
                      :rows="6"
                      v-model="agent.config.system_prompt"
                      readonly
                      placeholder="暂无系统提示词"
                    />
                    <h4 style="margin-top: 20px;">用户提示词模板</h4>
                    <el-input
                      type="textarea"
                      :rows="4"
                      v-model="agent.config.user_prompt_template"
                      readonly
                      placeholder="暂无用户提示词模板"
                    />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="工具配置" name="tools">
                  <div class="tools-content">
                    <div v-if="agent.config.tools && agent.config.tools.length > 0">
                      <div 
                        v-for="tool in agent.config.tools" 
                        :key="tool.name"
                        class="tool-item"
                      >
                        <div class="tool-header">
                          <el-icon><Tools /></el-icon>
                          <span class="tool-name">{{ tool.name }}</span>
                          <el-tag size="small" :type="tool.enabled ? 'success' : 'info'">
                            {{ tool.enabled ? '已启用' : '已禁用' }}
                          </el-tag>
                        </div>
                        <div class="tool-desc">{{ tool.description }}</div>
                      </div>
                    </div>
                    <div v-else class="no-tools">
                      <el-empty description="暂无配置工具" />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>

          <!-- 对话测试 -->
          <el-card class="chat-card">
            <template #header>
              <div class="card-header">
                <span>对话测试</span>
                <el-button size="small" @click="clearChat">
                  <el-icon><Delete /></el-icon>
                  清空对话
                </el-button>
              </div>
            </template>
            <div class="chat-content">
              <div class="chat-messages" ref="chatMessages">
                <div 
                  v-for="message in chatMessages" 
                  :key="message.id"
                  class="message-item"
                  :class="{ 'user-message': message.role === 'user', 'agent-message': message.role === 'assistant' }"
                >
                  <div class="message-avatar">
                    <el-avatar :size="32">
                      <el-icon v-if="message.role === 'user'"><User /></el-icon>
                      <el-icon v-else><Robot /></el-icon>
                    </el-avatar>
                  </div>
                  <div class="message-content">
                    <div class="message-text">{{ message.content }}</div>
                    <div class="message-time">{{ message.timestamp }}</div>
                  </div>
                </div>
              </div>
              <div class="chat-input">
                <el-input
                  v-model="chatInput"
                  placeholder="输入消息测试智能体..."
                  @keyup.enter="sendMessage"
                  :disabled="chatLoading"
                >
                  <template #append>
                    <el-button 
                      @click="sendMessage" 
                      :loading="chatLoading"
                      type="primary"
                    >
                      发送
                    </el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：统计和活动 -->
        <el-col :span="8">
          <!-- 性能统计 -->
          <el-card class="stats-card">
            <template #header>
              <span>性能统计</span>
            </template>
            <div class="stats-content">
              <div class="stat-item">
                <div class="stat-value">{{ agent.execution_count }}</div>
                <div class="stat-label">总执行次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ agent.success_rate }}%</div>
                <div class="stat-label">成功率</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ agent.avg_response_time }}ms</div>
                <div class="stat-label">平均响应时间</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ agent.error_count }}</div>
                <div class="stat-label">错误次数</div>
              </div>
            </div>
          </el-card>

          <!-- 最近活动 -->
          <el-card class="activity-card">
            <template #header>
              <span>最近活动</span>
            </template>
            <div class="activity-content">
              <el-timeline>
                <el-timeline-item
                  v-for="activity in recentActivities"
                  :key="activity.id"
                  :timestamp="activity.timestamp"
                  :type="getActivityType(activity.type)"
                >
                  <div class="activity-item">
                    <div class="activity-title">{{ activity.title }}</div>
                    <div class="activity-desc">{{ activity.description }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>

          <!-- 关联工作流 -->
          <el-card class="workflows-card">
            <template #header>
              <span>关联工作流</span>
            </template>
            <div class="workflows-content">
              <div v-if="relatedWorkflows.length > 0">
                <div 
                  v-for="workflow in relatedWorkflows" 
                  :key="workflow.id"
                  class="workflow-item"
                  @click="viewWorkflow(workflow.id)"
                >
                  <div class="workflow-info">
                    <div class="workflow-name">{{ workflow.name }}</div>
                    <div class="workflow-desc">{{ workflow.description }}</div>
                  </div>
                  <el-tag :type="getStatusType(workflow.status)">{{ workflow.status }}</el-tag>
                </div>
              </div>
              <div v-else class="no-workflows">
                <el-empty description="暂无关联工作流" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 配置编辑对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="编辑智能体配置"
      width="70%"
    >
      <div class="config-edit-content">
        <!-- 配置编辑表单内容 -->
        <el-form :model="editingConfig" label-width="120px">
          <el-form-item label="模型提供商">
            <el-select v-model="editingConfig.model_provider">
              <el-option label="OpenAI" value="openai" />
              <el-option label="通义千问" value="qwen" />
              <el-option label="文心一言" value="ernie" />
            </el-select>
          </el-form-item>
          <el-form-item label="模型名称">
            <el-input v-model="editingConfig.model_name" />
          </el-form-item>
          <el-form-item label="温度参数">
            <el-slider v-model="editingConfig.temperature" :min="0" :max="2" :step="0.1" />
          </el-form-item>
          <el-form-item label="最大令牌数">
            <el-input-number v-model="editingConfig.max_tokens" :min="1" :max="4096" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  VideoPlay,
  VideoPause,
  ArrowDown,
  Setting,
  Tools,
  Delete,
  User,
  Robot
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const agent = reactive({
  id: route.params.id,
  name: '客户服务智能体',
  type: '对话助手',
  status: '运行中',
  version: 'v1.2.0',
  priority: 4,
  description: '专门处理客户咨询和服务请求的智能体，具备多轮对话能力和情感理解',
  execution_count: 1247,
  success_rate: 96.8,
  avg_response_time: 850,
  error_count: 12,
  created_at: '2024-01-10 09:30:00',
  last_active: '2024-01-20 15:45:00',
  config: {
    model_provider: 'qwen',
    model_name: 'qwen-turbo',
    temperature: 0.7,
    max_tokens: 2048,
    system_prompt: '你是一个专业的客户服务助手，请以友好、耐心、专业的态度回答用户的问题。你需要：\n1. 仔细理解用户的问题\n2. 提供准确、有用的信息\n3. 保持礼貌和专业的语调\n4. 如果无法解决问题，及时转接人工客服',
    user_prompt_template: '用户问题：{user_input}\n\n请根据上述问题提供专业的回答。',
    tools: [
      {
        name: 'knowledge_search',
        description: '搜索知识库获取相关信息',
        enabled: true
      },
      {
        name: 'order_query',
        description: '查询订单状态和详情',
        enabled: true
      },
      {
        name: 'human_handoff',
        description: '转接人工客服',
        enabled: true
      }
    ]
  }
})

const activeConfigTab = ref('model')
const configDialogVisible = ref(false)
const editingConfig = ref({})
const chatInput = ref('')
const chatLoading = ref(false)
const chatMessages = ref([
  {
    id: 1,
    role: 'assistant',
    content: '您好！我是客户服务智能体，很高兴为您服务。请问有什么可以帮助您的吗？',
    timestamp: '14:30:00'
  }
])

const recentActivities = ref([
  {
    id: 1,
    title: '处理客户咨询',
    description: '成功回答了关于产品功能的问题',
    timestamp: '2024-01-20 15:45:00',
    type: 'success'
  },
  {
    id: 2,
    title: '转接人工客服',
    description: '复杂问题转接给人工客服处理',
    timestamp: '2024-01-20 15:30:00',
    type: 'info'
  },
  {
    id: 3,
    title: '知识库查询',
    description: '从知识库检索到相关解决方案',
    timestamp: '2024-01-20 15:15:00',
    type: 'success'
  },
  {
    id: 4,
    title: '响应超时',
    description: '处理请求时发生超时错误',
    timestamp: '2024-01-20 14:50:00',
    type: 'warning'
  }
])

const relatedWorkflows = ref([
  {
    id: 'wf001',
    name: '客户服务流程',
    description: '自动化客户问题处理和分类',
    status: '运行中'
  },
  {
    id: 'wf002',
    name: '订单处理流程',
    description: '订单查询和状态更新自动化',
    status: '运行中'
  }
])

// 计算属性和方法
const getStatusType = (status) => {
  const statusMap = {
    '运行中': 'success',
    '已停止': 'info',
    '错误': 'danger',
    '维护中': 'warning'
  }
  return statusMap[status] || 'info'
}

const getActivityType = (type) => {
  const typeMap = {
    'success': 'success',
    'info': 'primary',
    'warning': 'warning',
    'error': 'danger'
  }
  return typeMap[type] || 'info'
}

const editAgent = () => {
  ElMessage.info('智能体编辑功能开发中')
}

const toggleAgent = async () => {
  try {
    const action = agent.status === '运行中' ? '停止' : '启动'
    await ElMessageBox.confirm(
      `确定要${action}智能体吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    agent.status = agent.status === '运行中' ? '已停止' : '运行中'
    ElMessage.success(`智能体已${action}`)
  } catch {
    // 用户取消操作
  }
}

const handleCommand = async (command) => {
  switch (command) {
    case 'duplicate':
      ElMessage.success('智能体复制功能开发中')
      break
    case 'export':
      ElMessage.success('配置导出功能开发中')
      break
    case 'test':
      ElMessage.info('请在下方对话测试区域进行测试')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          '删除后无法恢复，确定要删除此智能体吗？',
          '确认删除',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        ElMessage.success('智能体删除功能开发中')
      } catch {
        // 用户取消删除
      }
      break
  }
}

const editConfig = () => {
  editingConfig.value = { ...agent.config }
  configDialogVisible.value = true
}

const saveConfig = () => {
  Object.assign(agent.config, editingConfig.value)
  configDialogVisible.value = false
  ElMessage.success('配置保存成功')
}

const sendMessage = async () => {
  if (!chatInput.value.trim()) return
  
  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: chatInput.value,
    timestamp: new Date().toLocaleTimeString()
  }
  
  chatMessages.value.push(userMessage)
  chatInput.value = ''
  chatLoading.value = true
  
  // 模拟智能体响应
  setTimeout(() => {
    const responses = [
      '感谢您的问题，我正在为您查找相关信息...',
      '根据您的描述，我建议您可以尝试以下解决方案...',
      '这是一个很好的问题，让我为您详细解答...',
      '我理解您的困扰，这种情况通常可以通过以下方式解决...'
    ]
    
    const agentMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      content: responses[Math.floor(Math.random() * responses.length)],
      timestamp: new Date().toLocaleTimeString()
    }
    
    chatMessages.value.push(agentMessage)
    chatLoading.value = false
    
    // 滚动到底部
    nextTick(() => {
      const chatContainer = document.querySelector('.chat-messages')
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight
      }
    })
  }, 1000 + Math.random() * 2000)
}

const clearChat = () => {
  chatMessages.value = [
    {
      id: 1,
      role: 'assistant',
      content: '您好！我是客户服务智能体，很高兴为您服务。请问有什么可以帮助您的吗？',
      timestamp: new Date().toLocaleTimeString()
    }
  ]
}

const viewWorkflow = (workflowId) => {
  router.push(`/workflows/${workflowId}`)
}

// 生命周期
onMounted(() => {
  // 初始化页面数据
})
</script>

<style scoped>
.agent-detail {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-btn {
  font-size: 16px;
  color: #409EFF;
}

.agent-info h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.agent-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #606266;
}

.meta-item {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.agent-content {
  gap: 20px;
}

.info-card,
.config-card,
.chat-card,
.stats-card,
.activity-card,
.workflows-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
}

.info-item.full-width label {
  margin-bottom: 5px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.config-section {
  padding: 10px 0;
}

.config-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.config-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 10px;
}

.prompt-content h4 {
  margin-bottom: 10px;
  color: #303133;
}

.tools-content {
  padding: 10px 0;
}

.tool-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
  background: #fafafa;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.tool-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.tool-desc {
  font-size: 14px;
  color: #606266;
}

.no-tools {
  text-align: center;
  padding: 20px;
}

.chat-content {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 15px;
  background: #fafafa;
}

.message-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
  gap: 10px;
}

.message-item.user-message {
  flex-direction: row-reverse;
}

.message-content {
  max-width: 70%;
}

.message-text {
  background: white;
  padding: 10px 15px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}

.user-message .message-text {
  background: #409EFF;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.stats-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 10px 0;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.activity-content {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  margin-bottom: 8px;
}

.activity-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 12px;
  color: #909399;
}

.workflows-content {
  max-height: 250px;
  overflow-y: auto;
}

.workflow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.workflow-item:hover {
  border-color: #409EFF;
  background: #ecf5ff;
}

.workflow-info {
  flex: 1;
}

.workflow-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.workflow-desc {
  font-size: 12px;
  color: #909399;
}

.no-workflows {
  text-align: center;
  padding: 20px;
}

.config-edit-content {
  padding: 20px 0;
}
</style>