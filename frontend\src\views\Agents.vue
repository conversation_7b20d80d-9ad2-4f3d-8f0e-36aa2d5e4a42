<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>智能体管理</span>
          <el-button type="primary" @click="refreshAgents">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table :data="agents" v-loading="loading" style="width: 100%">
        <el-table-column prop="agent_card.agent_id" label="智能体ID" width="200" show-overflow-tooltip />
        <el-table-column prop="agent_card.name" label="名称" width="200" />
        <el-table-column prop="agent_card.description" label="描述" show-overflow-tooltip />
        <el-table-column label="技能数量" width="100">
          <template #default="{ row }">
            {{ row.agent_card.skills?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.statistics.is_active ? 'success' : 'danger'">
              {{ row.statistics.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="成功率" width="100">
          <template #default="{ row }">
            {{ Math.round(row.statistics.success_rate * 100) }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewAgentDetail(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="testAgent(row)">
              测试
            </el-button>
            <el-button type="text" size="small" @click="viewAgentHealth(row)">
              健康检查
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 智能体详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="智能体详情" width="800px">
      <div v-if="selectedAgent">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="智能体ID">
            {{ selectedAgent.agent_card.agent_id }}
          </el-descriptions-item>
          <el-descriptions-item label="名称">
            {{ selectedAgent.agent_card.name }}
          </el-descriptions-item>
          <el-descriptions-item label="版本">
            {{ selectedAgent.agent_card.version }}
          </el-descriptions-item>
          <el-descriptions-item label="端点">
            {{ selectedAgent.agent_card.endpoint }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedAgent.agent_card.description }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">技能列表</h4>
        <el-table :data="selectedAgent.agent_card.skills" style="width: 100%">
          <el-table-column prop="name" label="技能名称" />
          <el-table-column prop="description" label="描述" show-overflow-tooltip />
          <el-table-column label="交互模式">
            <template #default="{ row }">
              <el-tag v-for="mode in row.interaction_modes" :key="mode" size="small" style="margin-right: 5px;">
                {{ mode }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <h4 style="margin: 20px 0 10px 0;">统计信息</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="运行中任务" :value="selectedAgent.statistics.running_tasks" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="总任务数" :value="selectedAgent.statistics.total_tasks" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="完成任务" :value="selectedAgent.statistics.completed_tasks" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="失败任务" :value="selectedAgent.statistics.failed_tasks" />
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 测试智能体对话框 -->
    <el-dialog v-model="testDialogVisible" title="测试智能体" width="600px">
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="技能名称">
          <el-select v-model="testForm.skill_name" placeholder="选择技能">
            <el-option
              v-for="skill in selectedAgent?.agent_card.skills || []"
              :key="skill.name"
              :label="skill.name"
              :value="skill.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="测试参数">
          <el-input
            v-model="testForm.parameters"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的参数"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="testDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeTest" :loading="testLoading">
          执行测试
        </el-button>
      </template>
    </el-dialog>

    <!-- 测试结果对话框 -->
    <el-dialog v-model="testResultDialogVisible" title="测试结果" width="700px">
      <div v-if="testResult">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="任务ID">
            {{ testResult.task_id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(testResult.status)">
              {{ getStatusText(testResult.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            {{ Math.round(testResult.progress * 100) }}%
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">执行结果</h4>
        <el-input
          v-model="testResultText"
          type="textarea"
          :rows="8"
          readonly
          style="font-family: monospace;"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { agentApi } from '@/api'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const agents = ref([])
const selectedAgent = ref(null)
const detailDialogVisible = ref(false)
const testDialogVisible = ref(false)
const testResultDialogVisible = ref(false)
const testLoading = ref(false)

const testForm = ref({
  skill_name: '',
  parameters: ''
})

const testResult = ref(null)
const testResultText = ref('')

// 方法
const loadAgents = async () => {
  loading.value = true
  try {
    const response = await agentApi.getAgents()
    agents.value = response.agents || []
  } catch (error) {
    console.error('加载智能体列表失败:', error)
    ElMessage.error('加载智能体列表失败')
  } finally {
    loading.value = false
  }
}

const refreshAgents = () => {
  loadAgents()
}

const viewAgentDetail = (agent) => {
  selectedAgent.value = agent
  detailDialogVisible.value = true
}

const testAgent = (agent) => {
  selectedAgent.value = agent
  testForm.value = {
    skill_name: '',
    parameters: '{\n  "user_input": "测试输入"\n}'
  }
  testDialogVisible.value = true
}

const viewAgentHealth = async (agent) => {
  try {
    const health = await agentApi.getAgentHealth(agent.agent_card.agent_id)
    const status = health.status === 'healthy' ? '健康' : '异常'
    const message = `智能体状态: ${status}\nLLM状态: ${health.llm_test_passed ? '正常' : '异常'}`
    ElMessage.success(message)
  } catch (error) {
    console.error('健康检查失败:', error)
    ElMessage.error('健康检查失败')
  }
}

const executeTest = async () => {
  if (!testForm.value.skill_name) {
    ElMessage.warning('请选择技能名称')
    return
  }

  testLoading.value = true
  try {
    // 解析参数
    let parameters = {}
    if (testForm.value.parameters.trim()) {
      parameters = JSON.parse(testForm.value.parameters)
    }

    // 创建任务
    const taskData = {
      task_id: `test_${Date.now()}`,
      skill_name: testForm.value.skill_name,
      parameters: parameters
    }

    const response = await agentApi.createTask(selectedAgent.value.agent_card.agent_id, taskData)
    
    // 等待任务完成
    await waitForTaskCompletion(taskData.task_id)
    
    testDialogVisible.value = false
  } catch (error) {
    console.error('测试执行失败:', error)
    if (error.message.includes('JSON')) {
      ElMessage.error('参数格式错误，请检查JSON格式')
    } else {
      ElMessage.error('测试执行失败')
    }
  } finally {
    testLoading.value = false
  }
}

const waitForTaskCompletion = async (taskId) => {
  const maxAttempts = 30
  let attempts = 0
  
  while (attempts < maxAttempts) {
    try {
      const taskResponse = await agentApi.getTask(taskId)
      testResult.value = taskResponse
      
      if (taskResponse.status === 'completed' || taskResponse.status === 'failed') {
        testResultText.value = JSON.stringify(taskResponse.result || taskResponse.error, null, 2)
        testResultDialogVisible.value = true
        break
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      attempts++
    } catch (error) {
      console.error('获取任务状态失败:', error)
      break
    }
  }
  
  if (attempts >= maxAttempts) {
    ElMessage.warning('任务执行超时')
  }
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return textMap[status] || status
}

// 生命周期
onMounted(() => {
  loadAgents()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
