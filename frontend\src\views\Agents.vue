<template>
  <div class="agents-container">
    <div class="page-header">
      <h1>智能体管理</h1>
      <p>管理和配置您的AI智能体</p>
    </div>
    
    <div class="toolbar">
      <el-button type="primary" @click="createAgent">
        <el-icon><Plus /></el-icon>
        创建智能体
      </el-button>
      <el-button @click="refreshAgents">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-input 
        v-model="searchText" 
        placeholder="搜索智能体..."
        style="width: 200px;"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="agents-grid">
      <el-card 
        v-for="agent in filteredAgents" 
        :key="agent.id" 
        class="agent-card"
      >
        <template #header>
          <div class="card-header">
            <div class="agent-info">
              <el-avatar :size="40" :src="agent.avatar">
                {{ agent.name.charAt(0) }}
              </el-avatar>
              <div>
                <h3>{{ agent.name }}</h3>
                <el-tag :type="getStatusType(agent.status)" size="small">
                  {{ getStatusText(agent.status) }}
                </el-tag>
              </div>
            </div>
            <el-dropdown @command="handleCommand">
              <el-button type="text">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'edit', agent}">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'clone', agent}">
                    克隆
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'export', agent}">
                    导出
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="{action: 'delete', agent}"
                    divided
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        
        <div class="agent-content">
          <p class="description">{{ agent.description }}</p>
          
          <div class="agent-details">
            <div class="detail-item">
              <span class="label">类型:</span>
              <span class="value">{{ agent.type }}</span>
            </div>
            <div class="detail-item">
              <span class="label">模型:</span>
              <span class="value">{{ agent.model }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatDate(agent.createdAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">最后使用:</span>
              <span class="value">{{ formatDate(agent.lastUsed) }}</span>
            </div>
          </div>
          
          <div class="agent-capabilities">
            <h4>能力标签</h4>
            <div class="capabilities-list">
              <el-tag 
                v-for="capability in agent.capabilities" 
                :key="capability"
                size="small"
                class="capability-tag"
              >
                {{ capability }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="card-actions">
            <el-button 
              size="small" 
              :disabled="agent.status !== 'active'"
              @click="testAgent(agent)"
            >
              测试
            </el-button>
            <el-button 
              size="small" 
              type="primary"
              :disabled="agent.status !== 'active'"
              @click="chatWithAgent(agent)"
            >
              对话
            </el-button>
            <el-button 
              size="small" 
              :type="agent.status === 'active' ? 'warning' : 'success'"
              @click="toggleAgentStatus(agent)"
            >
              {{ agent.status === 'active' ? '停用' : '启用' }}
            </el-button>
          </div>
        </template>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredAgents.length === 0" class="empty-state">
      <el-empty description="暂无智能体">
        <el-button type="primary" @click="createAgent">
          创建第一个智能体
        </el-button>
      </el-empty>
    </div>

    <!-- 创建/编辑智能体对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEditing ? '编辑智能体' : '创建智能体'"
      width="600px"
    >
      <el-form :model="agentForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="agentForm.name" placeholder="请输入智能体名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="agentForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入智能体描述"
          />
        </el-form-item>
        
        <el-form-item label="类型" prop="type">
          <el-select v-model="agentForm.type" placeholder="选择智能体类型">
            <el-option label="通用助手" value="general" />
            <el-option label="代码生成" value="code" />
            <el-option label="数据分析" value="analysis" />
            <el-option label="文档处理" value="document" />
            <el-option label="客服助手" value="customer" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模型" prop="model">
          <el-select v-model="agentForm.model" placeholder="选择AI模型">
            <el-option label="GPT-4" value="gpt-4" />
            <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
            <el-option label="Claude-3" value="claude-3" />
            <el-option label="通义千问" value="qwen" />
            <el-option label="文心一言" value="ernie" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="能力标签">
          <el-select 
            v-model="agentForm.capabilities" 
            multiple 
            placeholder="选择能力标签"
          >
            <el-option label="文本生成" value="text-generation" />
            <el-option label="代码编写" value="code-writing" />
            <el-option label="数据分析" value="data-analysis" />
            <el-option label="图像处理" value="image-processing" />
            <el-option label="语言翻译" value="translation" />
            <el-option label="问答系统" value="qa-system" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAgent">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, MoreFilled } from '@element-plus/icons-vue'

const searchText = ref('')
const dialogVisible = ref(false)
const isEditing = ref(false)
const formRef = ref(null)
const agents = ref([])

// 表单数据
const agentForm = ref({
  id: null,
  name: '',
  description: '',
  type: '',
  model: '',
  capabilities: []
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入智能体名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入智能体描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择智能体类型', trigger: 'change' }],
  model: [{ required: true, message: '请选择AI模型', trigger: 'change' }]
}

// 模拟数据
const mockAgents = [
  {
    id: 1,
    name: '通用助手',
    description: '能够处理各种通用任务的AI助手',
    type: '通用助手',
    model: 'GPT-4',
    status: 'active',
    capabilities: ['文本生成', '问答系统', '语言翻译'],
    createdAt: new Date('2024-01-10'),
    lastUsed: new Date('2024-01-15'),
    avatar: ''
  },
  {
    id: 2,
    name: '代码助手',
    description: '专门用于代码生成和编程辅助的智能体',
    type: '代码生成',
    model: 'GPT-4',
    status: 'active',
    capabilities: ['代码编写', '代码审查', '问题调试'],
    createdAt: new Date('2024-01-12'),
    lastUsed: new Date('2024-01-14'),
    avatar: ''
  },
  {
    id: 3,
    name: '数据分析师',
    description: '专业的数据分析和可视化智能体',
    type: '数据分析',
    model: 'Claude-3',
    status: 'inactive',
    capabilities: ['数据分析', '图表生成', '统计计算'],
    createdAt: new Date('2024-01-08'),
    lastUsed: new Date('2024-01-10'),
    avatar: ''
  }
]

// 计算属性
const filteredAgents = computed(() => {
  if (!searchText.value) return agents.value
  return agents.value.filter(agent => 
    agent.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
    agent.description.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

onMounted(() => {
  loadAgents()
})

const loadAgents = () => {
  agents.value = mockAgents
}

const refreshAgents = () => {
  loadAgents()
  ElMessage.success('智能体列表已刷新')
}

const createAgent = () => {
  isEditing.value = false
  agentForm.value = {
    id: null,
    name: '',
    description: '',
    type: '',
    model: '',
    capabilities: []
  }
  dialogVisible.value = true
}

const editAgent = (agent) => {
  isEditing.value = true
  agentForm.value = { ...agent }
  dialogVisible.value = true
}

const saveAgent = async () => {
  try {
    await formRef.value.validate()
    
    if (isEditing.value) {
      // 更新智能体
      const index = agents.value.findIndex(a => a.id === agentForm.value.id)
      if (index > -1) {
        agents.value[index] = { ...agentForm.value }
        ElMessage.success('智能体更新成功')
      }
    } else {
      // 创建新智能体
      const newAgent = {
        ...agentForm.value,
        id: Date.now(),
        status: 'active',
        createdAt: new Date(),
        lastUsed: null,
        avatar: ''
      }
      agents.value.push(newAgent)
      ElMessage.success('智能体创建成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCommand = (command) => {
  const { action, agent } = command
  
  switch (action) {
    case 'edit':
      editAgent(agent)
      break
    case 'clone':
      cloneAgent(agent)
      break
    case 'export':
      exportAgent(agent)
      break
    case 'delete':
      deleteAgent(agent)
      break
  }
}

const cloneAgent = (agent) => {
  const clonedAgent = {
    ...agent,
    id: Date.now(),
    name: `${agent.name} (副本)`,
    createdAt: new Date(),
    lastUsed: null
  }
  agents.value.push(clonedAgent)
  ElMessage.success('智能体克隆成功')
}

const exportAgent = (agent) => {
  // 模拟导出功能
  ElMessage.success(`智能体 "${agent.name}" 导出成功`)
}

const deleteAgent = async (agent) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除智能体 "${agent.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = agents.value.findIndex(a => a.id === agent.id)
    if (index > -1) {
      agents.value.splice(index, 1)
      ElMessage.success('智能体删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const testAgent = (agent) => {
  ElMessage.success(`开始测试智能体 "${agent.name}"`)
}

const chatWithAgent = (agent) => {
  ElMessage.success(`开始与智能体 "${agent.name}" 对话`)
}

const toggleAgentStatus = (agent) => {
  agent.status = agent.status === 'active' ? 'inactive' : 'active'
  const statusText = agent.status === 'active' ? '启用' : '停用'
  ElMessage.success(`智能体已${statusText}`)
}

const getStatusType = (status) => {
  return status === 'active' ? 'success' : 'info'
}

const getStatusText = (status) => {
  return status === 'active' ? '运行中' : '已停用'
}

const formatDate = (date) => {
  if (!date) return '从未使用'
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.agents-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.agent-card {
  transition: all 0.3s ease;
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.agent-content {
  padding: 16px 0;
}

.description {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.agent-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.label {
  color: #909399;
}

.value {
  color: #303133;
  font-weight: 500;
}

.agent-capabilities h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.capability-tag {
  font-size: 10px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  margin-top: 60px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}
</style>