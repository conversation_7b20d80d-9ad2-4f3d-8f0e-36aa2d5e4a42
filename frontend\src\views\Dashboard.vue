<template>
  <div class="dashboard">
    <!-- 系统概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon running">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ systemStats.runningTasks }}</div>
              <div class="card-label">运行中任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon agents">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ systemStats.totalAgents }}</div>
              <div class="card-label">智能体数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon executions">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ systemStats.totalExecutions }}</div>
              <div class="card-label">总执行次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-value">{{ systemStats.successRate }}%</div>
              <div class="card-label">成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card title="任务执行趋势">
          <template #header>
            <span>任务执行趋势</span>
            <el-button type="text" @click="refreshTaskTrend">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </template>
          <div class="chart-container">
            <v-chart :option="taskTrendOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="智能体状态分布">
          <template #header>
            <span>智能体状态分布</span>
            <el-button type="text" @click="refreshAgentStatus">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </template>
          <div class="chart-container">
            <v-chart :option="agentStatusOption" style="height: 300px;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-section">
      <el-col :span="16">
        <el-card title="最近任务">
          <template #header>
            <span>最近任务</span>
            <el-button type="text" @click="refreshRecentTasks">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </template>
          <el-table :data="recentTasks" style="width: 100%">
            <el-table-column prop="task_id" label="任务ID" width="200" show-overflow-tooltip />
            <el-table-column prop="skill_name" label="技能名称" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewTaskDetail(row.task_id)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="系统状态">
          <template #header>
            <span>系统状态</span>
            <el-badge :value="systemHealth.unhealthy_services" type="danger" v-if="systemHealth.unhealthy_services > 0">
              <el-icon><Warning /></el-icon>
            </el-badge>
          </template>
          <div class="system-status">
            <div class="status-item">
              <span class="status-label">服务器状态:</span>
              <el-tag :type="systemHealth.server_status === 'healthy' ? 'success' : 'danger'">
                {{ systemHealth.server_status === 'healthy' ? '正常' : '异常' }}
              </el-tag>
            </div>
            <div class="status-item">
              <span class="status-label">运行时间:</span>
              <span>{{ formatUptime(systemHealth.uptime) }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">活跃智能体:</span>
              <span>{{ systemHealth.total_agents }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">MCP服务:</span>
              <span>{{ systemHealth.healthy_services || 0 }}/{{ (systemHealth.healthy_services || 0) + (systemHealth.unhealthy_services || 0) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { systemApi, taskApi, agentApi } from '@/api'
import dayjs from 'dayjs'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const router = useRouter()

// 响应式数据
const systemStats = ref({
  runningTasks: 0,
  totalAgents: 0,
  totalExecutions: 0,
  successRate: 0
})

const systemHealth = ref({
  server_status: 'unknown',
  uptime: 0,
  total_agents: 0,
  healthy_services: 0,
  unhealthy_services: 0
})

const recentTasks = ref([])
const refreshTimer = ref(null)

// 图表配置
const taskTrendOption = ref({
  title: {
    text: '24小时任务执行趋势'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '任务数量',
    type: 'line',
    data: [],
    smooth: true
  }]
})

const agentStatusOption = ref({
  title: {
    text: '智能体状态分布'
  },
  tooltip: {
    trigger: 'item'
  },
  series: [{
    name: '智能体状态',
    type: 'pie',
    radius: '50%',
    data: []
  }]
})

// 方法
const loadSystemStats = async () => {
  try {
    const health = await systemApi.getHealth()
    systemStats.value = {
      runningTasks: health.active_tasks || 0,
      totalAgents: health.total_agents || 0,
      totalExecutions: health.total_executions || 0,
      successRate: Math.round((health.success_rate || 0) * 100)
    }
    systemHealth.value = health
  } catch (error) {
    console.error('加载系统统计失败:', error)
  }
}

const loadRecentTasks = async () => {
  try {
    const response = await taskApi.getTasks({ limit: 10 })
    recentTasks.value = response.tasks || []
  } catch (error) {
    console.error('加载最近任务失败:', error)
  }
}

const refreshTaskTrend = () => {
  // 模拟任务趋势数据
  const hours = []
  const data = []
  for (let i = 23; i >= 0; i--) {
    hours.push(dayjs().subtract(i, 'hour').format('HH:mm'))
    data.push(Math.floor(Math.random() * 20))
  }
  
  taskTrendOption.value.xAxis.data = hours
  taskTrendOption.value.series[0].data = data
}

const refreshAgentStatus = async () => {
  try {
    const agents = await agentApi.getAgents()
    const statusCount = { healthy: 0, unhealthy: 0, unknown: 0 }
    
    for (const agent of agents.agents || []) {
      const health = agent.statistics?.is_active ? 'healthy' : 'unhealthy'
      statusCount[health]++
    }
    
    agentStatusOption.value.series[0].data = [
      { value: statusCount.healthy, name: '健康' },
      { value: statusCount.unhealthy, name: '异常' },
      { value: statusCount.unknown, name: '未知' }
    ]
  } catch (error) {
    console.error('刷新智能体状态失败:', error)
  }
}

const refreshRecentTasks = () => {
  loadRecentTasks()
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return textMap[status] || status
}

const formatTime = (timeStr) => {
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

const formatUptime = (seconds) => {
  if (!seconds) return '未知'
  
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (days > 0) {
    return `${days}天 ${hours}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const viewTaskDetail = (taskId) => {
  router.push(`/tasks?taskId=${taskId}`)
}

// 生命周期
onMounted(() => {
  loadSystemStats()
  loadRecentTasks()
  refreshTaskTrend()
  refreshAgentStatus()
  
  // 设置定时刷新
  refreshTimer.value = setInterval(() => {
    loadSystemStats()
    loadRecentTasks()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.card-icon.running {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.agents {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.executions {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.success {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: var(--el-color-primary);
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-top: 5px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.activity-section {
  margin-bottom: 20px;
}

.system-status {
  padding: 10px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}
</style>
