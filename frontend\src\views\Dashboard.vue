<template>
  <div class="dashboard">
    <!-- 系统状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon" :class="{ 'status-online': systemStatus.online }">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">系统状态</div>
              <div class="status-value">{{ systemStatus.online ? '正常运行' : '离线' }}</div>
              <div class="status-detail">运行时间: {{ formatDuration(systemStatus.uptime) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">活跃智能体</div>
              <div class="status-value">{{ agentStats.active }}</div>
              <div class="status-detail">总计: {{ agentStats.total }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">运行中任务</div>
              <div class="status-value">{{ taskStats.running }}</div>
              <div class="status-detail">今日完成: {{ taskStats.completed_today }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-content">
            <div class="status-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-title">会话总数</div>
              <div class="status-value">{{ sessionStats.total }}</div>
              <div class="status-detail">成功率: {{ sessionStats.success_rate }}%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          
          <div class="action-buttons">
            <el-button type="primary" size="large" @click="createWorkflow">
              <el-icon><Plus /></el-icon>
              创建工作流
            </el-button>
            <el-button type="success" size="large" @click="executeWorkflow">
              <el-icon><VideoPlay /></el-icon>
              执行工作流
            </el-button>
            <el-button type="info" size="large" @click="viewResults">
              <el-icon><View /></el-icon>
              查看结果
            </el-button>
            <el-button type="warning" size="large" @click="openSettings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动和系统监控 -->
    <el-row :gutter="20" class="recent-activity">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近会话</span>
              <el-button type="text" @click="$router.push('/sessions')">查看全部</el-button>
            </div>
          </template>
          
          <div class="activity-list" v-loading="loadingRecentSessions">
            <div 
              v-for="session in recentSessions" 
              :key="session.id"
              class="activity-item"
              @click="viewSession(session)"
            >
              <div class="activity-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ session.workflow_name }}</div>
                <div class="activity-time">{{ formatRelativeTime(session.created_at) }}</div>
              </div>
              <div class="activity-status">
                <el-tag :type="getStatusType(session.status)">{{ formatStatus(session.status) }}</el-tag>
              </div>
            </div>
            
            <div v-if="recentSessions.length === 0" class="empty-activity">
              <el-empty description="暂无最近会话" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统监控</span>
              <el-button type="text" @click="refreshSystemStatus">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="monitoring-content" v-loading="loadingSystemStatus">
            <div class="metric-item">
              <div class="metric-label">CPU使用率</div>
              <el-progress 
                :percentage="systemMetrics.cpu_usage" 
                :status="systemMetrics.cpu_usage > 80 ? 'exception' : undefined"
              />
            </div>
            
            <div class="metric-item">
              <div class="metric-label">内存使用率</div>
              <el-progress 
                :percentage="systemMetrics.memory_usage" 
                :status="systemMetrics.memory_usage > 85 ? 'exception' : systemMetrics.memory_usage > 70 ? 'warning' : undefined"
              />
            </div>
            
            <div class="metric-item">
              <div class="metric-label">API响应时间</div>
              <div class="metric-value">{{ systemMetrics.avg_response_time }}ms</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">今日请求数</div>
              <div class="metric-value">{{ formatNumber(systemMetrics.requests_today) }}</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">MCP服务状态</div>
              <div class="mcp-status">
                <status-indicator 
                  :status="mcpStats.connected > 0 ? 'success' : 'error'" 
                  :text="`${mcpStats.connected}/${mcpStats.total} 已连接`"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 工作流执行对话框 -->
    <el-dialog v-model="executeDialogVisible" title="执行工作流" width="600px">
      <el-form :model="executeForm" label-width="100px">
        <el-form-item label="选择工作流">
          <el-select v-model="executeForm.workflow_id" placeholder="请选择工作流" style="width: 100%;">
            <el-option 
              v-for="workflow in availableWorkflows" 
              :key="workflow.id" 
              :label="workflow.name" 
              :value="workflow.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="输入数据">
          <el-input 
            v-model="executeForm.input_data" 
            type="textarea" 
            :rows="6" 
            placeholder="请输入JSON格式的数据"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="executeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExecute" :loading="executing">执行</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useWorkflowStore } from '@/stores/workflow'
import { useAgentsStore } from '@/stores/agents'
import { useResultsStore } from '@/stores/results'
import { useMCPStore } from '@/stores/mcp'
import { systemApi } from '@/utils/api'
import { useWebSocket } from '@/utils/websocket'
import { formatDuration, formatRelativeTime, formatStatus, formatNumber } from '@/utils/formatters'
import StatusIndicator from '@/components/common/StatusIndicator.vue'

const router = useRouter()
const workflowStore = useWorkflowStore()
const agentsStore = useAgentsStore()
const resultsStore = useResultsStore()
const mcpStore = useMCPStore()
const { isConnected, connect, on, subscribeToSystemStatus } = useWebSocket()

// 响应式数据
const systemStatus = ref({
  online: false,
  uptime: 0
})

const systemMetrics = ref({
  cpu_usage: 0,
  memory_usage: 0,
  avg_response_time: 0,
  requests_today: 0
})

const agentStats = ref({
  total: 0,
  active: 0
})

const taskStats = ref({
  running: 0,
  completed_today: 0
})

const sessionStats = ref({
  total: 0,
  success_rate: 0
})

const mcpStats = ref({
  total: 0,
  connected: 0
})

const recentSessions = ref([])
const availableWorkflows = ref([])

const loadingRecentSessions = ref(false)
const loadingSystemStatus = ref(false)
const executeDialogVisible = ref(false)
const executing = ref(false)

const executeForm = reactive({
  workflow_id: '',
  input_data: '{\n  "user_request": "请输入您的需求"\n}'
})

// 方法
const loadDashboardData = async () => {
  await Promise.all([
    loadSystemStatus(),
    loadRecentSessions(),
    loadAvailableWorkflows(),
    loadStats()
  ])
}

const loadSystemStatus = async () => {
  loadingSystemStatus.value = true
  try {
    const status = await systemApi.getStatus()
    systemStatus.value = status.system
    systemMetrics.value = status.metrics
  } catch (error) {
    console.error('加载系统状态失败:', error)
  } finally {
    loadingSystemStatus.value = false
  }
}

const loadRecentSessions = async () => {
  loadingRecentSessions.value = true
  try {
    const sessions = await resultsStore.fetchSessions({ limit: 5, order: 'desc' })
    recentSessions.value = sessions
  } catch (error) {
    console.error('加载最近会话失败:', error)
  } finally {
    loadingRecentSessions.value = false
  }
}

const loadAvailableWorkflows = async () => {
  try {
    const workflows = await workflowStore.fetchWorkflows()
    availableWorkflows.value = workflows.filter(w => w.is_active)
  } catch (error) {
    console.error('加载工作流列表失败:', error)
  }
}

const loadStats = async () => {
  try {
    // 加载智能体统计
    await agentsStore.fetchAgents()
    agentStats.value = {
      total: agentsStore.agentCount,
      active: agentsStore.activeAgents.length
    }

    // 加载任务统计
    taskStats.value = {
      running: agentsStore.runningTasks.length,
      completed_today: 0 // 需要从API获取
    }

    // 加载会话统计
    sessionStats.value = resultsStore.getSessionStats()

    // 加载MCP统计
    await mcpStore.fetchServers()
    mcpStats.value = mcpStore.getServerStats()
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const refreshSystemStatus = () => {
  loadSystemStatus()
}

const createWorkflow = () => {
  router.push('/workflows/designer')
}

const executeWorkflow = () => {
  executeDialogVisible.value = true
}

const viewResults = () => {
  router.push('/sessions')
}

const openSettings = () => {
  router.push('/settings')
}

const viewSession = (session) => {
  router.push(`/sessions/${session.id}`)
}

const confirmExecute = async () => {
  if (!executeForm.workflow_id) {
    ElMessage.warning('请选择工作流')
    return
  }

  try {
    executing.value = true

    let inputData
    try {
      inputData = JSON.parse(executeForm.input_data)
    } catch (error) {
      ElMessage.error('输入数据格式错误，请检查JSON格式')
      return
    }

    const result = await workflowStore.executeWorkflow(executeForm.workflow_id, inputData)

    executeDialogVisible.value = false
    ElMessage.success('工作流开始执行')

    // 跳转到会话页面查看执行结果
    router.push(`/sessions/${result.session_id}`)

  } catch (error) {
    console.error('执行工作流失败:', error)
  } finally {
    executing.value = false
  }
}

const getStatusType = (status) => {
  const typeMap = {
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'cancelled': 'info'
  }
  return typeMap[status] || 'info'
}

// WebSocket事件处理
const handleSystemStatusUpdate = (data) => {
  systemStatus.value = { ...systemStatus.value, ...data }
}

const handleMetricsUpdate = (data) => {
  systemMetrics.value = { ...systemMetrics.value, ...data }
}

// 生命周期
onMounted(async () => {
  // 连接WebSocket
  if (!isConnected.value) {
    connect()
  }

  // 订阅系统状态更新
  on('system_status', handleSystemStatusUpdate)
  on('system_metrics', handleMetricsUpdate)
  subscribeToSystemStatus()

  // 加载仪表板数据
  await loadDashboardData()

  // 设置定时刷新
  const refreshInterval = setInterval(() => {
    loadSystemStatus()
    loadStats()
  }, 30000) // 30秒刷新一次

  onUnmounted(() => {
    clearInterval(refreshInterval)
  })
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 120px;
}

.status-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: var(--el-color-primary);
  transition: all 0.3s ease;
}

.status-icon.status-online {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.status-detail {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.quick-actions {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 140px;
  height: 60px;
  font-size: 16px;
}

.recent-activity {
  margin-bottom: 20px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.activity-item:hover {
  background-color: var(--el-fill-color-lighter);
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 4px;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--el-color-primary-light-9);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: var(--el-color-primary);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.activity-status {
  margin-left: 12px;
}

.empty-activity {
  text-align: center;
  padding: 40px 0;
}

.monitoring-content {
  padding: 10px 0;
}

.metric-item {
  margin-bottom: 20px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  font-family: 'Courier New', monospace;
}

.mcp-status {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons {
    justify-content: space-around;
  }

  .action-buttons .el-button {
    min-width: 120px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .status-cards .el-col {
    margin-bottom: 10px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons .el-button {
    width: 100%;
    max-width: 300px;
  }

  .recent-activity .el-col {
    margin-bottom: 20px;
  }
}
</style>
