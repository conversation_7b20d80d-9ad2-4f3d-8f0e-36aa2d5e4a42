<template>
  <div class="logs-container">
    <div class="page-header">
      <h1>系统日志</h1>
      <p>查看和管理系统运行日志</p>
    </div>
    
    <div class="toolbar">
      <el-button @click="refreshLogs">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-button @click="clearLogs" type="danger">
        <el-icon><Delete /></el-icon>
        清空日志
      </el-button>
      <el-button @click="exportLogs">
        <el-icon><Download /></el-icon>
        导出日志
      </el-button>
      
      <el-divider direction="vertical" />
      
      <el-input 
        v-model="searchText" 
        placeholder="搜索日志内容..."
        style="width: 250px;"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      
      <el-select v-model="levelFilter" placeholder="日志级别" style="width: 120px;">
        <el-option label="全部" value="" />
        <el-option label="DEBUG" value="debug" />
        <el-option label="INFO" value="info" />
        <el-option label="WARN" value="warn" />
        <el-option label="ERROR" value="error" />
        <el-option label="FATAL" value="fatal" />
      </el-select>
      
      <el-select v-model="sourceFilter" placeholder="日志来源" style="width: 150px;">
        <el-option label="全部" value="" />
        <el-option label="系统" value="system" />
        <el-option label="API" value="api" />
        <el-option label="智能体" value="agent" />
        <el-option label="工作流" value="workflow" />
        <el-option label="数据库" value="database" />
        <el-option label="MCP" value="mcp" />
      </el-select>
      
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        style="width: 350px;"
      />
      
      <el-switch
        v-model="autoRefresh"
        active-text="自动刷新"
        @change="toggleAutoRefresh"
      />
    </div>

    <div class="logs-stats">
      <el-row :gutter="16">
        <el-col :span="4">
          <el-statistic title="总日志数" :value="totalLogs" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="错误数" :value="errorCount" suffix="条" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="警告数" :value="warnCount" suffix="条" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="今日日志" :value="todayCount" suffix="条" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="平均响应时间" :value="avgResponseTime" suffix="ms" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="系统状态" :value="systemStatus">
            <template #suffix>
              <el-tag :type="systemStatus === '正常' ? 'success' : 'danger'" size="small">
                {{ systemStatus }}
              </el-tag>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <div class="logs-table">
      <el-table 
        :data="filteredLogs" 
        style="width: 100%"
        :row-class-name="getRowClassName"
        @row-click="viewLogDetail"
        height="500"
      >
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.timestamp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="level" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelColor(row.level)" size="small">
              {{ row.level.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="source" label="来源" width="100">
          <template #default="{ row }">
            <el-tag :type="getSourceColor(row.source)" size="small">
              {{ getSourceText(row.source) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="module" label="模块" width="120" />
        
        <el-table-column prop="message" label="消息" min-width="300">
          <template #default="{ row }">
            <div class="log-message">
              <span class="message-text">{{ row.message }}</span>
              <el-button 
                v-if="row.details"
                type="text" 
                size="small"
                @click.stop="viewLogDetail(row)"
              >
                查看详情
              </el-button>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="userId" label="用户ID" width="100" />
        
        <el-table-column prop="requestId" label="请求ID" width="120">
          <template #default="{ row }">
            <span v-if="row.requestId" class="request-id">{{ row.requestId.slice(0, 8) }}...</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="耗时" width="80">
          <template #default="{ row }">
            <span v-if="row.duration">{{ row.duration }}ms</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-dropdown @command="handleCommand">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'view', log: row}">
                    查看详情
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'copy', log: row}">
                    复制消息
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="row.requestId"
                    :command="{action: 'trace', log: row}"
                  >
                    追踪请求
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="{action: 'filter', log: row}"
                    divided
                  >
                    筛选相同
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[50, 100, 200, 500]"
        :total="totalLogs"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog 
      v-model="logDetailVisible" 
      title="日志详情"
      width="800px"
      class="log-detail-dialog"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间">
            {{ formatDateTime(selectedLog.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLevelColor(selectedLog.level)">
              {{ selectedLog.level.toUpperCase() }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="来源">
            <el-tag :type="getSourceColor(selectedLog.source)">
              {{ getSourceText(selectedLog.source) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模块">
            {{ selectedLog.module }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ selectedLog.userId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="请求ID">
            {{ selectedLog.requestId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            {{ selectedLog.duration ? selectedLog.duration + 'ms' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedLog.ip || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="message-section">
          <h4>消息内容</h4>
          <div class="message-content">
            {{ selectedLog.message }}
          </div>
        </div>
        
        <div v-if="selectedLog.details" class="details-section">
          <h4>详细信息</h4>
          <el-tabs v-model="activeDetailTab">
            <el-tab-pane label="堆栈信息" name="stack" v-if="selectedLog.details.stack">
              <pre class="stack-trace">{{ selectedLog.details.stack }}</pre>
            </el-tab-pane>
            <el-tab-pane label="请求数据" name="request" v-if="selectedLog.details.request">
              <pre class="json-data">{{ JSON.stringify(selectedLog.details.request, null, 2) }}</pre>
            </el-tab-pane>
            <el-tab-pane label="响应数据" name="response" v-if="selectedLog.details.response">
              <pre class="json-data">{{ JSON.stringify(selectedLog.details.response, null, 2) }}</pre>
            </el-tab-pane>
            <el-tab-pane label="上下文" name="context" v-if="selectedLog.details.context">
              <pre class="json-data">{{ JSON.stringify(selectedLog.details.context, null, 2) }}</pre>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="logDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyLogDetail">
            复制详情
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 请求追踪对话框 -->
    <el-dialog 
      v-model="traceVisible" 
      title="请求追踪"
      width="900px"
      class="trace-dialog"
    >
      <div v-if="traceData" class="trace-timeline">
        <el-timeline>
          <el-timeline-item 
            v-for="item in traceData" 
            :key="item.id"
            :timestamp="formatDateTime(item.timestamp)"
            :type="getLevelColor(item.level)"
          >
            <div class="trace-item">
              <div class="trace-header">
                <el-tag :type="getLevelColor(item.level)" size="small">
                  {{ item.level.toUpperCase() }}
                </el-tag>
                <span class="trace-module">{{ item.module }}</span>
                <span class="trace-duration" v-if="item.duration">{{ item.duration }}ms</span>
              </div>
              <div class="trace-message">{{ item.message }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="traceVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Delete, Download, Search, MoreFilled } from '@element-plus/icons-vue'

const searchText = ref('')
const levelFilter = ref('')
const sourceFilter = ref('')
const dateRange = ref([])
const autoRefresh = ref(false)
const currentPage = ref(1)
const pageSize = ref(100)
const totalLogs = ref(0)
const logDetailVisible = ref(false)
const traceVisible = ref(false)
const selectedLog = ref(null)
const activeDetailTab = ref('stack')
const logs = ref([])
const traceData = ref([])
const refreshTimer = ref(null)

// 统计数据
const errorCount = ref(0)
const warnCount = ref(0)
const todayCount = ref(0)
const avgResponseTime = ref(0)
const systemStatus = ref('正常')

// 模拟数据
const mockLogs = [
  {
    id: 1,
    timestamp: new Date('2024-01-15T14:30:25'),
    level: 'error',
    source: 'api',
    module: 'auth',
    message: '用户认证失败: 无效的API密钥',
    userId: 'user_123',
    requestId: 'req_abc123def456',
    duration: 150,
    ip: '*************',
    details: {
      stack: 'AuthenticationError: Invalid API key\n    at validateApiKey (auth.js:45)\n    at authenticate (auth.js:20)\n    at processRequest (api.js:15)',
      request: {
        method: 'POST',
        url: '/api/v1/auth/login',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer invalid_token'
        },
        body: {
          username: 'user123',
          password: '***'
        }
      },
      context: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        sessionId: 'sess_xyz789'
      }
    }
  },
  {
    id: 2,
    timestamp: new Date('2024-01-15T14:29:45'),
    level: 'info',
    source: 'system',
    module: 'startup',
    message: '系统启动完成',
    userId: null,
    requestId: null,
    duration: null,
    ip: null,
    details: {
      context: {
        version: '1.0.0',
        environment: 'production',
        startupTime: '2.5s'
      }
    }
  },
  {
    id: 3,
    timestamp: new Date('2024-01-15T14:28:30'),
    level: 'warn',
    source: 'agent',
    module: 'execution',
    message: 'AI智能体响应时间超过阈值',
    userId: 'user_456',
    requestId: 'req_def456ghi789',
    duration: 5200,
    ip: '*************',
    details: {
      request: {
        agentId: 'agent_001',
        prompt: '请帮我分析这份数据...',
        maxTokens: 1000
      },
      response: {
        tokens: 850,
        responseTime: 5200,
        model: 'gpt-4'
      },
      context: {
        threshold: 3000,
        retryCount: 0
      }
    }
  },
  {
    id: 4,
    timestamp: new Date('2024-01-15T14:27:15'),
    level: 'debug',
    source: 'database',
    module: 'query',
    message: 'SQL查询执行: SELECT * FROM tasks WHERE status = ?',
    userId: 'user_789',
    requestId: 'req_ghi789jkl012',
    duration: 25,
    ip: '*************',
    details: {
      request: {
        sql: 'SELECT * FROM tasks WHERE status = ?',
        params: ['running'],
        connection: 'pool_1'
      },
      response: {
        rowCount: 15,
        executionTime: 25
      }
    }
  },
  {
    id: 5,
    timestamp: new Date('2024-01-15T14:26:00'),
    level: 'info',
    source: 'workflow',
    module: 'execution',
    message: '工作流执行完成: 数据处理流程',
    userId: 'user_123',
    requestId: 'req_jkl012mno345',
    duration: 1200,
    ip: '*************',
    details: {
      request: {
        workflowId: 'wf_001',
        inputData: {
          file: 'data.csv',
          rows: 1000
        }
      },
      response: {
        status: 'completed',
        outputFile: 'processed_data.csv',
        processedRows: 950
      }
    }
  }
]

// 计算属性
const filteredLogs = computed(() => {
  let filtered = logs.value
  
  // 级别筛选
  if (levelFilter.value) {
    filtered = filtered.filter(log => log.level === levelFilter.value)
  }
  
  // 来源筛选
  if (sourceFilter.value) {
    filtered = filtered.filter(log => log.source === sourceFilter.value)
  }
  
  // 时间范围筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    filtered = filtered.filter(log => {
      const logTime = new Date(log.timestamp)
      return logTime >= new Date(start) && logTime <= new Date(end)
    })
  }
  
  // 搜索筛选
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(searchLower) ||
      log.module.toLowerCase().includes(searchLower) ||
      (log.requestId && log.requestId.toLowerCase().includes(searchLower))
    )
  }
  
  return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
})

onMounted(() => {
  loadLogs()
  calculateStats()
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})

const loadLogs = () => {
  logs.value = mockLogs
  totalLogs.value = mockLogs.length
}

const calculateStats = () => {
  const today = new Date().toDateString()
  
  errorCount.value = logs.value.filter(log => log.level === 'error').length
  warnCount.value = logs.value.filter(log => log.level === 'warn').length
  todayCount.value = logs.value.filter(log => 
    new Date(log.timestamp).toDateString() === today
  ).length
  
  const durations = logs.value.filter(log => log.duration).map(log => log.duration)
  avgResponseTime.value = durations.length > 0 
    ? Math.round(durations.reduce((a, b) => a + b, 0) / durations.length)
    : 0
    
  systemStatus.value = errorCount.value > 10 ? '异常' : '正常'
}

const refreshLogs = () => {
  loadLogs()
  calculateStats()
  ElMessage.success('日志已刷新')
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    logs.value = []
    totalLogs.value = 0
    calculateStats()
    ElMessage.success('日志已清空')
  } catch {
    // 用户取消操作
  }
}

const exportLogs = () => {
  const data = filteredLogs.value.map(log => ({
    时间: formatDateTime(log.timestamp),
    级别: log.level.toUpperCase(),
    来源: getSourceText(log.source),
    模块: log.module,
    消息: log.message,
    用户ID: log.userId || '',
    请求ID: log.requestId || '',
    耗时: log.duration ? log.duration + 'ms' : '',
    IP地址: log.ip || ''
  }))
  
  const csv = [Object.keys(data[0]).join(',')]
    .concat(data.map(row => Object.values(row).join(',')))
    .join('\n')
  
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `logs_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  
  ElMessage.success('日志导出成功')
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    refreshTimer.value = setInterval(() => {
      refreshLogs()
    }, 30000) // 30秒刷新一次
    ElMessage.success('已开启自动刷新')
  } else {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
    ElMessage.success('已关闭自动刷新')
  }
}

const viewLogDetail = (log) => {
  selectedLog.value = log
  activeDetailTab.value = log.details?.stack ? 'stack' : 
                        log.details?.request ? 'request' : 'context'
  logDetailVisible.value = true
}

const handleCommand = (command) => {
  const { action, log } = command
  
  switch (action) {
    case 'view':
      viewLogDetail(log)
      break
    case 'copy':
      copyToClipboard(log.message)
      break
    case 'trace':
      traceRequest(log)
      break
    case 'filter':
      filterSimilar(log)
      break
  }
}

const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板')
  })
}

const copyLogDetail = () => {
  const detail = {
    时间: formatDateTime(selectedLog.value.timestamp),
    级别: selectedLog.value.level.toUpperCase(),
    来源: getSourceText(selectedLog.value.source),
    模块: selectedLog.value.module,
    消息: selectedLog.value.message,
    详情: selectedLog.value.details
  }
  
  copyToClipboard(JSON.stringify(detail, null, 2))
}

const traceRequest = (log) => {
  if (!log.requestId) return
  
  // 模拟获取相同请求ID的所有日志
  traceData.value = logs.value
    .filter(l => l.requestId === log.requestId)
    .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
  
  traceVisible.value = true
}

const filterSimilar = (log) => {
  sourceFilter.value = log.source
  levelFilter.value = log.level
  ElMessage.success(`已筛选 ${getSourceText(log.source)} - ${log.level.toUpperCase()} 级别的日志`)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getRowClassName = ({ row }) => {
  return `log-row log-${row.level}`
}

const getLevelColor = (level) => {
  const levelMap = {
    debug: 'info',
    info: 'success',
    warn: 'warning',
    error: 'danger',
    fatal: 'danger'
  }
  return levelMap[level] || 'info'
}

const getSourceColor = (source) => {
  const sourceMap = {
    system: 'primary',
    api: 'success',
    agent: 'warning',
    workflow: 'info',
    database: 'primary',
    mcp: 'warning'
  }
  return sourceMap[source] || 'info'
}

const getSourceText = (source) => {
  const sourceMap = {
    system: '系统',
    api: 'API',
    agent: '智能体',
    workflow: '工作流',
    database: '数据库',
    mcp: 'MCP'
  }
  return sourceMap[source] || source
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style scoped>
.logs-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.logs-stats {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.logs-table {
  margin-bottom: 20px;
}

.log-row {
  cursor: pointer;
}

.log-row:hover {
  background-color: #f5f7fa;
}

.log-row.log-error {
  background-color: #fef0f0;
}

.log-row.log-warn {
  background-color: #fdf6ec;
}

.log-row.log-debug {
  background-color: #f0f9ff;
}

.log-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.request-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.log-detail {
  max-height: 600px;
  overflow-y: auto;
}

.message-section,
.details-section {
  margin-top: 20px;
}

.message-section h4,
.details-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.message-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  word-break: break-all;
}

.stack-trace,
.json-data {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.trace-timeline {
  max-height: 500px;
  overflow-y: auto;
}

.trace-item {
  margin-bottom: 8px;
}

.trace-header {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 4px;
}

.trace-module {
  font-weight: 500;
  color: #303133;
}

.trace-duration {
  font-size: 12px;
  color: #666;
}

.trace-message {
  color: #666;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}
</style>