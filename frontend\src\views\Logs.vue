<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>系统日志</span>
          <div class="header-actions">
            <el-select v-model="levelFilter" placeholder="日志级别" style="width: 120px; margin-right: 10px;">
              <el-option label="全部" value="" />
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
              <el-option label="CRITICAL" value="CRITICAL" />
            </el-select>
            <el-select v-model="sessionFilter" placeholder="会话筛选" style="width: 200px; margin-right: 10px;">
              <el-option label="全部会话" value="" />
              <el-option
                v-for="session in availableSessions"
                :key="session.id"
                :label="`${session.workflow_name} (${session.id.slice(0, 8)}...)`"
                :value="session.id"
              />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="margin-right: 10px;"
              @change="handleDateChange"
            />
            <el-input
              v-model="searchKeyword"
              placeholder="搜索日志内容"
              style="width: 200px; margin-right: 10px;"
              @keyup.enter="searchLogs"
            >
              <template #suffix>
                <el-icon @click="searchLogs" style="cursor: pointer;"><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="clearLogs" type="danger">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
            <el-button @click="exportLogs" type="primary">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <div class="logs-container">
        <div class="logs-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总日志数" :value="total" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="错误日志" :value="errorCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="警告日志" :value="warningCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="今日日志" :value="todayCount" />
            </el-col>
          </el-row>
        </div>

        <div class="logs-content" v-loading="loading">
          <div v-if="logs.length === 0" class="empty-logs">
            <el-empty description="暂无日志数据" />
          </div>
          <div v-else>
            <div
              v-for="log in logs"
              :key="log.id"
              class="log-item"
              :class="`log-${log.level.toLowerCase()}`"
              @click="viewLogDetail(log)"
            >
              <div class="log-header">
                <div class="log-meta">
                  <el-tag :type="getLevelType(log.level)" size="small">{{ log.level }}</el-tag>
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                  <span v-if="log.session_id" class="log-session">
                    会话: {{ log.session_id.slice(0, 8) }}...
                  </span>
                </div>
                <div class="log-actions">
                  <el-button type="text" size="small" @click.stop="copyLog(log)">
                    <el-icon><CopyDocument /></el-icon>
                  </el-button>
                  <el-button type="text" size="small" @click.stop="viewLogDetail(log)">
                    <el-icon><View /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="log-message">{{ log.message }}</div>
              <div v-if="log.context && Object.keys(log.context).length > 0" class="log-context-preview">
                <el-tag size="small" type="info">包含上下文数据</el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          style="margin-top: 20px; text-align: right;"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="日志详情" width="800px">
      <div v-if="selectedLog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">
            {{ selectedLog.id }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLevelType(selectedLog.level)">{{ selectedLog.level }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="时间">
            {{ formatTime(selectedLog.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="会话ID">
            {{ selectedLog.session_id || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="消息" :span="2">
            {{ selectedLog.message }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedLog.context" style="margin-top: 20px;">
          <h4>上下文数据</h4>
          <pre class="context-data">{{ JSON.stringify(selectedLog.context, null, 2) }}</pre>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="copyLogDetail">
          <el-icon><CopyDocument /></el-icon>
          复制详情
        </el-button>
      </template>
    </el-dialog>

    <!-- 导出配置对话框 -->
    <el-dialog v-model="exportDialogVisible" title="导出日志" width="500px">
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="json">JSON</el-radio>
            <el-radio label="csv">CSV</el-radio>
            <el-radio label="txt">TXT</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="exportForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="日志级别">
          <el-checkbox-group v-model="exportForm.levels">
            <el-checkbox label="DEBUG">DEBUG</el-checkbox>
            <el-checkbox label="INFO">INFO</el-checkbox>
            <el-checkbox label="WARNING">WARNING</el-checkbox>
            <el-checkbox label="ERROR">ERROR</el-checkbox>
            <el-checkbox label="CRITICAL">CRITICAL</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="包含上下文">
          <el-switch v-model="exportForm.includeContext" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport" :loading="exportLoading">
          导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { sessionApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const logs = ref([])
const selectedLog = ref(null)
const availableSessions = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 筛选
const levelFilter = ref('')
const sessionFilter = ref('')
const dateRange = ref([])
const searchKeyword = ref('')

// 对话框状态
const detailDialogVisible = ref(false)
const exportDialogVisible = ref(false)

// 加载状态
const exportLoading = ref(false)

// 导出表单
const exportForm = ref({
  format: 'json',
  dateRange: [],
  levels: ['INFO', 'WARNING', 'ERROR', 'CRITICAL'],
  includeContext: true
})

// 统计数据
const errorCount = computed(() => {
  return logs.value.filter(log => log.level === 'ERROR' || log.level === 'CRITICAL').length
})

const warningCount = computed(() => {
  return logs.value.filter(log => log.level === 'WARNING').length
})

const todayCount = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return logs.value.filter(log => dayjs(log.timestamp).format('YYYY-MM-DD') === today).length
})

// 方法
const loadLogs = async () => {
  loading.value = true
  try {
    // 这里应该从后端加载日志
    // 暂时使用模拟数据
    const mockLogs = [
      {
        id: 'log_001',
        level: 'INFO',
        message: '系统启动成功',
        timestamp: '2024-01-15T10:00:00Z',
        session_id: null,
        context: null
      },
      {
        id: 'log_002',
        level: 'INFO',
        message: '智能体注册成功: intent_recognizer',
        timestamp: '2024-01-15T10:00:05Z',
        session_id: null,
        context: { agent_id: 'intent_recognizer' }
      },
      {
        id: 'log_003',
        level: 'DEBUG',
        message: '开始执行工作流: code_generation_workflow',
        timestamp: '2024-01-15T10:30:00Z',
        session_id: 'session_001',
        context: { workflow_id: 'code_generation_workflow' }
      },
      {
        id: 'log_004',
        level: 'WARNING',
        message: '千问API响应时间较长: 5.2秒',
        timestamp: '2024-01-15T10:30:15Z',
        session_id: 'session_001',
        context: { response_time: 5.2, model: 'qwen-plus' }
      },
      {
        id: 'log_005',
        level: 'ERROR',
        message: '智能体任务执行失败: 网络连接超时',
        timestamp: '2024-01-15T10:35:00Z',
        session_id: 'session_002',
        context: { error: 'ConnectionTimeout', agent_id: 'code_generator' }
      }
    ]

    logs.value = mockLogs
    total.value = mockLogs.length

  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

const loadSessions = async () => {
  try {
    // 这里应该从后端加载会话列表
    const mockSessions = [
      { id: 'session_001', workflow_name: '代码生成工作流' },
      { id: 'session_002', workflow_name: '市场调研工作流' }
    ]

    availableSessions.value = mockSessions
  } catch (error) {
    console.error('加载会话列表失败:', error)
  }
}

const refreshLogs = () => {
  loadLogs()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadLogs()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadLogs()
}

const handleDateChange = () => {
  loadLogs()
}

const searchLogs = () => {
  loadLogs()
}

const viewLogDetail = (log) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

const copyLog = async (log) => {
  try {
    const logText = `[${log.level}] ${formatTime(log.timestamp)} - ${log.message}`
    await navigator.clipboard.writeText(logText)
    ElMessage.success('日志已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const copyLogDetail = async () => {
  if (!selectedLog.value) return

  try {
    const logDetail = {
      id: selectedLog.value.id,
      level: selectedLog.value.level,
      message: selectedLog.value.message,
      timestamp: selectedLog.value.timestamp,
      session_id: selectedLog.value.session_id,
      context: selectedLog.value.context
    }

    await navigator.clipboard.writeText(JSON.stringify(logDetail, null, 2))
    ElMessage.success('日志详情已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用后端API清空日志
    logs.value = []
    total.value = 0

    ElMessage.success('日志已清空')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error)
      ElMessage.error('清空日志失败')
    }
  }
}

const exportLogs = () => {
  exportForm.value.dateRange = dateRange.value
  exportDialogVisible.value = true
}

const confirmExport = async () => {
  try {
    exportLoading.value = true

    // 准备导出数据
    let exportData = logs.value

    // 应用筛选条件
    if (exportForm.value.levels.length > 0) {
      exportData = exportData.filter(log => exportForm.value.levels.includes(log.level))
    }

    if (exportForm.value.dateRange && exportForm.value.dateRange.length === 2) {
      const [start, end] = exportForm.value.dateRange
      exportData = exportData.filter(log => {
        const logTime = dayjs(log.timestamp)
        return logTime.isAfter(start) && logTime.isBefore(end)
      })
    }

    // 处理上下文数据
    if (!exportForm.value.includeContext) {
      exportData = exportData.map(log => ({
        ...log,
        context: undefined
      }))
    }

    // 生成文件
    let content = ''
    let filename = `logs_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}`

    if (exportForm.value.format === 'json') {
      content = JSON.stringify(exportData, null, 2)
      filename += '.json'
    } else if (exportForm.value.format === 'csv') {
      const headers = ['ID', '级别', '时间', '消息', '会话ID']
      const rows = exportData.map(log => [
        log.id,
        log.level,
        formatTime(log.timestamp),
        log.message,
        log.session_id || ''
      ])

      content = [headers, ...rows].map(row => row.join(',')).join('\n')
      filename += '.csv'
    } else if (exportForm.value.format === 'txt') {
      content = exportData.map(log =>
        `[${log.level}] ${formatTime(log.timestamp)} - ${log.message}`
      ).join('\n')
      filename += '.txt'
    }

    // 下载文件
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.click()

    exportDialogVisible.value = false
    ElMessage.success('日志导出成功')

  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  } finally {
    exportLoading.value = false
  }
}

// 工具方法
const getLevelType = (level) => {
  const typeMap = {
    DEBUG: 'info',
    INFO: 'primary',
    WARNING: 'warning',
    ERROR: 'danger',
    CRITICAL: 'danger'
  }
  return typeMap[level] || 'info'
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 监听筛选条件变化
watch([levelFilter, sessionFilter, searchKeyword], () => {
  loadLogs()
})

// 生命周期
onMounted(() => {
  loadLogs()
  loadSessions()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.logs-container {
  margin-top: 20px;
}

.logs-stats {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.logs-content {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 10px;
}

.empty-logs {
  text-align: center;
  padding: 40px;
}

.log-item {
  margin-bottom: 10px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--el-border-color);
  background-color: var(--el-fill-color-blank);
  cursor: pointer;
  transition: all 0.3s ease;
}

.log-item:hover {
  background-color: var(--el-fill-color-light);
  transform: translateX(2px);
}

.log-item.log-debug {
  border-left-color: var(--el-color-info);
}

.log-item.log-info {
  border-left-color: var(--el-color-primary);
}

.log-item.log-warning {
  border-left-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.log-item.log-error,
.log-item.log-critical {
  border-left-color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-family: 'Courier New', monospace;
}

.log-session {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color);
  padding: 2px 6px;
  border-radius: 3px;
}

.log-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.log-item:hover .log-actions {
  opacity: 1;
}

.log-message {
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  word-break: break-word;
}

.log-context-preview {
  margin-top: 8px;
}

.context-data {
  background-color: var(--el-fill-color-darker);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .header-actions > * {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }

  .logs-stats .el-col {
    margin-bottom: 10px;
  }

  .log-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .log-actions {
    opacity: 1;
  }
}

/* 滚动条样式 */
.logs-content::-webkit-scrollbar {
  width: 6px;
}

.logs-content::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}
</style>
