<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>扩展服务管理</span>
          <div class="header-actions">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 10px;">
              <el-option label="全部" value="" />
              <el-option label="已连接" value="connected" />
              <el-option label="已断开" value="disconnected" />
              <el-option label="错误" value="error" />
            </el-select>
            <el-button @click="refreshServices">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              添加服务
            </el-button>
          </div>
        </div>
      </template>

      <div class="services-content">
        <!-- 服务统计 -->
        <div class="services-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总服务数" :value="services.length" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已连接" :value="connectedCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已断开" :value="disconnectedCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="错误状态" :value="errorCount" />
            </el-col>
          </el-row>
        </div>

        <!-- 服务列表 -->
        <el-table :data="filteredServices" v-loading="loading" style="width: 100%">
          <el-table-column prop="name" label="服务名称" width="200" />
          <el-table-column prop="type" label="服务类型" width="150">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)">{{ getTypeDisplayName(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" show-overflow-tooltip />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="endpoint" label="端点" width="200" show-overflow-tooltip />
          <el-table-column label="功能数" width="80">
            <template #default="{ row }">
              {{ row.capabilities?.length || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="last_ping" label="最后检查" width="180">
            <template #default="{ row }">
              {{ formatTime(row.last_ping) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewService(row)">
                详情
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="testConnection(row)"
                :loading="row.testing"
              >
                测试
              </el-button>
              <el-button
                v-if="row.status === 'disconnected'"
                type="text"
                size="small"
                @click="connectService(row)"
              >
                连接
              </el-button>
              <el-button
                v-if="row.status === 'connected'"
                type="text"
                size="small"
                @click="disconnectService(row)"
              >
                断开
              </el-button>
              <el-button
                type="text"
                size="small"
                danger
                @click="removeService(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 添加服务对话框 -->
    <el-dialog v-model="addDialogVisible" title="添加扩展服务" width="600px">
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="服务名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入服务名称" />
        </el-form-item>
        <el-form-item label="服务类型" prop="type">
          <el-select v-model="addForm.type" placeholder="请选择服务类型" style="width: 100%;">
            <el-option label="文件操作" value="file_operations" />
            <el-option label="网络搜索" value="web_search" />
            <el-option label="数据分析" value="data_analysis" />
            <el-option label="Git操作" value="git_operations" />
            <el-option label="数据库操作" value="database" />
            <el-option label="API调用" value="api_client" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务端点" prop="endpoint">
          <el-input v-model="addForm.endpoint" placeholder="http://localhost:3000 或 ws://localhost:3001" />
        </el-form-item>
        <el-form-item label="认证方式">
          <el-select v-model="addForm.auth_type" style="width: 100%;">
            <el-option label="无认证" value="none" />
            <el-option label="API密钥" value="api_key" />
            <el-option label="Bearer Token" value="bearer" />
            <el-option label="基础认证" value="basic" />
          </el-select>
        </el-form-item>
        <el-form-item label="认证信息" v-if="addForm.auth_type !== 'none'">
          <el-input
            v-model="addForm.auth_value"
            type="password"
            placeholder="请输入认证信息"
            show-password
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="addForm.description" type="textarea" :rows="3" placeholder="请输入服务描述" />
        </el-form-item>
        <el-form-item label="自动连接">
          <el-switch v-model="addForm.auto_connect" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addService" :loading="addLoading">
          添加
        </el-button>
      </template>
    </el-dialog>

    <!-- 服务详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="服务详情" width="800px">
      <div v-if="selectedService">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务名称">
            {{ selectedService.name }}
          </el-descriptions-item>
          <el-descriptions-item label="服务类型">
            <el-tag :type="getTypeTagType(selectedService.type)">
              {{ getTypeDisplayName(selectedService.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedService.status)">
              {{ getStatusText(selectedService.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="端点">
            {{ selectedService.endpoint }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedService.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后检查">
            {{ formatTime(selectedService.last_ping) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedService.description }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">可用功能</h4>
        <div v-if="selectedService.capabilities && selectedService.capabilities.length > 0">
          <el-tag
            v-for="capability in selectedService.capabilities"
            :key="capability"
            style="margin: 2px 4px;"
          >
            {{ capability }}
          </el-tag>
        </div>
        <div v-else class="no-data">暂无功能信息</div>

        <h4 style="margin: 20px 0 10px 0;">配置信息</h4>
        <pre v-if="selectedService.config">{{ JSON.stringify(selectedService.config, null, 2) }}</pre>
        <div v-else class="no-data">暂无配置信息</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const services = ref([])
const selectedService = ref(null)

// 筛选
const statusFilter = ref('')

// 对话框状态
const addDialogVisible = ref(false)
const detailDialogVisible = ref(false)

// 加载状态
const addLoading = ref(false)

// 表单数据
const addForm = ref({
  name: '',
  type: '',
  endpoint: '',
  auth_type: 'none',
  auth_value: '',
  description: '',
  auto_connect: true
})

// 表单引用
const addFormRef = ref()

// 表单验证规则
const addRules = {
  name: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择服务类型', trigger: 'change' }
  ],
  endpoint: [
    { required: true, message: '请输入服务端点', trigger: 'blur' },
    { pattern: /^(http|https|ws|wss):\/\//, message: '请输入有效的URL', trigger: 'blur' }
  ]
}

// 计算属性
const filteredServices = computed(() => {
  if (!statusFilter.value) return services.value
  return services.value.filter(service => service.status === statusFilter.value)
})

const connectedCount = computed(() => {
  return services.value.filter(service => service.status === 'connected').length
})

const disconnectedCount = computed(() => {
  return services.value.filter(service => service.status === 'disconnected').length
})

const errorCount = computed(() => {
  return services.value.filter(service => service.status === 'error').length
})

// 方法
const loadServices = async () => {
  loading.value = true
  try {
    // 这里应该从后端加载扩展服务列表
    // 暂时使用模拟数据
    const mockServices = [
      {
        id: 'service_001',
        name: '文件操作服务',
        type: 'file_operations',
        description: '提供文件读写、目录操作等功能',
        endpoint: 'http://localhost:3001',
        status: 'connected',
        capabilities: ['read_file', 'write_file', 'list_directory', 'create_directory'],
        config: { max_file_size: '10MB', allowed_extensions: ['.txt', '.json', '.py'] },
        created_at: '2024-01-15T10:00:00Z',
        last_ping: '2024-01-15T15:30:00Z',
        testing: false
      },
      {
        id: 'service_002',
        name: '网络搜索服务',
        type: 'web_search',
        description: '提供网络搜索和内容抓取功能',
        endpoint: 'http://localhost:3002',
        status: 'disconnected',
        capabilities: ['search_web', 'fetch_content', 'extract_text'],
        config: { search_engine: 'google', max_results: 10 },
        created_at: '2024-01-14T14:20:00Z',
        last_ping: '2024-01-15T12:00:00Z',
        testing: false
      },
      {
        id: 'service_003',
        name: 'Git操作服务',
        type: 'git_operations',
        description: '提供Git版本控制操作功能',
        endpoint: 'ws://localhost:3003',
        status: 'error',
        capabilities: ['clone_repo', 'commit_changes', 'push_changes', 'create_branch'],
        config: { default_branch: 'main', auto_push: false },
        created_at: '2024-01-13T09:15:00Z',
        last_ping: '2024-01-15T10:45:00Z',
        testing: false
      }
    ]

    services.value = mockServices

  } catch (error) {
    console.error('加载扩展服务失败:', error)
    ElMessage.error('加载扩展服务失败')
  } finally {
    loading.value = false
  }
}

const refreshServices = () => {
  loadServices()
}

const showAddDialog = () => {
  addForm.value = {
    name: '',
    type: '',
    endpoint: '',
    auth_type: 'none',
    auth_value: '',
    description: '',
    auto_connect: true
  }
  addDialogVisible.value = true
}

const addService = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()

    addLoading.value = true

    const serviceData = {
      ...addForm.value,
      id: `service_${Date.now()}`,
      status: 'disconnected',
      capabilities: [],
      config: {},
      created_at: new Date().toISOString(),
      last_ping: null,
      testing: false
    }

    // 这里应该调用后端API添加服务
    services.value.push(serviceData)

    addDialogVisible.value = false
    ElMessage.success('扩展服务添加成功')

    // 如果设置了自动连接，尝试连接
    if (addForm.value.auto_connect) {
      await connectService(serviceData)
    }

  } catch (error) {
    console.error('添加扩展服务失败:', error)
    ElMessage.error('添加扩展服务失败')
  } finally {
    addLoading.value = false
  }
}

const viewService = (service) => {
  selectedService.value = service
  detailDialogVisible.value = true
}

const testConnection = async (service) => {
  service.testing = true
  try {
    // 这里应该调用后端API测试连接
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟测试延迟

    service.last_ping = new Date().toISOString()
    service.status = 'connected'

    ElMessage.success(`服务 ${service.name} 连接测试成功`)
  } catch (error) {
    console.error('连接测试失败:', error)
    service.status = 'error'
    ElMessage.error(`服务 ${service.name} 连接测试失败`)
  } finally {
    service.testing = false
  }
}

const connectService = async (service) => {
  try {
    // 这里应该调用后端API连接服务
    service.status = 'connected'
    service.last_ping = new Date().toISOString()

    ElMessage.success(`服务 ${service.name} 已连接`)
  } catch (error) {
    console.error('连接服务失败:', error)
    service.status = 'error'
    ElMessage.error(`连接服务 ${service.name} 失败`)
  }
}

const disconnectService = async (service) => {
  try {
    // 这里应该调用后端API断开服务
    service.status = 'disconnected'

    ElMessage.success(`服务 ${service.name} 已断开`)
  } catch (error) {
    console.error('断开服务失败:', error)
    ElMessage.error(`断开服务 ${service.name} 失败`)
  }
}

const removeService = async (service) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务 "${service.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = services.value.findIndex(s => s.id === service.id)
    if (index > -1) {
      services.value.splice(index, 1)
      ElMessage.success('服务已删除')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除服务失败:', error)
      ElMessage.error('删除服务失败')
    }
  }
}

// 工具方法
const getStatusType = (status) => {
  const typeMap = {
    connected: 'success',
    disconnected: 'info',
    error: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    connected: '已连接',
    disconnected: '已断开',
    error: '错误'
  }
  return textMap[status] || status
}

const getTypeTagType = (type) => {
  const typeMap = {
    file_operations: 'primary',
    web_search: 'success',
    data_analysis: 'warning',
    git_operations: 'info',
    database: 'danger',
    api_client: '',
    custom: 'info'
  }
  return typeMap[type] || ''
}

const getTypeDisplayName = (type) => {
  const nameMap = {
    file_operations: '文件操作',
    web_search: '网络搜索',
    data_analysis: '数据分析',
    git_operations: 'Git操作',
    database: '数据库',
    api_client: 'API客户端',
    custom: '自定义'
  }
  return nameMap[type] || type
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadServices()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.services-content {
  margin-top: 20px;
}

.services-stats {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
}

pre {
  background-color: var(--el-fill-color-lighter);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.no-data {
  color: var(--el-text-color-secondary);
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }

  .header-actions {
    flex-direction: column;
    gap: 10px;
  }

  .services-stats .el-col {
    margin-bottom: 10px;
  }
}
</style>
