<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>MCP服务</span>
          <el-button type="primary">
            <el-icon><Connection /></el-icon>
            添加服务
          </el-button>
        </div>
      </template>
      
      <div class="empty-container">
        <el-icon class="empty-icon"><Connection /></el-icon>
        <div class="empty-text">MCP服务管理功能开发中...</div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
// MCP服务管理页面 - 待实现
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--el-text-color-secondary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
}
</style>
