<template>
  <div class="mcp-servers">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>MCP服务管理</span>
          <div class="header-actions">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 10px;">
              <el-option label="全部" value="" />
              <el-option label="已连接" value="connected" />
              <el-option label="已断开" value="disconnected" />
              <el-option label="错误" value="error" />
            </el-select>
            <el-button @click="refreshServers">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              添加服务
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="servers-content">
        <!-- 服务统计 -->
        <div class="servers-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总服务数" :value="serverStats.total" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已连接" :value="serverStats.connected" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="已断开" :value="serverStats.disconnected" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="错误状态" :value="serverStats.error" />
            </el-col>
          </el-row>
        </div>

        <!-- 服务列表 -->
        <el-table :data="filteredServers" v-loading="loading" style="width: 100%">
          <el-table-column prop="name" label="服务名称" width="200" />
          <el-table-column prop="type" label="服务类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)">{{ getTypeDisplayName(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" show-overflow-tooltip />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <status-indicator 
                :status="getStatusIndicator(row.status)" 
                :text="getStatusText(row.status)"
                :pulse="row.status === 'connecting'"
              />
            </template>
          </el-table-column>
          <el-table-column label="启动命令" width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <code v-if="row.type === 'stdio'">{{ row.command }} {{ row.args?.join(' ') }}</code>
              <span v-else>{{ row.base_url || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="功能数" width="80">
            <template #default="{ row }">
              {{ row.capabilities?.length || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="last_ping" label="最后检查" width="180">
            <template #default="{ row }">
              {{ formatTime(row.last_ping) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewServer(row)">
                详情
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="testConnection(row)"
                :loading="row.testing"
              >
                测试
              </el-button>
              <el-button 
                v-if="row.status === 'disconnected'" 
                type="text" 
                size="small" 
                @click="startServer(row)"
                :loading="row.starting"
              >
                启动
              </el-button>
              <el-button 
                v-if="row.status === 'connected'" 
                type="text" 
                size="small" 
                @click="stopServer(row)"
                :loading="row.stopping"
              >
                停止
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="editServer(row)"
              >
                编辑
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                danger 
                @click="removeServer(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 添加/编辑服务对话框 -->
    <el-dialog 
      v-model="serverDialogVisible" 
      :title="isEditing ? '编辑MCP服务' : '添加MCP服务'" 
      width="700px"
    >
      <el-form :model="serverForm" :rules="serverRules" ref="serverFormRef" label-width="120px">
        <el-form-item label="服务名称" prop="name">
          <el-input v-model="serverForm.name" placeholder="请输入服务名称" />
        </el-form-item>
        
        <el-form-item label="服务类型" prop="type">
          <el-radio-group v-model="serverForm.type" @change="handleTypeChange">
            <el-radio label="stdio">本地stdio</el-radio>
            <el-radio label="http">HTTP服务</el-radio>
            <el-radio label="websocket">WebSocket</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- stdio配置 -->
        <template v-if="serverForm.type === 'stdio'">
          <el-form-item label="启动命令" prop="command">
            <el-input v-model="serverForm.command" placeholder="如: npx, python, node" />
          </el-form-item>
          
          <el-form-item label="命令参数">
            <el-input 
              v-model="serverForm.argsText" 
              placeholder="如: @modelcontextprotocol/server-filesystem /path/to/directory"
            />
            <div class="form-tip">多个参数用空格分隔</div>
          </el-form-item>
          
          <el-form-item label="工作目录">
            <el-input v-model="serverForm.cwd" placeholder="留空使用当前目录" />
          </el-form-item>
        </template>
        
        <!-- HTTP配置 -->
        <template v-if="serverForm.type === 'http'">
          <el-form-item label="基础URL" prop="base_url">
            <el-input v-model="serverForm.base_url" placeholder="http://localhost:3000" />
          </el-form-item>
          
          <el-form-item label="请求头">
            <el-input 
              v-model="serverForm.headersText" 
              type="textarea" 
              :rows="3" 
              placeholder="JSON格式的请求头"
            />
          </el-form-item>
        </template>
        
        <!-- WebSocket配置 -->
        <template v-if="serverForm.type === 'websocket'">
          <el-form-item label="WebSocket URL" prop="ws_url">
            <el-input v-model="serverForm.ws_url" placeholder="ws://localhost:3001" />
          </el-form-item>
        </template>
        
        <el-form-item label="环境变量">
          <div class="env-editor">
            <div v-for="(env, index) in serverForm.envVars" :key="index" class="env-item">
              <el-input v-model="env.key" placeholder="变量名" style="width: 40%;" />
              <el-input v-model="env.value" placeholder="变量值" style="width: 50%; margin: 0 10px;" />
              <el-button @click="removeEnvVar(index)" type="danger" size="small">删除</el-button>
            </div>
            <el-button @click="addEnvVar" type="primary" size="small">添加环境变量</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input v-model="serverForm.description" type="textarea" :rows="3" placeholder="请输入服务描述" />
        </el-form-item>
        
        <el-form-item label="自动启动">
          <el-switch v-model="serverForm.auto_start" />
          <div class="form-tip">系统启动时自动启动此服务</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="serverDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveServer" :loading="saving">
          {{ isEditing ? '保存' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 服务详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="MCP服务详情" width="800px">
      <div v-if="selectedServer">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务名称">
            {{ selectedServer.name }}
          </el-descriptions-item>
          <el-descriptions-item label="服务类型">
            <el-tag :type="getTypeTagType(selectedServer.type)">
              {{ getTypeDisplayName(selectedServer.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <status-indicator 
              :status="getStatusIndicator(selectedServer.status)" 
              :text="getStatusText(selectedServer.status)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="进程ID" v-if="selectedServer.pid">
            {{ selectedServer.pid }}
          </el-descriptions-item>
          <el-descriptions-item label="启动命令" v-if="selectedServer.type === 'stdio'">
            <code>{{ selectedServer.command }} {{ selectedServer.args?.join(' ') }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="URL" v-if="selectedServer.base_url || selectedServer.ws_url">
            {{ selectedServer.base_url || selectedServer.ws_url }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedServer.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后检查">
            {{ formatTime(selectedServer.last_ping) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedServer.description }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">可用功能</h4>
        <div v-if="selectedServer.capabilities && selectedServer.capabilities.length > 0">
          <el-tag 
            v-for="capability in selectedServer.capabilities" 
            :key="capability"
            style="margin: 2px 4px;"
            type="success"
          >
            {{ capability }}
          </el-tag>
        </div>
        <div v-else class="no-data">暂无功能信息</div>

        <h4 style="margin: 20px 0 10px 0;">环境变量</h4>
        <div v-if="selectedServer.env && Object.keys(selectedServer.env).length > 0">
          <el-descriptions border size="small">
            <el-descriptions-item 
              v-for="(value, key) in selectedServer.env" 
              :key="key"
              :label="key"
            >
              {{ value }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div v-else class="no-data">暂无环境变量</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useMCPStore } from '@/stores/mcp'
import { formatTime } from '@/utils/formatters'
import StatusIndicator from '@/components/common/StatusIndicator.vue'

const mcpStore = useMCPStore()

// 响应式数据
const loading = ref(false)
const statusFilter = ref('')
const selectedServer = ref(null)
const saving = ref(false)
const isEditing = ref(false)

// 对话框状态
const serverDialogVisible = ref(false)
const detailDialogVisible = ref(false)

// 表单数据
const serverForm = reactive({
  name: '',
  type: 'stdio',
  description: '',
  command: '',
  args: [],
  argsText: '',
  cwd: '',
  base_url: '',
  ws_url: '',
  headers: {},
  headersText: '',
  env: {},
  envVars: [],
  auto_start: false
})

// 表单引用
const serverFormRef = ref()

// 表单验证规则
const serverRules = {
  name: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择服务类型', trigger: 'change' }
  ],
  command: [
    { required: true, message: '请输入启动命令', trigger: 'blur' }
  ],
  base_url: [
    { required: true, message: '请输入基础URL', trigger: 'blur' },
    { pattern: /^https?:\/\//, message: '请输入有效的HTTP URL', trigger: 'blur' }
  ],
  ws_url: [
    { required: true, message: '请输入WebSocket URL', trigger: 'blur' },
    { pattern: /^wss?:\/\//, message: '请输入有效的WebSocket URL', trigger: 'blur' }
  ]
}

// 计算属性
const filteredServers = computed(() => {
  if (!statusFilter.value) return mcpStore.servers
  return mcpStore.servers.filter(server => server.status === statusFilter.value)
})

const serverStats = computed(() => mcpStore.getServerStats())

// 方法
const refreshServers = async () => {
  loading.value = true
  try {
    await mcpStore.fetchServers()
  } finally {
    loading.value = false
  }
}

const showAddDialog = () => {
  isEditing.value = false
  resetServerForm()
  serverDialogVisible.value = true
}

const editServer = (server) => {
  isEditing.value = true
  fillServerForm(server)
  serverDialogVisible.value = true
}

const resetServerForm = () => {
  Object.assign(serverForm, {
    name: '',
    type: 'stdio',
    description: '',
    command: '',
    args: [],
    argsText: '',
    cwd: '',
    base_url: '',
    ws_url: '',
    headers: {},
    headersText: '',
    env: {},
    envVars: [],
    auto_start: false
  })
}

const fillServerForm = (server) => {
  Object.assign(serverForm, {
    id: server.id,
    name: server.name,
    type: server.type,
    description: server.description || '',
    command: server.command || '',
    args: server.args || [],
    argsText: server.args ? server.args.join(' ') : '',
    cwd: server.cwd || '',
    base_url: server.base_url || '',
    ws_url: server.ws_url || '',
    headers: server.headers || {},
    headersText: server.headers ? JSON.stringify(server.headers, null, 2) : '',
    env: server.env || {},
    envVars: server.env ? Object.entries(server.env).map(([key, value]) => ({ key, value })) : [],
    auto_start: server.auto_start || false
  })
}

const handleTypeChange = () => {
  // 清空其他类型的配置
  if (serverForm.type === 'stdio') {
    serverForm.base_url = ''
    serverForm.ws_url = ''
    serverForm.headersText = ''
  } else if (serverForm.type === 'http') {
    serverForm.command = ''
    serverForm.argsText = ''
    serverForm.cwd = ''
    serverForm.ws_url = ''
  } else if (serverForm.type === 'websocket') {
    serverForm.command = ''
    serverForm.argsText = ''
    serverForm.cwd = ''
    serverForm.base_url = ''
    serverForm.headersText = ''
  }
}

const addEnvVar = () => {
  serverForm.envVars.push({ key: '', value: '' })
}

const removeEnvVar = (index) => {
  serverForm.envVars.splice(index, 1)
}

const saveServer = async () => {
  if (!serverFormRef.value) return

  try {
    await serverFormRef.value.validate()

    saving.value = true

    // 准备服务器数据
    const serverData = {
      name: serverForm.name,
      type: serverForm.type,
      description: serverForm.description,
      auto_start: serverForm.auto_start
    }

    // 根据类型添加特定配置
    if (serverForm.type === 'stdio') {
      serverData.command = serverForm.command
      serverData.args = serverForm.argsText ? serverForm.argsText.split(' ').filter(arg => arg.trim()) : []
      if (serverForm.cwd) {
        serverData.cwd = serverForm.cwd
      }
    } else if (serverForm.type === 'http') {
      serverData.base_url = serverForm.base_url
      if (serverForm.headersText) {
        try {
          serverData.headers = JSON.parse(serverForm.headersText)
        } catch (error) {
          ElMessage.error('请求头格式错误，请检查JSON格式')
          return
        }
      }
    } else if (serverForm.type === 'websocket') {
      serverData.ws_url = serverForm.ws_url
    }

    // 添加环境变量
    serverData.env = {}
    serverForm.envVars.forEach(envVar => {
      if (envVar.key && envVar.value) {
        serverData.env[envVar.key] = envVar.value
      }
    })

    if (isEditing.value) {
      await mcpStore.updateServer(serverForm.id, serverData)
    } else {
      await mcpStore.createServer(serverData)
    }

    serverDialogVisible.value = false

  } catch (error) {
    console.error('保存MCP服务失败:', error)
  } finally {
    saving.value = false
  }
}

const viewServer = (server) => {
  selectedServer.value = server
  detailDialogVisible.value = true
}

const testConnection = async (server) => {
  server.testing = true
  try {
    await mcpStore.testServer(server.id)
    ElMessage.success(`服务 ${server.name} 连接测试成功`)
  } catch (error) {
    ElMessage.error(`服务 ${server.name} 连接测试失败`)
  } finally {
    server.testing = false
  }
}

const startServer = async (server) => {
  server.starting = true
  try {
    await mcpStore.toggleServer(server.id, true)
    ElMessage.success(`服务 ${server.name} 启动成功`)
  } catch (error) {
    ElMessage.error(`启动服务 ${server.name} 失败`)
  } finally {
    server.starting = false
  }
}

const stopServer = async (server) => {
  server.stopping = true
  try {
    await mcpStore.toggleServer(server.id, false)
    ElMessage.success(`服务 ${server.name} 已停止`)
  } catch (error) {
    ElMessage.error(`停止服务 ${server.name} 失败`)
  } finally {
    server.stopping = false
  }
}

const removeServer = async (server) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务 "${server.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await mcpStore.deleteServer(server.id)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除MCP服务失败:', error)
    }
  }
}

// 工具方法
const getStatusIndicator = (status) => {
  const statusMap = {
    'connected': 'success',
    'disconnected': 'default',
    'connecting': 'processing',
    'error': 'error'
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status) => {
  const textMap = {
    'connected': '已连接',
    'disconnected': '已断开',
    'connecting': '连接中',
    'error': '错误'
  }
  return textMap[status] || status
}

const getTypeTagType = (type) => {
  const typeMap = {
    'stdio': 'primary',
    'http': 'success',
    'websocket': 'warning'
  }
  return typeMap[type] || ''
}

const getTypeDisplayName = (type) => {
  return mcpStore.getServerTypeDisplayName(type)
}

// 生命周期
onMounted(() => {
  refreshServers()
})
</script>

<style scoped>
.mcp-servers {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.servers-content {
  margin-top: 20px;
}

.servers-stats {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.env-editor {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 10px;
}

.env-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.env-item:last-child {
  margin-bottom: 0;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.no-data {
  color: var(--el-text-color-secondary);
  text-align: center;
  padding: 20px;
}

code {
  background-color: var(--el-fill-color-lighter);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mcp-servers {
    padding: 10px;
  }

  .header-actions {
    flex-direction: column;
    gap: 10px;
  }

  .servers-stats .el-col {
    margin-bottom: 10px;
  }

  .env-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .env-item .el-input {
    width: 100% !important;
    margin: 0 !important;
  }
}
</style>
