<template>
  <div class="not-found">
    <div class="not-found-container">
      <!-- 404图标 -->
      <div class="error-icon">
        <svg viewBox="0 0 200 200" class="error-svg">
          <!-- 404数字 -->
          <text x="100" y="80" class="error-text">404</text>
          <!-- 装饰元素 -->
          <circle cx="50" cy="120" r="8" class="decoration-circle" />
          <circle cx="150" cy="120" r="8" class="decoration-circle" />
          <path d="M60 140 Q100 160 140 140" stroke="#e4e7ed" stroke-width="3" fill="none" class="decoration-path" />
        </svg>
      </div>

      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        <p class="error-suggestion">
          请检查URL是否正确，或者返回首页继续浏览。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button type="primary" size="large" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button size="large" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
        <el-button type="info" size="large" @click="reportIssue">
          <el-icon><Warning /></el-icon>
          报告问题
        </el-button>
      </div>

      <!-- 快速导航 -->
      <div class="quick-nav">
        <h3>快速导航</h3>
        <div class="nav-grid">
          <div class="nav-item" @click="navigateTo('/dashboard')">
            <el-icon class="nav-icon"><Odometer /></el-icon>
            <span class="nav-text">仪表板</span>
          </div>
          <div class="nav-item" @click="navigateTo('/workflows')">
            <el-icon class="nav-icon"><Operation /></el-icon>
            <span class="nav-text">工作流</span>
          </div>
          <div class="nav-item" @click="navigateTo('/agents')">
            <el-icon class="nav-icon"><User /></el-icon>
            <span class="nav-text">智能体</span>
          </div>
          <div class="nav-item" @click="navigateTo('/sessions')">
            <el-icon class="nav-icon"><Document /></el-icon>
            <span class="nav-text">会话</span>
          </div>
          <div class="nav-item" @click="navigateTo('/tasks')">
            <el-icon class="nav-icon"><List /></el-icon>
            <span class="nav-text">任务</span>
          </div>
          <div class="nav-item" @click="navigateTo('/settings')">
            <el-icon class="nav-icon"><Setting /></el-icon>
            <span class="nav-text">设置</span>
          </div>
        </div>
      </div>

      <!-- 帮助信息 -->
      <div class="help-info">
        <el-alert
          title="需要帮助？"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>如果您认为这是一个错误，请联系系统管理员或查看帮助文档。</p>
            <div class="help-links">
              <el-link type="primary" @click="openHelp">查看帮助文档</el-link>
              <el-link type="primary" @click="contactSupport">联系技术支持</el-link>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  HomeFilled,
  ArrowLeft,
  Warning,
  Odometer,
  Operation,
  User,
  Document,
  List,
  Setting
} from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 报告问题
const reportIssue = () => {
  ElMessage.info('问题报告功能开发中，请联系系统管理员')
}

// 导航到指定页面
const navigateTo = (path) => {
  router.push(path)
}

// 打开帮助文档
const openHelp = () => {
  ElMessage.info('帮助文档功能开发中')
}

// 联系技术支持
const contactSupport = () => {
  ElMessage.info('技术支持联系功能开发中')
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.not-found-container {
  max-width: 600px;
  width: 90%;
  text-align: center;
  background: white;
  border-radius: 16px;
  padding: 48px 32px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.error-icon {
  margin-bottom: 32px;
}

.error-svg {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  display: block;
}

.error-text {
  font-size: 48px;
  font-weight: 700;
  fill: #409eff;
  text-anchor: middle;
  dominant-baseline: middle;
}

.decoration-circle {
  fill: #e4e7ed;
  animation: bounce 2s infinite;
}

.decoration-circle:nth-child(3) {
  animation-delay: 0.5s;
}

.decoration-path {
  stroke-dasharray: 5, 5;
  animation: dash 3s linear infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}

.error-content {
  margin-bottom: 32px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 16px;
  color: #606266;
  margin: 0 0 12px 0;
  line-height: 1.6;
}

.error-suggestion {
  font-size: 14px;
  color: #909399;
  margin: 0;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 48px;
  flex-wrap: wrap;
}

.quick-nav {
  margin-bottom: 32px;
}

.quick-nav h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 24px 0;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.nav-item:hover {
  background: #e3f2fd;
  border-color: #409eff;
  transform: translateY(-2px);
}

.nav-icon {
  font-size: 24px;
  color: #409eff;
}

.nav-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.help-info {
  text-align: left;
}

.help-links {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 10%;
  right: 10%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(20px) rotate(240deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-container {
    padding: 32px 24px;
  }
  
  .error-svg {
    width: 150px;
    height: 150px;
  }
  
  .error-text {
    font-size: 36px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .help-links {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .nav-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-item {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
}
</style>