<template>
  <div class="session-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="$router.go(-1)"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="session-info">
          <h1>{{ sessionData.title }}</h1>
          <div class="session-meta">
            <el-tag :type="getStatusType(sessionData.status)">{{ sessionData.status }}</el-tag>
            <span class="meta-item">创建时间：{{ sessionData.createdAt }}</span>
            <span class="meta-item">持续时间：{{ sessionData.duration }}</span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="exportSession">
          <el-icon><Download /></el-icon>
          导出会话
        </el-button>
        <el-button @click="shareSession">
          <el-icon><Share /></el-icon>
          分享
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button>
            更多操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="duplicate">复制会话</el-dropdown-item>
              <el-dropdown-item command="archive">归档</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 会话内容区域 -->
    <div class="session-content">
      <div class="content-main">
        <!-- 消息列表 -->
        <div class="message-list" ref="messageList">
          <div 
            v-for="message in messages" 
            :key="message.id"
            :class="['message-item', message.role]"
          >
            <div class="message-avatar">
              <el-avatar 
                v-if="message.role === 'user'"
                :size="32"
                :src="message.avatar"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <el-avatar 
                v-else
                :size="32"
                class="agent-avatar"
              >
                <el-icon><Robot /></el-icon>
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="sender-name">{{ message.senderName }}</span>
                <span class="message-time">{{ message.timestamp }}</span>
              </div>
              <div class="message-body">
                <div v-if="message.type === 'text'" class="text-message">
                  {{ message.content }}
                </div>
                <div v-else-if="message.type === 'code'" class="code-message">
                  <pre><code>{{ message.content }}</code></pre>
                </div>
                <div v-else-if="message.type === 'file'" class="file-message">
                  <el-icon><Document /></el-icon>
                  <span>{{ message.fileName }}</span>
                  <el-button type="text" size="small">下载</el-button>
                </div>
              </div>
              <div class="message-actions">
                <el-button type="text" size="small" @click="copyMessage(message)">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-button>
                <el-button type="text" size="small" @click="replyMessage(message)">
                  <el-icon><ChatDotRound /></el-icon>
                  回复
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area" v-if="sessionData.status === '进行中'">
          <div class="input-toolbar">
            <el-button type="text" @click="attachFile">
              <el-icon><Paperclip /></el-icon>
              附件
            </el-button>
            <el-button type="text" @click="insertCode">
              <el-icon><Code /></el-icon>
              代码
            </el-button>
          </div>
          <div class="input-box">
            <el-input
              v-model="newMessage"
              type="textarea"
              :rows="3"
              placeholder="输入消息..."
              @keydown.ctrl.enter="sendMessage"
            />
            <div class="input-actions">
              <span class="input-tip">Ctrl + Enter 发送</span>
              <el-button type="primary" @click="sendMessage" :disabled="!newMessage.trim()">
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="content-sidebar">
        <!-- 会话信息 -->
        <el-card class="info-card">
          <template #header>
            <span>会话信息</span>
          </template>
          <div class="info-item">
            <label>会话ID：</label>
            <span>{{ sessionData.id }}</span>
          </div>
          <div class="info-item">
            <label>关联智能体：</label>
            <el-link type="primary" @click="viewAgent(sessionData.agentId)">
              {{ sessionData.agentName }}
            </el-link>
          </div>
          <div class="info-item">
            <label>消息数量：</label>
            <span>{{ messages.length }}</span>
          </div>
          <div class="info-item">
            <label>参与人数：</label>
            <span>{{ sessionData.participants }}</span>
          </div>
        </el-card>

        <!-- 会话统计 -->
        <el-card class="stats-card">
          <template #header>
            <span>会话统计</span>
          </template>
          <div class="stat-item">
            <div class="stat-value">{{ sessionStats.totalTokens }}</div>
            <div class="stat-label">总Token数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ sessionStats.avgResponseTime }}ms</div>
            <div class="stat-label">平均响应时间</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ sessionStats.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </el-card>

        <!-- 相关文件 -->
        <el-card class="files-card">
          <template #header>
            <span>相关文件</span>
          </template>
          <div class="file-list">
            <div 
              v-for="file in relatedFiles" 
              :key="file.id"
              class="file-item"
              @click="viewFile(file)"
            >
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ file.size }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Download,
  Share,
  ArrowDown,
  User,
  Robot,
  Document,
  CopyDocument,
  ChatDotRound,
  Paperclip,
  Code
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const messageList = ref(null)
const newMessage = ref('')

// 会话数据
const sessionData = reactive({
  id: route.params.id,
  title: '智能代码生成会话',
  status: '进行中',
  createdAt: '2024-01-15 10:30:00',
  duration: '2小时30分钟',
  agentId: 'agent_001',
  agentName: 'CodeGen Assistant',
  participants: 2
})

// 消息列表
const messages = ref([
  {
    id: 1,
    role: 'user',
    type: 'text',
    content: '请帮我生成一个Vue组件，用于显示用户列表',
    senderName: '用户',
    timestamp: '10:30:15',
    avatar: ''
  },
  {
    id: 2,
    role: 'assistant',
    type: 'text',
    content: '好的，我来为您生成一个Vue用户列表组件。这个组件将包含用户数据展示、搜索和分页功能。',
    senderName: 'CodeGen Assistant',
    timestamp: '10:30:18'
  },
  {
    id: 3,
    role: 'assistant',
    type: 'code',
    content: `<template>\n  <div class="user-list">\n    <el-table :data="users" style="width: 100%">\n      <el-table-column prop="name" label="姓名" />\n      <el-table-column prop="email" label="邮箱" />\n    </el-table>\n  </div>\n</template>`,
    senderName: 'CodeGen Assistant',
    timestamp: '10:30:25'
  },
  {
    id: 4,
    role: 'user',
    type: 'text',
    content: '很好，能否添加编辑和删除功能？',
    senderName: '用户',
    timestamp: '10:32:10',
    avatar: ''
  }
])

// 会话统计
const sessionStats = reactive({
  totalTokens: 1250,
  avgResponseTime: 850,
  successRate: 98.5
})

// 相关文件
const relatedFiles = ref([
  {
    id: 1,
    name: 'UserList.vue',
    size: '2.5KB',
    type: 'vue'
  },
  {
    id: 2,
    name: 'user-api.js',
    size: '1.8KB',
    type: 'js'
  },
  {
    id: 3,
    name: 'user-types.ts',
    size: '0.9KB',
    type: 'ts'
  }
])

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '进行中': 'success',
    '已完成': 'info',
    '已暂停': 'warning',
    '已终止': 'danger'
  }
  return statusMap[status] || 'info'
}

// 发送消息
const sendMessage = () => {
  if (!newMessage.value.trim()) return
  
  const message = {
    id: Date.now(),
    role: 'user',
    type: 'text',
    content: newMessage.value,
    senderName: '用户',
    timestamp: new Date().toLocaleTimeString(),
    avatar: ''
  }
  
  messages.value.push(message)
  newMessage.value = ''
  
  // 滚动到底部
  nextTick(() => {
    if (messageList.value) {
      messageList.value.scrollTop = messageList.value.scrollHeight
    }
  })
  
  // 模拟AI回复
  setTimeout(() => {
    const aiMessage = {
      id: Date.now() + 1,
      role: 'assistant',
      type: 'text',
      content: '我已经收到您的消息，正在处理中...',
      senderName: 'CodeGen Assistant',
      timestamp: new Date().toLocaleTimeString()
    }
    messages.value.push(aiMessage)
    
    nextTick(() => {
      if (messageList.value) {
        messageList.value.scrollTop = messageList.value.scrollHeight
      }
    })
  }, 1000)
}

// 复制消息
const copyMessage = (message) => {
  navigator.clipboard.writeText(message.content)
  ElMessage.success('消息已复制到剪贴板')
}

// 回复消息
const replyMessage = (message) => {
  newMessage.value = `回复 @${message.senderName}: `
}

// 附加文件
const attachFile = () => {
  ElMessage.info('文件上传功能开发中')
}

// 插入代码
const insertCode = () => {
  newMessage.value += '\n```\n\n```'
}

// 导出会话
const exportSession = () => {
  ElMessage.success('会话导出功能开发中')
}

// 分享会话
const shareSession = () => {
  ElMessage.success('会话分享功能开发中')
}

// 处理更多操作
const handleCommand = (command) => {
  switch (command) {
    case 'duplicate':
      ElMessage.success('会话复制功能开发中')
      break
    case 'archive':
      ElMessage.success('会话归档功能开发中')
      break
    case 'delete':
      ElMessageBox.confirm('确定要删除这个会话吗？', '确认删除', {
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
        router.push('/sessions')
      })
      break
  }
}

// 查看智能体
const viewAgent = (agentId) => {
  router.push(`/agents/${agentId}`)
}

// 查看文件
const viewFile = (file) => {
  ElMessage.info(`查看文件: ${file.name}`)
}

onMounted(() => {
  // 滚动到底部
  nextTick(() => {
    if (messageList.value) {
      messageList.value.scrollTop = messageList.value.scrollHeight
    }
  })
})
</script>

<style scoped>
.session-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.session-info h1 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.session-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.meta-item {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.session-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  margin: 16px 0 16px 16px;
  border-radius: 8px;
  overflow: hidden;
}

.message-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-item.user .message-content {
  text-align: right;
}

.message-avatar {
  flex-shrink: 0;
}

.agent-avatar {
  background: #409eff;
  color: white;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.sender-name {
  font-weight: 500;
  color: #606266;
}

.message-body {
  margin-bottom: 8px;
}

.text-message {
  background: #f5f7fa;
  padding: 12px 16px;
  border-radius: 8px;
  line-height: 1.5;
  color: #303133;
}

.message-item.user .text-message {
  background: #409eff;
  color: white;
}

.code-message {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.code-message pre {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  overflow-x: auto;
}

.file-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.message-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.input-area {
  border-top: 1px solid #e4e7ed;
  padding: 16px;
}

.input-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.input-box {
  position: relative;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-tip {
  font-size: 12px;
  color: #909399;
}

.content-sidebar {
  width: 300px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card,
.stats-card,
.files-card {
  background: white;
  border-radius: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  color: #909399;
  font-weight: 500;
}

.stats-card .el-card__body {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  cursor: pointer;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.2s;
}

.file-item:hover {
  background: #f5f7fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.file-size {
  font-size: 12px;
  color: #909399;
}
</style>