<template>
  <div class="sessions-container">
    <div class="page-header">
      <h1>会话管理</h1>
      <p>管理您的AI对话会话</p>
    </div>
    
    <div class="toolbar">
      <el-button type="primary" @click="createSession">
        <el-icon><Plus /></el-icon>
        新建会话
      </el-button>
      <el-button @click="refreshSessions">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-input 
        v-model="searchText" 
        placeholder="搜索会话..."
        style="width: 200px;"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
        <el-option label="全部" value="" />
        <el-option label="活跃" value="active" />
        <el-option label="已结束" value="ended" />
        <el-option label="暂停" value="paused" />
      </el-select>
    </div>

    <div class="sessions-list">
      <el-card 
        v-for="session in filteredSessions" 
        :key="session.id" 
        class="session-card"
        @click="openSession(session)"
      >
        <div class="session-header">
          <div class="session-info">
            <h3>{{ session.title }}</h3>
            <div class="session-meta">
              <el-tag :type="getStatusType(session.status)" size="small">
                {{ getStatusText(session.status) }}
              </el-tag>
              <span class="agent-name">{{ session.agentName }}</span>
              <span class="message-count">{{ session.messageCount }} 条消息</span>
            </div>
          </div>
          
          <div class="session-actions">
            <el-dropdown @command="handleCommand" @click.stop>
              <el-button type="text">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'rename', session}">
                    重命名
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'export', session}">
                    导出
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="{action: 'archive', session}"
                    :disabled="session.status === 'ended'"
                  >
                    归档
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="{action: 'delete', session}"
                    divided
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <div class="session-content">
          <div class="last-message">
            <div class="message-preview">
              <span class="sender">{{ session.lastMessage?.sender || '系统' }}:</span>
              <span class="content">{{ session.lastMessage?.content || '暂无消息' }}</span>
            </div>
            <div class="message-time">
              {{ formatTime(session.lastMessage?.timestamp || session.updatedAt) }}
            </div>
          </div>
          
          <div class="session-stats">
            <div class="stat-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatDate(session.createdAt) }}</span>
            </div>
            <div class="stat-item">
              <span class="label">持续时间:</span>
              <span class="value">{{ getDuration(session) }}</span>
            </div>
            <div class="stat-item">
              <span class="label">Token使用:</span>
              <span class="value">{{ session.tokenUsage || 0 }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredSessions.length === 0" class="empty-state">
      <el-empty description="暂无会话">
        <el-button type="primary" @click="createSession">
          创建第一个会话
        </el-button>
      </el-empty>
    </div>

    <!-- 重命名对话框 -->
    <el-dialog v-model="renameDialogVisible" title="重命名会话" width="400px">
      <el-form :model="renameForm" ref="renameFormRef">
        <el-form-item 
          label="会话标题" 
          prop="title"
          :rules="[{ required: true, message: '请输入会话标题', trigger: 'blur' }]"
        >
          <el-input v-model="renameForm.title" placeholder="请输入新的会话标题" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="renameDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRename">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 会话详情对话框 -->
    <el-dialog 
      v-model="sessionDetailVisible" 
      :title="selectedSession?.title"
      width="800px"
      class="session-detail-dialog"
    >
      <div v-if="selectedSession" class="session-detail">
        <div class="session-info-panel">
          <div class="info-row">
            <span class="label">智能体:</span>
            <span class="value">{{ selectedSession.agentName }}</span>
          </div>
          <div class="info-row">
            <span class="label">状态:</span>
            <el-tag :type="getStatusType(selectedSession.status)">
              {{ getStatusText(selectedSession.status) }}
            </el-tag>
          </div>
          <div class="info-row">
            <span class="label">消息数:</span>
            <span class="value">{{ selectedSession.messageCount }}</span>
          </div>
          <div class="info-row">
            <span class="label">Token使用:</span>
            <span class="value">{{ selectedSession.tokenUsage }}</span>
          </div>
        </div>
        
        <div class="messages-container">
          <h4>消息记录</h4>
          <div class="messages-list">
            <div 
              v-for="message in selectedSession.messages" 
              :key="message.id"
              class="message-item"
              :class="message.sender"
            >
              <div class="message-header">
                <span class="sender-name">{{ message.sender }}</span>
                <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sessionDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="continueSession">
            继续对话
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, MoreFilled } from '@element-plus/icons-vue'

const searchText = ref('')
const statusFilter = ref('')
const renameDialogVisible = ref(false)
const sessionDetailVisible = ref(false)
const renameFormRef = ref(null)
const selectedSession = ref(null)
const sessions = ref([])

const renameForm = ref({
  id: null,
  title: ''
})

// 模拟数据
const mockSessions = [
  {
    id: 1,
    title: '代码优化讨论',
    agentName: '代码助手',
    status: 'active',
    messageCount: 15,
    tokenUsage: 2500,
    createdAt: new Date('2024-01-15T10:00:00'),
    updatedAt: new Date('2024-01-15T14:30:00'),
    lastMessage: {
      id: 15,
      sender: '代码助手',
      content: '这个优化方案看起来不错，可以提升约30%的性能。',
      timestamp: new Date('2024-01-15T14:30:00')
    },
    messages: [
      {
        id: 1,
        sender: '用户',
        content: '你好，我需要优化这段Python代码的性能。',
        timestamp: new Date('2024-01-15T10:00:00')
      },
      {
        id: 2,
        sender: '代码助手',
        content: '你好！我很乐意帮助你优化代码。请分享你的代码，我来分析一下。',
        timestamp: new Date('2024-01-15T10:01:00')
      }
    ]
  },
  {
    id: 2,
    title: '数据分析项目',
    agentName: '数据分析师',
    status: 'ended',
    messageCount: 8,
    tokenUsage: 1200,
    createdAt: new Date('2024-01-14T09:00:00'),
    updatedAt: new Date('2024-01-14T11:45:00'),
    lastMessage: {
      id: 8,
      sender: '数据分析师',
      content: '分析完成，已生成详细报告。',
      timestamp: new Date('2024-01-14T11:45:00')
    },
    messages: [
      {
        id: 1,
        sender: '用户',
        content: '请帮我分析这份销售数据。',
        timestamp: new Date('2024-01-14T09:00:00')
      }
    ]
  },
  {
    id: 3,
    title: '产品需求讨论',
    agentName: '通用助手',
    status: 'paused',
    messageCount: 22,
    tokenUsage: 3800,
    createdAt: new Date('2024-01-13T15:00:00'),
    updatedAt: new Date('2024-01-13T17:20:00'),
    lastMessage: {
      id: 22,
      sender: '用户',
      content: '让我再想想这个功能的具体实现...',
      timestamp: new Date('2024-01-13T17:20:00')
    },
    messages: []
  }
]

// 计算属性
const filteredSessions = computed(() => {
  let filtered = sessions.value
  
  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(session => session.status === statusFilter.value)
  }
  
  // 搜索筛选
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filtered = filtered.filter(session => 
      session.title.toLowerCase().includes(searchLower) ||
      session.agentName.toLowerCase().includes(searchLower)
    )
  }
  
  return filtered.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
})

onMounted(() => {
  loadSessions()
})

const loadSessions = () => {
  sessions.value = mockSessions
}

const refreshSessions = () => {
  loadSessions()
  ElMessage.success('会话列表已刷新')
}

const createSession = () => {
  ElMessage.success('创建新会话功能开发中...')
}

const openSession = (session) => {
  selectedSession.value = session
  sessionDetailVisible.value = true
}

const handleCommand = (command) => {
  const { action, session } = command
  
  switch (action) {
    case 'rename':
      renameSession(session)
      break
    case 'export':
      exportSession(session)
      break
    case 'archive':
      archiveSession(session)
      break
    case 'delete':
      deleteSession(session)
      break
  }
}

const renameSession = (session) => {
  renameForm.value = {
    id: session.id,
    title: session.title
  }
  renameDialogVisible.value = true
}

const confirmRename = async () => {
  try {
    await renameFormRef.value.validate()
    
    const session = sessions.value.find(s => s.id === renameForm.value.id)
    if (session) {
      session.title = renameForm.value.title
      ElMessage.success('会话重命名成功')
    }
    
    renameDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const exportSession = (session) => {
  ElMessage.success(`会话 "${session.title}" 导出成功`)
}

const archiveSession = (session) => {
  session.status = 'ended'
  ElMessage.success(`会话 "${session.title}" 已归档`)
}

const deleteSession = async (session) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会话 "${session.title}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = sessions.value.findIndex(s => s.id === session.id)
    if (index > -1) {
      sessions.value.splice(index, 1)
      ElMessage.success('会话删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const continueSession = () => {
  ElMessage.success(`继续会话 "${selectedSession.value.title}"`)
  sessionDetailVisible.value = false
}

const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    ended: 'info',
    paused: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    active: '活跃',
    ended: '已结束',
    paused: '暂停'
  }
  return statusMap[status] || '未知'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getDuration = (session) => {
  const start = new Date(session.createdAt)
  const end = new Date(session.updatedAt)
  const diffMs = end - start
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes}分钟`
  }
  return `${diffMinutes}分钟`
}
</script>

<style scoped>
.sessions-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.session-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.session-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.session-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.agent-name {
  color: #409eff;
  font-weight: 500;
}

.session-content {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.last-message {
  margin-bottom: 12px;
}

.message-preview {
  margin-bottom: 4px;
  line-height: 1.4;
}

.sender {
  font-weight: 500;
  color: #303133;
}

.content {
  color: #666;
  margin-left: 4px;
}

.message-time {
  font-size: 11px;
  color: #999;
}

.session-stats {
  display: flex;
  gap: 20px;
  font-size: 11px;
}

.stat-item {
  display: flex;
  gap: 4px;
}

.label {
  color: #999;
}

.value {
  color: #666;
  font-weight: 500;
}

.empty-state {
  margin-top: 60px;
}

.session-detail {
  max-height: 500px;
  overflow-y: auto;
}

.session-info-panel {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.messages-container h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.messages-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.message-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item.用户 {
  background: #f0f9ff;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.sender-name {
  font-weight: 600;
  color: #303133;
}

.timestamp {
  color: #999;
}

.message-content {
  line-height: 1.5;
  color: #666;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}
</style>