<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>执行会话</span>
          <div class="header-actions">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 10px;">
              <el-option label="全部" value="" />
              <el-option label="等待中" value="pending" />
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
            <el-button @click="refreshSessions">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建会话
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="filteredSessions" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="会话ID" width="200" show-overflow-tooltip />
        <el-table-column prop="workflow_name" label="工作流" width="200" />
        <el-table-column prop="user_id" label="用户ID" width="150" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="120">
          <template #default="{ row }">
            <el-progress :percentage="getProgress(row)" :status="getProgressStatus(row.status)" />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewSession(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="viewTasks(row)">
              任务
            </el-button>
            <el-button type="text" size="small" @click="viewLogs(row)">
              日志
            </el-button>
            <el-button
              v-if="row.status === 'running'"
              type="text"
              size="small"
              danger
              @click="stopSession(row)"
            >
              停止
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; text-align: right;"
      />
    </el-card>

    <!-- 创建会话对话框 -->
    <el-dialog v-model="createDialogVisible" title="创建新会话" width="600px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="工作流" prop="workflow_name">
          <el-select v-model="createForm.workflow_name" placeholder="请选择工作流" style="width: 100%;">
            <el-option
              v-for="workflow in availableWorkflows"
              :key="workflow.workflow_id"
              :label="workflow.name"
              :value="workflow.workflow_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="createForm.user_id" placeholder="可选，留空使用默认用户" />
        </el-form-item>
        <el-form-item label="输入数据" prop="input_data">
          <el-input
            v-model="createForm.input_data"
            type="textarea"
            :rows="6"
            placeholder="请输入JSON格式的输入数据"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createSession" :loading="createLoading">
          创建并执行
        </el-button>
      </template>
    </el-dialog>

    <!-- 会话详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="会话详情" width="800px">
      <div v-if="selectedSession">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="会话ID">
            {{ selectedSession.id }}
          </el-descriptions-item>
          <el-descriptions-item label="工作流">
            {{ selectedSession.workflow_name }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ selectedSession.user_id || '默认用户' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedSession.status)">
              {{ getStatusText(selectedSession.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedSession.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(selectedSession.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">输入数据</h4>
        <pre>{{ JSON.stringify(selectedSession.input_data, null, 2) }}</pre>

        <h4 style="margin: 20px 0 10px 0;">输出数据</h4>
        <pre v-if="selectedSession.output_data">{{ JSON.stringify(selectedSession.output_data, null, 2) }}</pre>
        <div v-else class="no-data">暂无输出数据</div>
      </div>
    </el-dialog>

    <!-- 任务列表对话框 -->
    <el-dialog v-model="tasksDialogVisible" title="会话任务" width="900px">
      <el-table :data="sessionTasks" v-loading="tasksLoading" style="width: 100%">
        <el-table-column prop="id" label="任务ID" width="200" show-overflow-tooltip />
        <el-table-column prop="agent_name" label="智能体" width="150" />
        <el-table-column prop="task_name" label="任务名称" width="200" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="started_at" label="开始时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.started_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="completed_at" label="完成时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.completed_at) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 日志对话框 -->
    <el-dialog v-model="logsDialogVisible" title="会话日志" width="900px">
      <div class="logs-container">
        <div class="logs-header">
          <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px;" @change="loadSessionLogs">
            <el-option label="全部" value="" />
            <el-option label="DEBUG" value="DEBUG" />
            <el-option label="INFO" value="INFO" />
            <el-option label="WARNING" value="WARNING" />
            <el-option label="ERROR" value="ERROR" />
          </el-select>
          <el-button @click="loadSessionLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>

        <div class="logs-content" v-loading="logsLoading">
          <div v-for="log in sessionLogs" :key="log.id" class="log-item" :class="`log-${log.level.toLowerCase()}`">
            <div class="log-header">
              <span class="log-level">{{ log.level }}</span>
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            </div>
            <div class="log-message">{{ log.message }}</div>
            <div v-if="log.context" class="log-context">
              <pre>{{ JSON.stringify(log.context, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { sessionApi, workflowApi } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const sessions = ref([])
const selectedSession = ref(null)
const sessionTasks = ref([])
const sessionLogs = ref([])
const availableWorkflows = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 筛选
const statusFilter = ref('')

// 对话框状态
const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const tasksDialogVisible = ref(false)
const logsDialogVisible = ref(false)

// 加载状态
const createLoading = ref(false)
const tasksLoading = ref(false)
const logsLoading = ref(false)

// 日志级别
const logLevel = ref('')

// 表单数据
const createForm = ref({
  workflow_name: '',
  user_id: '',
  input_data: ''
})

// 表单引用
const createFormRef = ref()

// 表单验证规则
const createRules = {
  workflow_name: [
    { required: true, message: '请选择工作流', trigger: 'change' }
  ],
  input_data: [
    { required: true, message: '请输入输入数据', trigger: 'blur' },
    { validator: validateJSON, trigger: 'blur' }
  ]
}

// 计算属性
const filteredSessions = computed(() => {
  if (!statusFilter.value) {
    return sessions.value
  }
  return sessions.value.filter(session => session.status === statusFilter.value)
})

// 验证器
function validateJSON(rule, value, callback) {
  if (!value.trim()) {
    callback(new Error('请输入输入数据'))
    return
  }

  try {
    JSON.parse(value)
    callback()
  } catch (error) {
    callback(new Error('输入数据必须是有效的JSON格式'))
  }
}

// 方法
const loadSessions = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      status: statusFilter.value || undefined
    }

    const response = await sessionApi.getSessions(params)
    sessions.value = response.sessions || []
    total.value = response.total || 0
  } catch (error) {
    console.error('加载会话列表失败:', error)
    ElMessage.error('加载会话列表失败')
  } finally {
    loading.value = false
  }
}

const loadWorkflows = async () => {
  try {
    const response = await workflowApi.getWorkflows()
    availableWorkflows.value = response.workflows || []
  } catch (error) {
    console.error('加载工作流列表失败:', error)
  }
}

const refreshSessions = () => {
  loadSessions()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadSessions()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadSessions()
}

const showCreateDialog = () => {
  createForm.value = {
    workflow_name: '',
    user_id: '',
    input_data: '{\n  \n}'
  }
  createDialogVisible.value = true
}

const createSession = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()

    createLoading.value = true

    // 解析输入数据
    const inputData = JSON.parse(createForm.value.input_data)

    const sessionData = {
      workflow_name: createForm.value.workflow_name,
      user_id: createForm.value.user_id || undefined,
      input_data: inputData
    }

    const response = await sessionApi.createSession(sessionData)

    ElMessage.success('会话创建成功，正在执行...')
    createDialogVisible.value = false

    // 刷新列表
    loadSessions()

    // 跳转到会话详情
    if (response.session_id) {
      router.push(`/session/${response.session_id}`)
    }

  } catch (error) {
    console.error('创建会话失败:', error)
    if (error.message.includes('JSON')) {
      ElMessage.error('输入数据格式错误，请检查JSON格式')
    } else {
      ElMessage.error('创建会话失败')
    }
  } finally {
    createLoading.value = false
  }
}

const viewSession = (session) => {
  selectedSession.value = session
  detailDialogVisible.value = true
}

const viewTasks = async (session) => {
  selectedSession.value = session
  tasksLoading.value = true
  tasksDialogVisible.value = true

  try {
    const response = await sessionApi.getSessionTasks(session.id)
    sessionTasks.value = response.tasks || []
  } catch (error) {
    console.error('加载会话任务失败:', error)
    ElMessage.error('加载会话任务失败')
  } finally {
    tasksLoading.value = false
  }
}

const viewLogs = async (session) => {
  selectedSession.value = session
  logLevel.value = ''
  logsDialogVisible.value = true
  await loadSessionLogs()
}

const loadSessionLogs = async () => {
  if (!selectedSession.value) return

  logsLoading.value = true
  try {
    const response = await sessionApi.getSessionLogs(selectedSession.value.id, logLevel.value)
    sessionLogs.value = response.logs || []
  } catch (error) {
    console.error('加载会话日志失败:', error)
    ElMessage.error('加载会话日志失败')
  } finally {
    logsLoading.value = false
  }
}

const stopSession = async (session) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止会话 "${session.id}" 吗？`,
      '确认停止',
      {
        confirmButtonText: '停止',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用停止会话的API
    // await sessionApi.stopSession(session.id)

    ElMessage.success('会话停止请求已发送')
    loadSessions()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('停止会话失败:', error)
      ElMessage.error('停止会话失败')
    }
  }
}

// 工具方法
const getStatusType = (status) => {
  const typeMap = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

const getProgress = (session) => {
  if (session.status === 'completed') return 100
  if (session.status === 'failed') return 100
  if (session.status === 'running') return 50
  return 0
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  loadSessions()
  loadWorkflows()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

pre {
  background-color: var(--el-fill-color-lighter);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.no-data {
  color: var(--el-text-color-secondary);
  text-align: center;
  padding: 20px;
}

.logs-container {
  max-height: 500px;
  overflow-y: auto;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.logs-content {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid var(--el-border-color);
}

.log-item.log-debug {
  border-left-color: var(--el-color-info);
  background-color: var(--el-color-info-light-9);
}

.log-item.log-info {
  border-left-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.log-item.log-warning {
  border-left-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.log-item.log-error {
  border-left-color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.log-level {
  font-weight: bold;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: var(--el-fill-color);
}

.log-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.log-message {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 5px;
}

.log-context {
  font-size: 12px;
  background-color: var(--el-fill-color-darker);
  padding: 8px;
  border-radius: 3px;
  margin-top: 5px;
}

.log-context pre {
  margin: 0;
  background: none;
  padding: 0;
}
</style>
