<template>
  <div class="settings-container">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>配置和管理系统参数</p>
    </div>
    
    <div class="settings-content">
      <el-tabs v-model="activeTab" tab-position="left" class="settings-tabs">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <div class="tab-content">
            <h3>基本配置</h3>
            <el-form :model="basicSettings" label-width="120px" class="settings-form">
              <el-form-item label="系统名称">
                <el-input v-model="basicSettings.systemName" placeholder="请输入系统名称" />
              </el-form-item>
              
              <el-form-item label="系统描述">
                <el-input 
                  v-model="basicSettings.systemDescription" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入系统描述"
                />
              </el-form-item>
              
              <el-form-item label="默认语言">
                <el-select v-model="basicSettings.defaultLanguage" placeholder="请选择默认语言">
                  <el-option label="中文" value="zh-CN" />
                  <el-option label="English" value="en-US" />
                  <el-option label="日本語" value="ja-JP" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="时区">
                <el-select v-model="basicSettings.timezone" placeholder="请选择时区">
                  <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                  <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                  <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                  <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="主题模式">
                <el-radio-group v-model="basicSettings.theme">
                  <el-radio label="light">浅色模式</el-radio>
                  <el-radio label="dark">深色模式</el-radio>
                  <el-radio label="auto">跟随系统</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="自动保存">
                <el-switch v-model="basicSettings.autoSave" />
                <span class="form-tip">启用后将自动保存用户操作</span>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
                <el-button @click="resetBasicSettings">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- AI配置 -->
        <el-tab-pane label="AI配置" name="ai">
          <div class="tab-content">
            <h3>AI模型配置</h3>
            <el-form :model="aiSettings" label-width="120px" class="settings-form">
              <el-form-item label="默认模型">
                <el-select v-model="aiSettings.defaultModel" placeholder="请选择默认AI模型">
                  <el-option label="GPT-4" value="gpt-4" />
                  <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
                  <el-option label="Claude-3" value="claude-3" />
                  <el-option label="通义千问" value="qwen-max" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="最大Token数">
                <el-input-number 
                  v-model="aiSettings.maxTokens" 
                  :min="100" 
                  :max="8000" 
                  :step="100"
                />
              </el-form-item>
              
              <el-form-item label="温度参数">
                <el-slider 
                  v-model="aiSettings.temperature" 
                  :min="0" 
                  :max="2" 
                  :step="0.1"
                  show-input
                />
                <span class="form-tip">控制AI回答的创造性，0为最保守，2为最创造性</span>
              </el-form-item>
              
              <el-form-item label="超时时间">
                <el-input-number 
                  v-model="aiSettings.timeout" 
                  :min="10" 
                  :max="300" 
                  :step="5"
                />
                <span class="form-tip">秒</span>
              </el-form-item>
              
              <el-form-item label="重试次数">
                <el-input-number 
                  v-model="aiSettings.retryCount" 
                  :min="0" 
                  :max="5" 
                  :step="1"
                />
              </el-form-item>
              
              <el-form-item label="启用流式输出">
                <el-switch v-model="aiSettings.streamOutput" />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveAiSettings">保存设置</el-button>
                <el-button @click="testConnection">测试连接</el-button>
              </el-form-item>
            </el-form>
            
            <el-divider />
            
            <h3>API密钥管理</h3>
            <div class="api-keys-section">
              <el-table :data="apiKeys" style="width: 100%">
                <el-table-column prop="provider" label="服务商" width="120" />
                <el-table-column prop="name" label="名称" width="150" />
                <el-table-column prop="key" label="API密钥" min-width="200">
                  <template #default="{ row }">
                    <span v-if="!row.showKey">{{ maskApiKey(row.key) }}</span>
                    <span v-else>{{ row.key }}</span>
                    <el-button 
                      type="text" 
                      size="small" 
                      @click="toggleKeyVisibility(row)"
                    >
                      {{ row.showKey ? '隐藏' : '显示' }}
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                      {{ row.status === 'active' ? '有效' : '无效' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button type="text" size="small" @click="editApiKey(row)">编辑</el-button>
                    <el-button type="text" size="small" @click="deleteApiKey(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="api-keys-actions">
                <el-button type="primary" @click="addApiKey">添加API密钥</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <div class="tab-content">
            <h3>访问控制</h3>
            <el-form :model="securitySettings" label-width="120px" class="settings-form">
              <el-form-item label="启用认证">
                <el-switch v-model="securitySettings.enableAuth" />
              </el-form-item>
              
              <el-form-item label="会话超时" v-if="securitySettings.enableAuth">
                <el-input-number 
                  v-model="securitySettings.sessionTimeout" 
                  :min="5" 
                  :max="1440" 
                  :step="5"
                />
                <span class="form-tip">分钟</span>
              </el-form-item>
              
              <el-form-item label="最大登录尝试" v-if="securitySettings.enableAuth">
                <el-input-number 
                  v-model="securitySettings.maxLoginAttempts" 
                  :min="3" 
                  :max="10" 
                  :step="1"
                />
              </el-form-item>
              
              <el-form-item label="IP白名单">
                <el-switch v-model="securitySettings.enableIpWhitelist" />
              </el-form-item>
              
              <el-form-item label="允许的IP" v-if="securitySettings.enableIpWhitelist">
                <el-input 
                  v-model="securitySettings.allowedIps" 
                  type="textarea" 
                  :rows="3"
                  placeholder="每行一个IP地址或CIDR，例如：***********/24"
                />
              </el-form-item>
              
              <el-form-item label="启用HTTPS">
                <el-switch v-model="securitySettings.enableHttps" />
              </el-form-item>
              
              <el-form-item label="数据加密">
                <el-switch v-model="securitySettings.enableEncryption" />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveSecuritySettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <div class="tab-content">
            <h3>通知配置</h3>
            <el-form :model="notificationSettings" label-width="120px" class="settings-form">
              <el-form-item label="邮件通知">
                <el-switch v-model="notificationSettings.enableEmail" />
              </el-form-item>
              
              <div v-if="notificationSettings.enableEmail" class="email-config">
                <el-form-item label="SMTP服务器">
                  <el-input v-model="notificationSettings.smtpHost" placeholder="smtp.example.com" />
                </el-form-item>
                
                <el-form-item label="SMTP端口">
                  <el-input-number v-model="notificationSettings.smtpPort" :min="1" :max="65535" />
                </el-form-item>
                
                <el-form-item label="发件人邮箱">
                  <el-input v-model="notificationSettings.fromEmail" placeholder="<EMAIL>" />
                </el-form-item>
                
                <el-form-item label="邮箱密码">
                  <el-input 
                    v-model="notificationSettings.emailPassword" 
                    type="password" 
                    placeholder="请输入邮箱密码或应用密码"
                    show-password
                  />
                </el-form-item>
              </div>
              
              <el-form-item label="Webhook通知">
                <el-switch v-model="notificationSettings.enableWebhook" />
              </el-form-item>
              
              <el-form-item label="Webhook URL" v-if="notificationSettings.enableWebhook">
                <el-input 
                  v-model="notificationSettings.webhookUrl" 
                  placeholder="https://hooks.example.com/webhook"
                />
              </el-form-item>
              
              <el-form-item label="通知事件">
                <el-checkbox-group v-model="notificationSettings.events">
                  <el-checkbox label="task_completed">任务完成</el-checkbox>
                  <el-checkbox label="task_failed">任务失败</el-checkbox>
                  <el-checkbox label="system_error">系统错误</el-checkbox>
                  <el-checkbox label="agent_offline">智能体离线</el-checkbox>
                  <el-checkbox label="workflow_finished">工作流完成</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
                <el-button @click="testNotification">发送测试通知</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 系统信息 -->
        <el-tab-pane label="系统信息" name="system">
          <div class="tab-content">
            <h3>系统状态</h3>
            <div class="system-info">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-card class="info-card">
                    <div class="info-item">
                      <span class="label">系统版本:</span>
                      <span class="value">{{ systemInfo.version }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">运行时间:</span>
                      <span class="value">{{ systemInfo.uptime }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">CPU使用率:</span>
                      <span class="value">{{ systemInfo.cpuUsage }}%</span>
                    </div>
                    <div class="info-item">
                      <span class="label">内存使用:</span>
                      <span class="value">{{ systemInfo.memoryUsage }}</span>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="12">
                  <el-card class="info-card">
                    <div class="info-item">
                      <span class="label">数据库状态:</span>
                      <el-tag :type="systemInfo.dbStatus === 'connected' ? 'success' : 'danger'">
                        {{ systemInfo.dbStatus === 'connected' ? '已连接' : '未连接' }}
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <span class="label">Redis状态:</span>
                      <el-tag :type="systemInfo.redisStatus === 'connected' ? 'success' : 'danger'">
                        {{ systemInfo.redisStatus === 'connected' ? '已连接' : '未连接' }}
                      </el-tag>
                    </div>
                    <div class="info-item">
                      <span class="label">活跃智能体:</span>
                      <span class="value">{{ systemInfo.activeAgents }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">运行任务:</span>
                      <span class="value">{{ systemInfo.runningTasks }}</span>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
            
            <el-divider />
            
            <h3>系统操作</h3>
            <div class="system-actions">
              <el-button type="primary" @click="exportConfig">导出配置</el-button>
              <el-button @click="importConfig">导入配置</el-button>
              <el-button type="warning" @click="restartSystem">重启系统</el-button>
              <el-button type="danger" @click="resetSystem">重置系统</el-button>
            </div>
            
            <el-divider />
            
            <h3>日志管理</h3>
            <div class="log-management">
              <el-form :model="logSettings" label-width="120px" class="settings-form">
                <el-form-item label="日志级别">
                  <el-select v-model="logSettings.level">
                    <el-option label="DEBUG" value="debug" />
                    <el-option label="INFO" value="info" />
                    <el-option label="WARN" value="warn" />
                    <el-option label="ERROR" value="error" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="日志保留天数">
                  <el-input-number 
                    v-model="logSettings.retentionDays" 
                    :min="1" 
                    :max="365" 
                    :step="1"
                  />
                </el-form-item>
                
                <el-form-item label="最大日志文件">
                  <el-input-number 
                    v-model="logSettings.maxFiles" 
                    :min="1" 
                    :max="100" 
                    :step="1"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="saveLogSettings">保存设置</el-button>
                  <el-button @click="clearLogs">清空日志</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- API密钥编辑对话框 -->
    <el-dialog v-model="apiKeyDialogVisible" :title="isEditingApiKey ? '编辑API密钥' : '添加API密钥'" width="500px">
      <el-form :model="apiKeyForm" :rules="apiKeyRules" ref="apiKeyFormRef" label-width="100px">
        <el-form-item label="服务商" prop="provider">
          <el-select v-model="apiKeyForm.provider" placeholder="请选择服务商">
            <el-option label="OpenAI" value="openai" />
            <el-option label="Anthropic" value="anthropic" />
            <el-option label="阿里云" value="alibaba" />
            <el-option label="百度" value="baidu" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="名称" prop="name">
          <el-input v-model="apiKeyForm.name" placeholder="请输入名称" />
        </el-form-item>
        
        <el-form-item label="API密钥" prop="key">
          <el-input 
            v-model="apiKeyForm.key" 
            type="password" 
            placeholder="请输入API密钥"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="apiKeyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveApiKey">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const activeTab = ref('basic')
const apiKeyDialogVisible = ref(false)
const isEditingApiKey = ref(false)
const apiKeyFormRef = ref(null)

// 基本设置
const basicSettings = ref({
  systemName: 'A2A智能助手系统',
  systemDescription: '基于AI的智能助手和自动化平台',
  defaultLanguage: 'zh-CN',
  timezone: 'Asia/Shanghai',
  theme: 'light',
  autoSave: true
})

// AI设置
const aiSettings = ref({
  defaultModel: 'gpt-4',
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 60,
  retryCount: 3,
  streamOutput: true
})

// 安全设置
const securitySettings = ref({
  enableAuth: true,
  sessionTimeout: 60,
  maxLoginAttempts: 5,
  enableIpWhitelist: false,
  allowedIps: '',
  enableHttps: true,
  enableEncryption: true
})

// 通知设置
const notificationSettings = ref({
  enableEmail: false,
  smtpHost: '',
  smtpPort: 587,
  fromEmail: '',
  emailPassword: '',
  enableWebhook: false,
  webhookUrl: '',
  events: ['task_completed', 'task_failed', 'system_error']
})

// 系统信息
const systemInfo = ref({
  version: '1.0.0',
  uptime: '3天 12小时 45分钟',
  cpuUsage: 25,
  memoryUsage: '2.1GB / 8GB',
  dbStatus: 'connected',
  redisStatus: 'connected',
  activeAgents: 5,
  runningTasks: 12
})

// 日志设置
const logSettings = ref({
  level: 'info',
  retentionDays: 30,
  maxFiles: 10
})

// API密钥
const apiKeys = ref([
  {
    id: 1,
    provider: 'openai',
    name: 'OpenAI主密钥',
    key: 'sk-1234567890abcdef1234567890abcdef',
    status: 'active',
    showKey: false
  },
  {
    id: 2,
    provider: 'anthropic',
    name: 'Claude密钥',
    key: 'sk-ant-1234567890abcdef1234567890abcdef',
    status: 'active',
    showKey: false
  }
])

const apiKeyForm = ref({
  id: null,
  provider: '',
  name: '',
  key: ''
})

const apiKeyRules = {
  provider: [{ required: true, message: '请选择服务商', trigger: 'change' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  key: [{ required: true, message: '请输入API密钥', trigger: 'blur' }]
}

onMounted(() => {
  // 初始化设置
})

// 基本设置方法
const saveBasicSettings = () => {
  ElMessage.success('基本设置已保存')
}

const resetBasicSettings = () => {
  basicSettings.value = {
    systemName: 'A2A智能助手系统',
    systemDescription: '基于AI的智能助手和自动化平台',
    defaultLanguage: 'zh-CN',
    timezone: 'Asia/Shanghai',
    theme: 'light',
    autoSave: true
  }
  ElMessage.success('基本设置已重置')
}

// AI设置方法
const saveAiSettings = () => {
  ElMessage.success('AI配置已保存')
}

const testConnection = () => {
  ElMessage.success('AI服务连接正常')
}

// API密钥管理
const maskApiKey = (key) => {
  if (!key) return ''
  return key.slice(0, 8) + '*'.repeat(key.length - 16) + key.slice(-8)
}

const toggleKeyVisibility = (row) => {
  row.showKey = !row.showKey
}

const addApiKey = () => {
  apiKeyForm.value = {
    id: null,
    provider: '',
    name: '',
    key: ''
  }
  isEditingApiKey.value = false
  apiKeyDialogVisible.value = true
}

const editApiKey = (row) => {
  apiKeyForm.value = { ...row }
  isEditingApiKey.value = true
  apiKeyDialogVisible.value = true
}

const saveApiKey = async () => {
  try {
    await apiKeyFormRef.value.validate()
    
    if (isEditingApiKey.value) {
      const index = apiKeys.value.findIndex(key => key.id === apiKeyForm.value.id)
      if (index > -1) {
        apiKeys.value[index] = { ...apiKeyForm.value }
      }
      ElMessage.success('API密钥已更新')
    } else {
      const newKey = {
        ...apiKeyForm.value,
        id: apiKeys.value.length + 1,
        status: 'active',
        showKey: false
      }
      apiKeys.value.push(newKey)
      ElMessage.success('API密钥已添加')
    }
    
    apiKeyDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const deleteApiKey = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除API密钥 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = apiKeys.value.findIndex(key => key.id === row.id)
    if (index > -1) {
      apiKeys.value.splice(index, 1)
      ElMessage.success('API密钥已删除')
    }
  } catch {
    // 用户取消删除
  }
}

// 安全设置方法
const saveSecuritySettings = () => {
  ElMessage.success('安全设置已保存')
}

// 通知设置方法
const saveNotificationSettings = () => {
  ElMessage.success('通知设置已保存')
}

const testNotification = () => {
  ElMessage.success('测试通知已发送')
}

// 系统操作方法
const exportConfig = () => {
  const config = {
    basic: basicSettings.value,
    ai: aiSettings.value,
    security: securitySettings.value,
    notification: notificationSettings.value,
    log: logSettings.value
  }
  
  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `a2a_config_${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  
  ElMessage.success('配置已导出')
}

const importConfig = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target.result)
          if (config.basic) basicSettings.value = config.basic
          if (config.ai) aiSettings.value = config.ai
          if (config.security) securitySettings.value = config.security
          if (config.notification) notificationSettings.value = config.notification
          if (config.log) logSettings.value = config.log
          ElMessage.success('配置已导入')
        } catch (error) {
          ElMessage.error('配置文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

const restartSystem = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重启系统吗？这将中断所有正在运行的任务。',
      '确认重启',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('系统重启指令已发送')
  } catch {
    // 用户取消操作
  }
}

const resetSystem = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置系统吗？这将清除所有数据和配置，此操作不可恢复！',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success('系统重置指令已发送')
  } catch {
    // 用户取消操作
  }
}

// 日志管理方法
const saveLogSettings = () => {
  ElMessage.success('日志设置已保存')
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有日志吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('日志已清空')
  } catch {
    // 用户取消操作
  }
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.settings-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.settings-tabs {
  min-height: 600px;
}

.tab-content {
  padding: 20px;
}

.tab-content h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.settings-form {
  max-width: 600px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.email-config {
  margin-left: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.system-info {
  margin-bottom: 20px;
}

.info-card {
  height: 160px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #303133;
  font-weight: 500;
}

.system-actions {
  margin-bottom: 20px;
}

.system-actions .el-button {
  margin-right: 12px;
  margin-bottom: 8px;
}

.log-management {
  max-width: 400px;
}

.api-keys-section {
  margin-top: 20px;
}

.api-keys-actions {
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}

:deep(.el-tabs--left .el-tabs__nav-wrap) {
  background: #f8f9fa;
}

:deep(.el-tabs--left .el-tabs__item) {
  text-align: left;
  padding: 0 20px;
}

:deep(.el-tabs--left .el-tabs__item.is-active) {
  background: white;
  border-right: 2px solid #409eff;
}
</style>