<template>
  <div class="page-container">
    <el-row :gutter="20">
      <!-- 基本设置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>基本设置</span>
            </div>
          </template>

          <el-form :model="basicSettings" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="basicSettings.systemName" placeholder="A2A多智能体协作系统" />
            </el-form-item>
            <el-form-item label="系统版本">
              <el-input v-model="basicSettings.version" readonly />
            </el-form-item>
            <el-form-item label="服务器地址">
              <el-input v-model="basicSettings.serverHost" placeholder="localhost" />
            </el-form-item>
            <el-form-item label="服务器端口">
              <el-input-number v-model="basicSettings.serverPort" :min="1000" :max="65535" />
            </el-form-item>
            <el-form-item label="调试模式">
              <el-switch v-model="basicSettings.debugMode" />
            </el-form-item>
            <el-form-item label="日志级别">
              <el-select v-model="basicSettings.logLevel" style="width: 100%;">
                <el-option label="DEBUG" value="DEBUG" />
                <el-option label="INFO" value="INFO" />
                <el-option label="WARNING" value="WARNING" />
                <el-option label="ERROR" value="ERROR" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 千问API设置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Key /></el-icon>
              <span>千问API设置</span>
            </div>
          </template>

          <el-form :model="qwenSettings" label-width="120px">
            <el-form-item label="API密钥">
              <el-input
                v-model="qwenSettings.apiKey"
                type="password"
                placeholder="请输入千问API密钥"
                show-password
              />
            </el-form-item>
            <el-form-item label="API地址">
              <el-input v-model="qwenSettings.baseUrl" placeholder="https://dashscope.aliyuncs.com/compatible-mode/v1" />
            </el-form-item>
            <el-form-item label="模型名称">
              <el-select v-model="qwenSettings.model" style="width: 100%;">
                <el-option label="qwen-plus" value="qwen-plus" />
                <el-option label="qwen-turbo" value="qwen-turbo" />
                <el-option label="qwen-max" value="qwen-max" />
              </el-select>
            </el-form-item>
            <el-form-item label="温度参数">
              <el-slider v-model="qwenSettings.temperature" :min="0" :max="2" :step="0.1" show-input />
            </el-form-item>
            <el-form-item label="最大Token">
              <el-input-number v-model="qwenSettings.maxTokens" :min="100" :max="8000" />
            </el-form-item>
            <el-form-item>
              <el-button @click="testQwenConnection" :loading="testingConnection">
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 工作流设置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Operation /></el-icon>
              <span>工作流设置</span>
            </div>
          </template>

          <el-form :model="workflowSettings" label-width="140px">
            <el-form-item label="最大执行时间">
              <el-input-number
                v-model="workflowSettings.maxExecutionTime"
                :min="60"
                :max="7200"
                style="width: 100%;"
              />
              <span style="margin-left: 8px; color: var(--el-text-color-secondary);">秒</span>
            </el-form-item>
            <el-form-item label="最大并行任务">
              <el-input-number
                v-model="workflowSettings.maxParallelTasks"
                :min="1"
                :max="50"
                style="width: 100%;"
              />
            </el-form-item>
            <el-form-item label="默认重试次数">
              <el-input-number
                v-model="workflowSettings.defaultRetryCount"
                :min="0"
                :max="10"
                style="width: 100%;"
              />
            </el-form-item>
            <el-form-item label="重试延迟">
              <el-input-number
                v-model="workflowSettings.retryDelay"
                :min="1"
                :max="60"
                style="width: 100%;"
              />
              <span style="margin-left: 8px; color: var(--el-text-color-secondary);">秒</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 功能开关 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Switch /></el-icon>
              <span>功能开关</span>
            </div>
          </template>

          <el-form :model="featureSettings" label-width="140px">
            <el-form-item label="启用Web界面">
              <el-switch v-model="featureSettings.enableWebUI" />
            </el-form-item>
            <el-form-item label="启用API文档">
              <el-switch v-model="featureSettings.enableApiDocs" />
            </el-form-item>
            <el-form-item label="启用指标监控">
              <el-switch v-model="featureSettings.enableMetrics" />
            </el-form-item>
            <el-form-item label="启用调试日志">
              <el-switch v-model="featureSettings.enableDebugLogs" />
            </el-form-item>
            <el-form-item label="自动保存会话">
              <el-switch v-model="featureSettings.autoSaveSessions" />
            </el-form-item>
            <el-form-item label="启用CORS">
              <el-switch v-model="featureSettings.enableCors" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="resetSettings">
        <el-icon><RefreshLeft /></el-icon>
        重置设置
      </el-button>
      <el-button @click="exportSettings">
        <el-icon><Download /></el-icon>
        导出配置
      </el-button>
      <el-button @click="importSettings">
        <el-icon><Upload /></el-icon>
        导入配置
      </el-button>
      <el-button type="primary" @click="saveSettings" :loading="saving">
        <el-icon><Check /></el-icon>
        保存设置
      </el-button>
    </div>

    <!-- 导入配置对话框 -->
    <el-dialog v-model="importDialogVisible" title="导入配置" width="500px">
      <el-upload
        class="upload-demo"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".json"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将配置文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传JSON格式的配置文件
          </div>
        </template>
      </el-upload>

      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmImport" :disabled="!importFile">
          确认导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { systemApi } from '@/api'

// 响应式数据
const saving = ref(false)
const testingConnection = ref(false)
const importDialogVisible = ref(false)
const importFile = ref(null)

// 基本设置
const basicSettings = ref({
  systemName: 'A2A多智能体协作系统',
  version: '1.0.0',
  serverHost: 'localhost',
  serverPort: 8000,
  debugMode: false,
  logLevel: 'INFO'
})

// 千问API设置
const qwenSettings = ref({
  apiKey: '',
  baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  model: 'qwen-plus',
  temperature: 0.7,
  maxTokens: 2000
})

// 工作流设置
const workflowSettings = ref({
  maxExecutionTime: 3600,
  maxParallelTasks: 10,
  defaultRetryCount: 3,
  retryDelay: 1
})

// 功能开关
const featureSettings = ref({
  enableWebUI: true,
  enableApiDocs: true,
  enableMetrics: true,
  enableDebugLogs: false,
  autoSaveSessions: true,
  enableCors: true
})

// 方法
const loadSettings = async () => {
  try {
    // 这里应该从后端加载设置
    // const response = await systemApi.getSettings()
    // 暂时使用默认值
    ElMessage.success('设置加载成功')
  } catch (error) {
    console.error('加载设置失败:', error)
    ElMessage.error('加载设置失败')
  }
}

const saveSettings = async () => {
  try {
    saving.value = true

    const allSettings = {
      basic: basicSettings.value,
      qwen: qwenSettings.value,
      workflow: workflowSettings.value,
      features: featureSettings.value
    }

    // 这里应该保存到后端
    // await systemApi.saveSettings(allSettings)

    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

const testQwenConnection = async () => {
  if (!qwenSettings.value.apiKey) {
    ElMessage.warning('请先输入API密钥')
    return
  }

  try {
    testingConnection.value = true

    // 这里应该测试千问API连接
    // await systemApi.testQwenConnection(qwenSettings.value)

    // 模拟测试延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('千问API连接测试成功')
  } catch (error) {
    console.error('千问API连接测试失败:', error)
    ElMessage.error('千问API连接测试失败')
  } finally {
    testingConnection.value = false
  }
}

const resetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有设置为默认值吗？此操作不可恢复。',
      '确认重置',
      {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 重置为默认值
    basicSettings.value = {
      systemName: 'A2A多智能体协作系统',
      version: '1.0.0',
      serverHost: 'localhost',
      serverPort: 8000,
      debugMode: false,
      logLevel: 'INFO'
    }

    qwenSettings.value = {
      apiKey: '',
      baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
      model: 'qwen-plus',
      temperature: 0.7,
      maxTokens: 2000
    }

    workflowSettings.value = {
      maxExecutionTime: 3600,
      maxParallelTasks: 10,
      defaultRetryCount: 3,
      retryDelay: 1
    }

    featureSettings.value = {
      enableWebUI: true,
      enableApiDocs: true,
      enableMetrics: true,
      enableDebugLogs: false,
      autoSaveSessions: true,
      enableCors: true
    }

    ElMessage.success('设置已重置为默认值')
  } catch (error) {
    // 用户取消操作
  }
}

const exportSettings = () => {
  const allSettings = {
    basic: basicSettings.value,
    qwen: qwenSettings.value,
    workflow: workflowSettings.value,
    features: featureSettings.value,
    exportTime: new Date().toISOString()
  }

  const dataStr = JSON.stringify(allSettings, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `a2a-settings-${new Date().toISOString().split('T')[0]}.json`
  link.click()

  ElMessage.success('配置已导出')
}

const importSettings = () => {
  importDialogVisible.value = true
}

const handleFileChange = (file) => {
  importFile.value = file.raw
}

const confirmImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请选择配置文件')
    return
  }

  try {
    const text = await importFile.value.text()
    const settings = JSON.parse(text)

    // 验证配置格式
    if (!settings.basic || !settings.qwen || !settings.workflow || !settings.features) {
      throw new Error('配置文件格式不正确')
    }

    // 应用配置
    basicSettings.value = { ...basicSettings.value, ...settings.basic }
    qwenSettings.value = { ...qwenSettings.value, ...settings.qwen }
    workflowSettings.value = { ...workflowSettings.value, ...settings.workflow }
    featureSettings.value = { ...featureSettings.value, ...settings.features }

    importDialogVisible.value = false
    importFile.value = null

    ElMessage.success('配置导入成功')
  } catch (error) {
    console.error('导入配置失败:', error)
    ElMessage.error('导入配置失败：' + error.message)
  }
}

// 生命周期
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.action-buttons {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.action-buttons .el-button {
  margin: 0 10px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.upload-demo {
  text-align: center;
}

.el-slider {
  margin: 0 12px;
}

.el-input-number {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }

  .el-col {
    margin-bottom: 20px;
  }

  .action-buttons .el-button {
    margin: 5px;
    width: calc(50% - 10px);
  }
}
</style>
