<template>
  <div class="task-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="$router.go(-1)"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="task-info">
          <h1>{{ taskData.name }}</h1>
          <div class="task-meta">
            <el-tag :type="getStatusType(taskData.status)">{{ taskData.status }}</el-tag>
            <el-tag type="info">{{ taskData.priority }}</el-tag>
            <span class="meta-item">创建时间：{{ taskData.createdAt }}</span>
            <span class="meta-item">执行时间：{{ taskData.executionTime }}</span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button 
          v-if="taskData.status === '等待中' || taskData.status === '暂停'"
          type="primary" 
          @click="startTask"
        >
          <el-icon><VideoPlay /></el-icon>
          {{ taskData.status === '暂停' ? '继续' : '开始' }}
        </el-button>
        <el-button 
          v-if="taskData.status === '执行中'"
          type="warning" 
          @click="pauseTask"
        >
          <el-icon><VideoPause /></el-icon>
          暂停
        </el-button>
        <el-button 
          v-if="taskData.status !== '已完成' && taskData.status !== '已取消'"
          type="danger" 
          @click="cancelTask"
        >
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button @click="exportTask">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-dropdown @command="handleCommand">
          <el-button>
            更多操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="clone">克隆任务</el-dropdown-item>
              <el-dropdown-item command="edit" :disabled="taskData.status === '执行中'">编辑任务</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 任务内容区域 -->
    <div class="task-content">
      <div class="content-main">
        <!-- 任务概览 -->
        <el-card class="overview-card">
          <template #header>
            <span>任务概览</span>
          </template>
          <div class="overview-grid">
            <div class="overview-item">
              <div class="item-label">任务描述</div>
              <div class="item-value">{{ taskData.description }}</div>
            </div>
            <div class="overview-item">
              <div class="item-label">执行进度</div>
              <div class="item-value">
                <el-progress 
                  :percentage="taskData.progress" 
                  :status="getProgressStatus(taskData.status)"
                  :stroke-width="8"
                />
                <span class="progress-text">{{ taskData.progress }}%</span>
              </div>
            </div>
            <div class="overview-item">
              <div class="item-label">关联工作流</div>
              <div class="item-value">
                <el-link type="primary" @click="viewWorkflow(taskData.workflowId)">
                  {{ taskData.workflowName }}
                </el-link>
              </div>
            </div>
            <div class="overview-item">
              <div class="item-label">执行智能体</div>
              <div class="item-value">
                <el-link type="primary" @click="viewAgent(taskData.agentId)">
                  {{ taskData.agentName }}
                </el-link>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 执行步骤 -->
        <el-card class="steps-card">
          <template #header>
            <div class="card-header">
              <span>执行步骤</span>
              <el-button type="text" @click="refreshSteps">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="step in executionSteps"
              :key="step.id"
              :type="getStepType(step.status)"
              :icon="getStepIcon(step.status)"
              :timestamp="step.timestamp"
            >
              <div class="step-content">
                <div class="step-header">
                  <span class="step-name">{{ step.name }}</span>
                  <el-tag :type="getStatusType(step.status)" size="small">
                    {{ step.status }}
                  </el-tag>
                </div>
                <div class="step-description">{{ step.description }}</div>
                <div v-if="step.output" class="step-output">
                  <el-collapse>
                    <el-collapse-item title="查看输出" name="output">
                      <pre>{{ step.output }}</pre>
                    </el-collapse-item>
                  </el-collapse>
                </div>
                <div v-if="step.error" class="step-error">
                  <el-alert
                    :title="step.error"
                    type="error"
                    :closable="false"
                    show-icon
                  />
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 实时日志 -->
        <el-card class="logs-card">
          <template #header>
            <div class="card-header">
              <span>实时日志</span>
              <div class="log-actions">
                <el-switch
                  v-model="autoScroll"
                  active-text="自动滚动"
                  size="small"
                />
                <el-button type="text" @click="clearLogs">
                  <el-icon><Delete /></el-icon>
                  清空
                </el-button>
                <el-button type="text" @click="downloadLogs">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </div>
            </div>
          </template>
          <div class="log-container" ref="logContainer">
            <div 
              v-for="log in taskLogs" 
              :key="log.id"
              :class="['log-item', log.level]"
            >
              <span class="log-time">{{ log.timestamp }}</span>
              <span class="log-level">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 侧边栏 -->
      <div class="content-sidebar">
        <!-- 任务信息 -->
        <el-card class="info-card">
          <template #header>
            <span>任务信息</span>
          </template>
          <div class="info-item">
            <label>任务ID：</label>
            <span>{{ taskData.id }}</span>
          </div>
          <div class="info-item">
            <label>任务类型：</label>
            <span>{{ taskData.type }}</span>
          </div>
          <div class="info-item">
            <label>优先级：</label>
            <span>{{ taskData.priority }}</span>
          </div>
          <div class="info-item">
            <label>创建者：</label>
            <span>{{ taskData.creator }}</span>
          </div>
          <div class="info-item">
            <label>预计耗时：</label>
            <span>{{ taskData.estimatedTime }}</span>
          </div>
          <div class="info-item">
            <label>实际耗时：</label>
            <span>{{ taskData.actualTime }}</span>
          </div>
        </el-card>

        <!-- 性能统计 -->
        <el-card class="stats-card">
          <template #header>
            <span>性能统计</span>
          </template>
          <div class="stat-item">
            <div class="stat-value">{{ taskStats.cpuUsage }}%</div>
            <div class="stat-label">CPU使用率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ taskStats.memoryUsage }}MB</div>
            <div class="stat-label">内存使用</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ taskStats.networkIO }}KB/s</div>
            <div class="stat-label">网络IO</div>
          </div>
        </el-card>

        <!-- 依赖任务 -->
        <el-card class="dependencies-card">
          <template #header>
            <span>依赖任务</span>
          </template>
          <div class="dependency-list">
            <div 
              v-for="dep in dependencies" 
              :key="dep.id"
              class="dependency-item"
              @click="viewTask(dep.id)"
            >
              <div class="dep-name">{{ dep.name }}</div>
              <el-tag :type="getStatusType(dep.status)" size="small">
                {{ dep.status }}
              </el-tag>
            </div>
          </div>
        </el-card>

        <!-- 相关文件 -->
        <el-card class="files-card">
          <template #header>
            <span>相关文件</span>
          </template>
          <div class="file-list">
            <div 
              v-for="file in relatedFiles" 
              :key="file.id"
              class="file-item"
              @click="viewFile(file)"
            >
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ file.size }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  VideoPlay,
  VideoPause,
  Close,
  Download,
  ArrowDown,
  Refresh,
  Delete,
  Document,
  SuccessFilled,
  Loading,
  WarningFilled,
  CircleCloseFilled
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const logContainer = ref(null)
const autoScroll = ref(true)
let logUpdateTimer = null

// 任务数据
const taskData = reactive({
  id: route.params.id,
  name: '数据分析任务',
  description: '对用户行为数据进行深度分析，生成用户画像和行为预测模型',
  status: '执行中',
  priority: '高',
  type: '数据分析',
  progress: 65,
  createdAt: '2024-01-15 09:30:00',
  executionTime: '1小时25分钟',
  workflowId: 'wf_001',
  workflowName: '用户数据分析流程',
  agentId: 'agent_002',
  agentName: 'DataAnalyst Agent',
  creator: '张三',
  estimatedTime: '2小时',
  actualTime: '1小时25分钟'
})

// 执行步骤
const executionSteps = ref([
  {
    id: 1,
    name: '数据预处理',
    description: '清洗和标准化原始数据',
    status: '已完成',
    timestamp: '09:30:15',
    output: '处理了10,000条用户记录，清除了500条无效数据'
  },
  {
    id: 2,
    name: '特征工程',
    description: '提取和构建用户行为特征',
    status: '已完成',
    timestamp: '09:45:30',
    output: '生成了25个用户行为特征维度'
  },
  {
    id: 3,
    name: '模型训练',
    description: '训练用户行为预测模型',
    status: '执行中',
    timestamp: '10:15:45'
  },
  {
    id: 4,
    name: '模型评估',
    description: '评估模型性能和准确性',
    status: '等待中',
    timestamp: ''
  },
  {
    id: 5,
    name: '结果输出',
    description: '生成分析报告和可视化图表',
    status: '等待中',
    timestamp: ''
  }
])

// 任务日志
const taskLogs = ref([
  {
    id: 1,
    timestamp: '09:30:15',
    level: 'info',
    message: '任务开始执行'
  },
  {
    id: 2,
    timestamp: '09:30:18',
    level: 'info',
    message: '开始数据预处理步骤'
  },
  {
    id: 3,
    timestamp: '09:45:30',
    level: 'info',
    message: '数据预处理完成，开始特征工程'
  },
  {
    id: 4,
    timestamp: '10:15:45',
    level: 'info',
    message: '特征工程完成，开始模型训练'
  },
  {
    id: 5,
    timestamp: '10:55:12',
    level: 'info',
    message: '模型训练进度：65%'
  }
])

// 性能统计
const taskStats = reactive({
  cpuUsage: 78,
  memoryUsage: 1024,
  networkIO: 256
})

// 依赖任务
const dependencies = ref([
  {
    id: 'task_001',
    name: '数据采集任务',
    status: '已完成'
  },
  {
    id: 'task_002',
    name: '数据验证任务',
    status: '已完成'
  }
])

// 相关文件
const relatedFiles = ref([
  {
    id: 1,
    name: 'user_data.csv',
    size: '15.2MB',
    type: 'csv'
  },
  {
    id: 2,
    name: 'analysis_config.json',
    size: '2.1KB',
    type: 'json'
  },
  {
    id: 3,
    name: 'model_output.pkl',
    size: '8.5MB',
    type: 'pkl'
  }
])

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '执行中': 'primary',
    '已完成': 'success',
    '等待中': 'info',
    '暂停': 'warning',
    '失败': 'danger',
    '已取消': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取进度状态
const getProgressStatus = (status) => {
  const statusMap = {
    '执行中': '',
    '已完成': 'success',
    '失败': 'exception',
    '已取消': 'exception'
  }
  return statusMap[status] || ''
}

// 获取步骤类型
const getStepType = (status) => {
  const typeMap = {
    '已完成': 'success',
    '执行中': 'primary',
    '等待中': 'info',
    '失败': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取步骤图标
const getStepIcon = (status) => {
  const iconMap = {
    '已完成': SuccessFilled,
    '执行中': Loading,
    '等待中': '',
    '失败': CircleCloseFilled
  }
  return iconMap[status] || ''
}

// 开始任务
const startTask = () => {
  taskData.status = '执行中'
  ElMessage.success('任务已开始执行')
}

// 暂停任务
const pauseTask = () => {
  taskData.status = '暂停'
  ElMessage.success('任务已暂停')
}

// 取消任务
const cancelTask = () => {
  ElMessageBox.confirm('确定要取消这个任务吗？', '确认取消', {
    type: 'warning'
  }).then(() => {
    taskData.status = '已取消'
    ElMessage.success('任务已取消')
  })
}

// 导出任务
const exportTask = () => {
  ElMessage.success('任务导出功能开发中')
}

// 处理更多操作
const handleCommand = (command) => {
  switch (command) {
    case 'clone':
      ElMessage.success('任务克隆功能开发中')
      break
    case 'edit':
      ElMessage.success('任务编辑功能开发中')
      break
    case 'delete':
      ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
        router.push('/tasks')
      })
      break
  }
}

// 刷新步骤
const refreshSteps = () => {
  ElMessage.success('步骤信息已刷新')
}

// 清空日志
const clearLogs = () => {
  taskLogs.value = []
  ElMessage.success('日志已清空')
}

// 下载日志
const downloadLogs = () => {
  ElMessage.success('日志下载功能开发中')
}

// 查看工作流
const viewWorkflow = (workflowId) => {
  router.push(`/workflows/${workflowId}`)
}

// 查看智能体
const viewAgent = (agentId) => {
  router.push(`/agents/${agentId}`)
}

// 查看任务
const viewTask = (taskId) => {
  router.push(`/tasks/${taskId}`)
}

// 查看文件
const viewFile = (file) => {
  ElMessage.info(`查看文件: ${file.name}`)
}

// 模拟日志更新
const simulateLogUpdate = () => {
  const levels = ['info', 'warn', 'error', 'debug']
  const messages = [
    '处理数据批次 #1234',
    '模型训练进度更新',
    '内存使用率检查',
    '网络连接状态正常',
    '缓存清理完成'
  ]
  
  const newLog = {
    id: Date.now(),
    timestamp: new Date().toLocaleTimeString(),
    level: levels[Math.floor(Math.random() * levels.length)],
    message: messages[Math.floor(Math.random() * messages.length)]
  }
  
  taskLogs.value.push(newLog)
  
  // 自动滚动到底部
  if (autoScroll.value) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight
      }
    })
  }
  
  // 限制日志数量
  if (taskLogs.value.length > 100) {
    taskLogs.value.splice(0, taskLogs.value.length - 100)
  }
}

onMounted(() => {
  // 模拟实时日志更新
  logUpdateTimer = setInterval(simulateLogUpdate, 3000)
})

onUnmounted(() => {
  if (logUpdateTimer) {
    clearInterval(logUpdateTimer)
  }
})
</script>

<style scoped>
.task-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.task-info h1 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.meta-item {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.task-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overview-card,
.steps-card,
.logs-card {
  background: white;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.item-value {
  font-size: 16px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
}

.step-content {
  padding-left: 16px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.step-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.step-output {
  margin-top: 12px;
}

.step-output pre {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
}

.step-error {
  margin-top: 12px;
}

.log-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  background: #1e1e1e;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
  color: #e5e5e5;
}

.log-item.error {
  color: #ff6b6b;
}

.log-item.warn {
  color: #ffd93d;
}

.log-item.info {
  color: #74c0fc;
}

.log-item.debug {
  color: #b197fc;
}

.log-time {
  color: #868e96;
  flex-shrink: 0;
}

.log-level {
  color: inherit;
  font-weight: 600;
  flex-shrink: 0;
  width: 50px;
}

.log-message {
  color: inherit;
  flex: 1;
}

.content-sidebar {
  width: 300px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card,
.stats-card,
.dependencies-card,
.files-card {
  background: white;
  border-radius: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  color: #909399;
  font-weight: 500;
}

.stats-card .el-card__body {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.dependency-list,
.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.dependency-item,
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  cursor: pointer;
  border-bottom: 1px solid #f5f7fa;
  transition: background-color 0.2s;
}

.dependency-item:hover,
.file-item:hover {
  background: #f5f7fa;
}

.dependency-item:last-child,
.file-item:last-child {
  border-bottom: none;
}

.dep-name,
.file-name {
  font-size: 14px;
  color: #303133;
  flex: 1;
}

.file-item {
  gap: 8px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}
</style>