<template>
  <div class="tasks-container">
    <div class="page-header">
      <h1>任务管理</h1>
      <p>管理和监控您的AI任务执行</p>
    </div>
    
    <div class="toolbar">
      <el-button type="primary" @click="createTask">
        <el-icon><Plus /></el-icon>
        创建任务
      </el-button>
      <el-button @click="refreshTasks">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
      <el-input 
        v-model="searchText" 
        placeholder="搜索任务..."
        style="width: 200px;"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;">
        <el-option label="全部" value="" />
        <el-option label="运行中" value="running" />
        <el-option label="已完成" value="completed" />
        <el-option label="失败" value="failed" />
        <el-option label="等待中" value="pending" />
        <el-option label="已取消" value="cancelled" />
      </el-select>
      <el-select v-model="priorityFilter" placeholder="优先级筛选" style="width: 120px;">
        <el-option label="全部" value="" />
        <el-option label="高" value="high" />
        <el-option label="中" value="medium" />
        <el-option label="低" value="low" />
      </el-select>
    </div>

    <div class="tasks-table">
      <el-table 
        :data="filteredTasks" 
        style="width: 100%"
        @row-click="viewTaskDetail"
        row-class-name="task-row"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="name" label="任务名称" min-width="200">
          <template #default="{ row }">
            <div class="task-name">
              <span class="name">{{ row.name }}</span>
              <el-tag v-if="row.isRecurring" size="small" type="info">循环</el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <div class="progress-container">
              <el-progress 
                :percentage="row.progress" 
                :status="row.status === 'failed' ? 'exception' : (row.status === 'completed' ? 'success' : '')"
                :stroke-width="6"
                text-inside
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="agentName" label="执行智能体" width="120" />
        
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ getDuration(row) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                v-if="row.status === 'running'"
                type="warning" 
                size="small" 
                @click.stop="pauseTask(row)"
              >
                暂停
              </el-button>
              <el-button 
                v-if="row.status === 'paused'"
                type="success" 
                size="small" 
                @click.stop="resumeTask(row)"
              >
                继续
              </el-button>
              <el-button 
                v-if="['running', 'pending', 'paused'].includes(row.status)"
                type="danger" 
                size="small" 
                @click.stop="cancelTask(row)"
              >
                取消
              </el-button>
              <el-button 
                v-if="row.status === 'failed'"
                type="primary" 
                size="small" 
                @click.stop="retryTask(row)"
              >
                重试
              </el-button>
              <el-dropdown @command="handleCommand" @click.stop>
                <el-button type="text" size="small">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'view', task: row}">
                      查看详情
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'logs', task: row}">
                      查看日志
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'clone', task: row}">
                      克隆任务
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="{action: 'delete', task: row}"
                      :disabled="row.status === 'running'"
                      divided
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalTasks"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog 
      v-model="taskDetailVisible" 
      :title="selectedTask?.name"
      width="800px"
      class="task-detail-dialog"
    >
      <div v-if="selectedTask" class="task-detail">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="info">
            <div class="task-info">
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">任务ID:</span>
                  <span class="value">{{ selectedTask.id }}</span>
                </div>
                <div class="info-item">
                  <span class="label">任务名称:</span>
                  <span class="value">{{ selectedTask.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">类型:</span>
                  <el-tag :type="getTypeColor(selectedTask.type)">
                    {{ getTypeText(selectedTask.type) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">状态:</span>
                  <el-tag :type="getStatusColor(selectedTask.status)">
                    {{ getStatusText(selectedTask.status) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">优先级:</span>
                  <el-tag :type="getPriorityColor(selectedTask.priority)">
                    {{ getPriorityText(selectedTask.priority) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">执行智能体:</span>
                  <span class="value">{{ selectedTask.agentName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDateTime(selectedTask.createdAt) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">开始时间:</span>
                  <span class="value">{{ selectedTask.startedAt ? formatDateTime(selectedTask.startedAt) : '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">完成时间:</span>
                  <span class="value">{{ selectedTask.completedAt ? formatDateTime(selectedTask.completedAt) : '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">耗时:</span>
                  <span class="value">{{ getDuration(selectedTask) }}</span>
                </div>
              </div>
              
              <div class="description-section">
                <h4>任务描述</h4>
                <p>{{ selectedTask.description || '暂无描述' }}</p>
              </div>
              
              <div class="progress-section">
                <h4>执行进度</h4>
                <el-progress 
                  :percentage="selectedTask.progress" 
                  :status="selectedTask.status === 'failed' ? 'exception' : (selectedTask.status === 'completed' ? 'success' : '')"
                />
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="配置参数" name="config">
            <div class="task-config">
              <el-descriptions :column="1" border>
                <el-descriptions-item 
                  v-for="(value, key) in selectedTask.config" 
                  :key="key"
                  :label="key"
                >
                  {{ typeof value === 'object' ? JSON.stringify(value, null, 2) : value }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="执行日志" name="logs">
            <div class="task-logs">
              <div class="logs-container">
                <div 
                  v-for="log in selectedTask.logs" 
                  :key="log.id"
                  class="log-entry"
                  :class="log.level"
                >
                  <span class="timestamp">{{ formatDateTime(log.timestamp) }}</span>
                  <span class="level">{{ log.level.toUpperCase() }}</span>
                  <span class="message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="结果输出" name="result">
            <div class="task-result">
              <div v-if="selectedTask.result">
                <pre>{{ JSON.stringify(selectedTask.result, null, 2) }}</pre>
              </div>
              <div v-else class="no-result">
                <el-empty description="暂无结果输出" />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskDetailVisible = false">关闭</el-button>
          <el-button 
            v-if="selectedTask?.status === 'failed'"
            type="primary" 
            @click="retryTask(selectedTask)"
          >
            重试任务
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建任务对话框 -->
    <el-dialog v-model="createTaskVisible" title="创建新任务" width="600px">
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="100px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="任务类型" prop="type">
          <el-select v-model="taskForm.type" placeholder="请选择任务类型">
            <el-option label="数据处理" value="data_processing" />
            <el-option label="文本分析" value="text_analysis" />
            <el-option label="代码生成" value="code_generation" />
            <el-option label="图像处理" value="image_processing" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="taskForm.priority" placeholder="请选择优先级">
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="执行智能体" prop="agentId">
          <el-select v-model="taskForm.agentId" placeholder="请选择智能体">
            <el-option label="通用助手" value="1" />
            <el-option label="代码助手" value="2" />
            <el-option label="数据分析师" value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="taskForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="循环任务">
          <el-switch v-model="taskForm.isRecurring" />
        </el-form-item>
        
        <el-form-item v-if="taskForm.isRecurring" label="执行间隔">
          <el-input v-model="taskForm.interval" placeholder="如: 0 0 * * *" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createTaskVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTask">创建</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, MoreFilled } from '@element-plus/icons-vue'

const searchText = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)
const taskDetailVisible = ref(false)
const createTaskVisible = ref(false)
const selectedTask = ref(null)
const activeTab = ref('info')
const tasks = ref([])
const taskFormRef = ref(null)

const taskForm = ref({
  name: '',
  type: '',
  priority: 'medium',
  agentId: '',
  description: '',
  isRecurring: false,
  interval: ''
})

const taskRules = {
  name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  agentId: [{ required: true, message: '请选择执行智能体', trigger: 'change' }]
}

// 模拟数据
const mockTasks = [
  {
    id: 1,
    name: '数据清洗任务',
    type: 'data_processing',
    status: 'running',
    priority: 'high',
    progress: 65,
    agentName: '数据分析师',
    createdAt: new Date('2024-01-15T10:00:00'),
    startedAt: new Date('2024-01-15T10:05:00'),
    completedAt: null,
    isRecurring: false,
    description: '清洗销售数据中的异常值和重复记录',
    config: {
      inputFile: 'sales_data.csv',
      outputFile: 'cleaned_sales_data.csv',
      removeNulls: true,
      removeDuplicates: true
    },
    logs: [
      {
        id: 1,
        timestamp: new Date('2024-01-15T10:05:00'),
        level: 'info',
        message: '任务开始执行'
      },
      {
        id: 2,
        timestamp: new Date('2024-01-15T10:10:00'),
        level: 'info',
        message: '正在读取输入文件...'
      },
      {
        id: 3,
        timestamp: new Date('2024-01-15T10:15:00'),
        level: 'warning',
        message: '发现100条重复记录'
      }
    ],
    result: null
  },
  {
    id: 2,
    name: '代码审查报告',
    type: 'code_generation',
    status: 'completed',
    priority: 'medium',
    progress: 100,
    agentName: '代码助手',
    createdAt: new Date('2024-01-14T14:00:00'),
    startedAt: new Date('2024-01-14T14:02:00'),
    completedAt: new Date('2024-01-14T14:30:00'),
    isRecurring: true,
    description: '自动生成代码审查报告',
    config: {
      repository: 'https://github.com/example/repo',
      branch: 'main',
      includeTests: true
    },
    logs: [
      {
        id: 1,
        timestamp: new Date('2024-01-14T14:02:00'),
        level: 'info',
        message: '开始分析代码库'
      },
      {
        id: 2,
        timestamp: new Date('2024-01-14T14:30:00'),
        level: 'info',
        message: '报告生成完成'
      }
    ],
    result: {
      totalFiles: 45,
      issuesFound: 12,
      codeQuality: 'Good',
      reportUrl: '/reports/code-review-20240114.html'
    }
  },
  {
    id: 3,
    name: '文档翻译',
    type: 'text_analysis',
    status: 'failed',
    priority: 'low',
    progress: 25,
    agentName: '通用助手',
    createdAt: new Date('2024-01-13T09:00:00'),
    startedAt: new Date('2024-01-13T09:05:00'),
    completedAt: null,
    isRecurring: false,
    description: '将技术文档从英文翻译为中文',
    config: {
      sourceLanguage: 'en',
      targetLanguage: 'zh',
      inputFile: 'technical_docs.md'
    },
    logs: [
      {
        id: 1,
        timestamp: new Date('2024-01-13T09:05:00'),
        level: 'info',
        message: '开始翻译任务'
      },
      {
        id: 2,
        timestamp: new Date('2024-01-13T09:15:00'),
        level: 'error',
        message: 'API调用失败: 超出配额限制'
      }
    ],
    result: null
  }
]

// 计算属性
const filteredTasks = computed(() => {
  let filtered = tasks.value
  
  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }
  
  // 优先级筛选
  if (priorityFilter.value) {
    filtered = filtered.filter(task => task.priority === priorityFilter.value)
  }
  
  // 搜索筛选
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    filtered = filtered.filter(task => 
      task.name.toLowerCase().includes(searchLower) ||
      task.agentName.toLowerCase().includes(searchLower) ||
      task.description?.toLowerCase().includes(searchLower)
    )
  }
  
  return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
})

onMounted(() => {
  loadTasks()
})

const loadTasks = () => {
  tasks.value = mockTasks
  totalTasks.value = mockTasks.length
}

const refreshTasks = () => {
  loadTasks()
  ElMessage.success('任务列表已刷新')
}

const createTask = () => {
  taskForm.value = {
    name: '',
    type: '',
    priority: 'medium',
    agentId: '',
    description: '',
    isRecurring: false,
    interval: ''
  }
  createTaskVisible.value = true
}

const submitTask = async () => {
  try {
    await taskFormRef.value.validate()
    
    const newTask = {
      id: tasks.value.length + 1,
      name: taskForm.value.name,
      type: taskForm.value.type,
      status: 'pending',
      priority: taskForm.value.priority,
      progress: 0,
      agentName: getAgentName(taskForm.value.agentId),
      createdAt: new Date(),
      startedAt: null,
      completedAt: null,
      isRecurring: taskForm.value.isRecurring,
      description: taskForm.value.description,
      config: {},
      logs: [],
      result: null
    }
    
    tasks.value.unshift(newTask)
    totalTasks.value++
    
    ElMessage.success('任务创建成功')
    createTaskVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const getAgentName = (agentId) => {
  const agentMap = {
    '1': '通用助手',
    '2': '代码助手',
    '3': '数据分析师'
  }
  return agentMap[agentId] || '未知智能体'
}

const viewTaskDetail = (task) => {
  selectedTask.value = task
  activeTab.value = 'info'
  taskDetailVisible.value = true
}

const handleCommand = (command) => {
  const { action, task } = command
  
  switch (action) {
    case 'view':
      viewTaskDetail(task)
      break
    case 'logs':
      selectedTask.value = task
      activeTab.value = 'logs'
      taskDetailVisible.value = true
      break
    case 'clone':
      cloneTask(task)
      break
    case 'delete':
      deleteTask(task)
      break
  }
}

const pauseTask = (task) => {
  task.status = 'paused'
  ElMessage.success(`任务 "${task.name}" 已暂停`)
}

const resumeTask = (task) => {
  task.status = 'running'
  ElMessage.success(`任务 "${task.name}" 已继续`)
}

const cancelTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    task.status = 'cancelled'
    ElMessage.success(`任务 "${task.name}" 已取消`)
  } catch {
    // 用户取消操作
  }
}

const retryTask = (task) => {
  task.status = 'pending'
  task.progress = 0
  task.startedAt = null
  task.completedAt = null
  ElMessage.success(`任务 "${task.name}" 已重新提交`)
}

const cloneTask = (task) => {
  const clonedTask = {
    ...task,
    id: tasks.value.length + 1,
    name: `${task.name} (副本)`,
    status: 'pending',
    progress: 0,
    createdAt: new Date(),
    startedAt: null,
    completedAt: null,
    logs: [],
    result: null
  }
  
  tasks.value.unshift(clonedTask)
  totalTasks.value++
  
  ElMessage.success(`任务 "${task.name}" 已克隆`)
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${task.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      tasks.value.splice(index, 1)
      totalTasks.value--
      ElMessage.success('任务删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const getStatusColor = (status) => {
  const statusMap = {
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    pending: 'warning',
    cancelled: 'info',
    paused: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    pending: '等待中',
    cancelled: '已取消',
    paused: '已暂停'
  }
  return statusMap[status] || '未知'
}

const getTypeColor = (type) => {
  const typeMap = {
    data_processing: 'primary',
    text_analysis: 'success',
    code_generation: 'warning',
    image_processing: 'danger',
    custom: 'info'
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    data_processing: '数据处理',
    text_analysis: '文本分析',
    code_generation: '代码生成',
    image_processing: '图像处理',
    custom: '自定义'
  }
  return typeMap[type] || '未知'
}

const getPriorityColor = (priority) => {
  const priorityMap = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || '未知'
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const getDuration = (task) => {
  if (!task.startedAt) return '-'
  
  const end = task.completedAt || new Date()
  const start = new Date(task.startedAt)
  const diffMs = end - start
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMinutes / 60)
  
  if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes % 60}分钟`
  }
  return `${diffMinutes}分钟`
}
</script>

<style scoped>
.tasks-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.tasks-table {
  margin-bottom: 20px;
}

.task-row {
  cursor: pointer;
}

.task-row:hover {
  background-color: #f5f7fa;
}

.task-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name {
  font-weight: 500;
}

.progress-container {
  padding: 0 8px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.task-detail {
  max-height: 600px;
  overflow-y: auto;
}

.task-info {
  padding: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #303133;
}

.description-section,
.progress-section {
  margin-bottom: 24px;
}

.description-section h4,
.progress-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.description-section p {
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.task-config {
  padding: 16px;
}

.task-logs {
  padding: 16px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #f8f9fa;
}

.log-entry {
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  display: flex;
  gap: 12px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.info {
  color: #409eff;
}

.log-entry.warning {
  color: #e6a23c;
}

.log-entry.error {
  color: #f56c6c;
}

.timestamp {
  color: #999;
  min-width: 140px;
}

.level {
  font-weight: 600;
  min-width: 60px;
}

.message {
  flex: 1;
}

.task-result {
  padding: 16px;
}

.task-result pre {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.5;
}

.no-result {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
}
</style>