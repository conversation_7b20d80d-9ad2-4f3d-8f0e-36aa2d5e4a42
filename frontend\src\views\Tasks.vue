<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>任务管理</span>
          <div class="header-actions">
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-right: 10px;">
              <el-option label="全部" value="" />
              <el-option label="等待中" value="pending" />
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
            <el-select v-model="agentFilter" placeholder="智能体筛选" style="width: 150px; margin-right: 10px;">
              <el-option label="全部智能体" value="" />
              <el-option label="意图识别" value="intent_recognizer" />
              <el-option label="任务分解" value="task_decomposer" />
              <el-option label="代码生成" value="code_generator" />
              <el-option label="市场调研" value="market_researcher" />
              <el-option label="结果验证" value="result_validator" />
            </el-select>
            <el-button @click="refreshTasks">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              创建任务
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="filteredTasks" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="任务ID" width="200" show-overflow-tooltip />
        <el-table-column prop="session_id" label="会话ID" width="200" show-overflow-tooltip />
        <el-table-column prop="agent_name" label="智能体" width="150">
          <template #default="{ row }">
            <el-tag size="small">{{ getAgentDisplayName(row.agent_name) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="task_name" label="任务名称" width="200" />
        <el-table-column prop="task_description" label="任务描述" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ getStatusText(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="120">
          <template #default="{ row }">
            <el-progress :percentage="Math.round(row.progress * 100)" :status="getProgressStatus(row.status)" />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewTask(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="viewTaskLogs(row)">
              日志
            </el-button>
            <el-button
              v-if="row.status === 'running'"
              type="text"
              size="small"
              danger
              @click="cancelTask(row)"
            >
              取消
            </el-button>
            <el-button
              v-if="row.status === 'failed'"
              type="text"
              size="small"
              @click="retryTask(row)"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; text-align: right;"
      />
    </el-card>

    <!-- 创建任务对话框 -->
    <el-dialog v-model="createDialogVisible" title="创建任务" width="600px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="智能体" prop="agent_id">
          <el-select v-model="createForm.agent_id" placeholder="请选择智能体" style="width: 100%;">
            <el-option label="意图识别智能体" value="intent_recognizer" />
            <el-option label="任务分解智能体" value="task_decomposer" />
            <el-option label="代码生成智能体" value="code_generator" />
            <el-option label="市场调研智能体" value="market_researcher" />
            <el-option label="结果验证智能体" value="result_validator" />
          </el-select>
        </el-form-item>
        <el-form-item label="技能名称" prop="skill_name">
          <el-select v-model="createForm.skill_name" placeholder="请选择技能" style="width: 100%;">
            <el-option
              v-for="skill in availableSkills"
              :key="skill.value"
              :label="skill.label"
              :value="skill.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称" prop="task_name">
          <el-input v-model="createForm.task_name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input v-model="createForm.task_description" type="textarea" :rows="3" placeholder="请输入任务描述" />
        </el-form-item>
        <el-form-item label="输入参数" prop="parameters">
          <el-input
            v-model="createForm.parameters"
            type="textarea"
            :rows="6"
            placeholder="请输入JSON格式的参数"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createTask" :loading="createLoading">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="任务详情" width="800px">
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">
            {{ selectedTask.id }}
          </el-descriptions-item>
          <el-descriptions-item label="会话ID">
            {{ selectedTask.session_id }}
          </el-descriptions-item>
          <el-descriptions-item label="智能体">
            {{ getAgentDisplayName(selectedTask.agent_name) }}
          </el-descriptions-item>
          <el-descriptions-item label="任务名称">
            {{ selectedTask.task_name }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            {{ Math.round(selectedTask.progress * 100) }}%
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedTask.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatTime(selectedTask.started_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ formatTime(selectedTask.completed_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">
            {{ selectedTask.task_description }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">输入数据</h4>
        <pre>{{ JSON.stringify(selectedTask.input_data, null, 2) }}</pre>

        <h4 style="margin: 20px 0 10px 0;">输出数据</h4>
        <pre v-if="selectedTask.output_data">{{ JSON.stringify(selectedTask.output_data, null, 2) }}</pre>
        <div v-else class="no-data">暂无输出数据</div>

        <div v-if="selectedTask.error_message" style="margin-top: 20px;">
          <h4 style="margin: 20px 0 10px 0;">错误信息</h4>
          <el-alert :title="selectedTask.error_message" type="error" show-icon />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { sessionApi, agentApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const selectedTask = ref(null)
const taskLogs = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 筛选
const statusFilter = ref('')
const agentFilter = ref('')

// 对话框状态
const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const logsDialogVisible = ref(false)

// 加载状态
const createLoading = ref(false)
const logsLoading = ref(false)

// 日志级别
const logLevel = ref('')

// 表单数据
const createForm = ref({
  agent_id: '',
  skill_name: '',
  task_name: '',
  task_description: '',
  parameters: '{\n  \n}'
})

// 表单引用
const createFormRef = ref()

// 表单验证规则
const createRules = {
  agent_id: [
    { required: true, message: '请选择智能体', trigger: 'change' }
  ],
  skill_name: [
    { required: true, message: '请选择技能', trigger: 'change' }
  ],
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  parameters: [
    { required: true, message: '请输入参数', trigger: 'blur' },
    { validator: validateJSON, trigger: 'blur' }
  ]
}

// 可用技能
const availableSkills = computed(() => {
  const skillsMap = {
    'intent_recognizer': [
      { label: '识别意图', value: 'recognize_intent' },
      { label: '提取实体', value: 'extract_entities' }
    ],
    'task_decomposer': [
      { label: '分解任务', value: 'decompose_task' },
      { label: '优化任务', value: 'optimize_tasks' }
    ],
    'code_generator': [
      { label: '生成代码', value: 'generate_code' },
      { label: '优化代码', value: 'optimize_code' },
      { label: '审查代码', value: 'review_code' }
    ],
    'market_researcher': [
      { label: '市场调研', value: 'conduct_market_research' },
      { label: '竞争分析', value: 'analyze_competition' },
      { label: '趋势分析', value: 'analyze_trends' }
    ],
    'result_validator': [
      { label: '验证结果', value: 'validate_result' },
      { label: '评估质量', value: 'assess_quality' },
      { label: '匹配需求', value: 'verify_requirement_match' }
    ]
  }

  return skillsMap[createForm.value.agent_id] || []
})

// 计算属性
const filteredTasks = computed(() => {
  let filtered = tasks.value

  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  if (agentFilter.value) {
    filtered = filtered.filter(task => task.agent_name === agentFilter.value)
  }

  return filtered
})

// 验证器
function validateJSON(rule, value, callback) {
  if (!value.trim()) {
    callback(new Error('请输入参数'))
    return
  }

  try {
    JSON.parse(value)
    callback()
  } catch (error) {
    callback(new Error('参数必须是有效的JSON格式'))
  }
}

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    // 这里应该从后端加载任务列表
    // 暂时使用模拟数据
    const mockTasks = [
      {
        id: 'task_001',
        session_id: 'session_001',
        agent_name: 'intent_recognizer',
        task_name: '识别用户意图',
        task_description: '分析用户输入的意图类型',
        status: 'completed',
        progress: 1.0,
        input_data: { user_input: '生成一个Python函数' },
        output_data: { intent: 'generate_code', confidence: 0.95 },
        created_at: '2024-01-15T10:30:00Z',
        started_at: '2024-01-15T10:30:05Z',
        completed_at: '2024-01-15T10:30:15Z'
      },
      {
        id: 'task_002',
        session_id: 'session_001',
        agent_name: 'code_generator',
        task_name: '生成Python代码',
        task_description: '根据需求生成Python函数代码',
        status: 'running',
        progress: 0.6,
        input_data: { task_description: '生成一个排序函数', language: 'Python' },
        output_data: null,
        created_at: '2024-01-15T10:30:20Z',
        started_at: '2024-01-15T10:30:25Z',
        completed_at: null
      }
    ]

    tasks.value = mockTasks
    total.value = mockTasks.length

  } catch (error) {
    console.error('加载任务列表失败:', error)
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const refreshTasks = () => {
  loadTasks()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadTasks()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadTasks()
}

const showCreateDialog = () => {
  createForm.value = {
    agent_id: '',
    skill_name: '',
    task_name: '',
    task_description: '',
    parameters: '{\n  \n}'
  }
  createDialogVisible.value = true
}

const createTask = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()

    createLoading.value = true

    // 解析参数
    const parameters = JSON.parse(createForm.value.parameters)

    const taskData = {
      skill_name: createForm.value.skill_name,
      parameters: parameters
    }

    // 调用智能体API
    await agentApi.createTask(createForm.value.agent_id, taskData)

    ElMessage.success('任务创建成功')
    createDialogVisible.value = false
    loadTasks()

  } catch (error) {
    console.error('创建任务失败:', error)
    if (error.message.includes('JSON')) {
      ElMessage.error('参数格式错误，请检查JSON格式')
    } else {
      ElMessage.error('创建任务失败')
    }
  } finally {
    createLoading.value = false
  }
}

const viewTask = (task) => {
  selectedTask.value = task
  detailDialogVisible.value = true
}

const viewTaskLogs = async (task) => {
  selectedTask.value = task
  logLevel.value = ''
  logsDialogVisible.value = true
  await loadTaskLogs()
}

const loadTaskLogs = async () => {
  if (!selectedTask.value) return

  logsLoading.value = true
  try {
    // 这里应该从后端加载任务日志
    // 暂时使用模拟数据
    const mockLogs = [
      {
        id: 'log_001',
        level: 'INFO',
        message: '任务开始执行',
        timestamp: '2024-01-15T10:30:25Z',
        context: { task_id: selectedTask.value.id }
      },
      {
        id: 'log_002',
        level: 'DEBUG',
        message: '正在调用千问API',
        timestamp: '2024-01-15T10:30:30Z',
        context: { model: 'qwen-plus' }
      }
    ]

    taskLogs.value = mockLogs
  } catch (error) {
    console.error('加载任务日志失败:', error)
    ElMessage.error('加载任务日志失败')
  } finally {
    logsLoading.value = false
  }
}

const cancelTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 "${task.task_name}" 吗？`,
      '确认取消',
      {
        confirmButtonText: '取消任务',
        cancelButtonText: '不取消',
        type: 'warning'
      }
    )

    // 这里应该调用取消任务的API
    ElMessage.success('任务取消请求已发送')
    loadTasks()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

const retryTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要重试任务 "${task.task_name}" 吗？`,
      '确认重试',
      {
        confirmButtonText: '重试',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 这里应该调用重试任务的API
    ElMessage.success('任务重试请求已发送')
    loadTasks()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试任务失败:', error)
      ElMessage.error('重试任务失败')
    }
  }
}

// 工具方法
const getAgentDisplayName = (agentName) => {
  const nameMap = {
    'intent_recognizer': '意图识别',
    'task_decomposer': '任务分解',
    'code_generator': '代码生成',
    'market_researcher': '市场调研',
    'result_validator': '结果验证'
  }
  return nameMap[agentName] || agentName
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return undefined
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 监听智能体选择变化，重置技能选择
watch(() => createForm.value.agent_id, () => {
  createForm.value.skill_name = ''
})

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

pre {
  background-color: var(--el-fill-color-lighter);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.no-data {
  color: var(--el-text-color-secondary);
  text-align: center;
  padding: 20px;
}

.logs-container {
  max-height: 500px;
  overflow-y: auto;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.logs-content {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid var(--el-border-color);
}

.log-item.log-debug {
  border-left-color: var(--el-color-info);
  background-color: var(--el-color-info-light-9);
}

.log-item.log-info {
  border-left-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.log-item.log-warning {
  border-left-color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}

.log-item.log-error {
  border-left-color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.log-level {
  font-weight: bold;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: var(--el-fill-color);
}

.log-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.log-message {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 5px;
}

.log-context {
  font-size: 12px;
  background-color: var(--el-fill-color-darker);
  padding: 8px;
  border-radius: 3px;
  margin-top: 5px;
}

.log-context pre {
  margin: 0;
  background: none;
  padding: 0;
}
</style>
