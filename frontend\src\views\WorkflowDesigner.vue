<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="createNewWorkflow">
          <el-icon><Plus /></el-icon>
          新建工作流
        </el-button>
        <el-button @click="openWorkflow">
          <el-icon><FolderOpened /></el-icon>
          打开
        </el-button>
        <el-button @click="saveWorkflow" :disabled="!currentWorkflow">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-divider direction="vertical" />
        <el-button @click="runWorkflow" :disabled="!currentWorkflow" type="success">
          <el-icon><VideoPlay /></el-icon>
          运行
        </el-button>
        <el-button @click="stopWorkflow" :disabled="!isRunning" type="danger">
          <el-icon><VideoPause /></el-icon>
          停止
        </el-button>
      </div>

      <div class="toolbar-right">
        <el-input
          v-model="currentWorkflow.name"
          placeholder="工作流名称"
          style="width: 200px; margin-right: 10px;"
          v-if="currentWorkflow"
        />
        <el-button @click="showWorkflowSettings" :disabled="!currentWorkflow">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>

    <div class="designer-main">
      <!-- 组件面板 -->
      <div class="components-panel">
        <div class="panel-header">
          <h4>组件库</h4>
        </div>
        <div class="panel-content">
          <div class="component-group">
            <h5>智能体</h5>
            <div
              v-for="agent in availableAgents"
              :key="agent.id"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, 'agent', agent)"
            >
              <el-icon><Robot /></el-icon>
              <span>{{ agent.name }}</span>
            </div>
          </div>

          <div class="component-group">
            <h5>控制流</h5>
            <div
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, 'branch', { type: 'branch' })"
            >
              <el-icon><Share /></el-icon>
              <span>分支</span>
            </div>
            <div
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, 'loop', { type: 'loop' })"
            >
              <el-icon><Refresh /></el-icon>
              <span>循环</span>
            </div>
            <div
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, 'parallel', { type: 'parallel' })"
            >
              <el-icon><Operation /></el-icon>
              <span>并行</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas-area">
        <div
          class="canvas"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @click="clearSelection"
        >
          <div v-if="!currentWorkflow" class="empty-canvas">
            <el-icon class="empty-icon"><Connection /></el-icon>
            <div class="empty-text">请创建或打开一个工作流</div>
          </div>

          <div v-else class="workflow-canvas">
            <!-- 工作流步骤 -->
            <div
              v-for="(step, index) in currentWorkflow.steps"
              :key="step.id"
              class="workflow-step"
              :class="{ 'selected': selectedStep?.id === step.id }"
              :style="{ left: step.x + 'px', top: step.y + 'px' }"
              @click.stop="selectStep(step)"
              @mousedown="startDrag($event, step)"
            >
              <div class="step-header">
                <el-icon><Robot /></el-icon>
                <span>{{ step.name || getStepDisplayName(step) }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click.stop="removeStep(step)"
                  class="remove-btn"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
              <div class="step-content">
                <div class="step-info">
                  <span>{{ step.agent_id || step.type }}</span>
                </div>
              </div>

              <!-- 连接点 -->
              <div class="connection-point input" @click.stop=""></div>
              <div class="connection-point output" @click.stop=""></div>
            </div>

            <!-- 连接线 -->
            <svg class="connections-layer">
              <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7"
                        refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#409EFF" />
                </marker>
              </defs>
              <path
                v-for="connection in connections"
                :key="connection.id"
                :d="connection.path"
                stroke="#409EFF"
                stroke-width="2"
                fill="none"
                marker-end="url(#arrowhead)"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h4>属性</h4>
        </div>
        <div class="panel-content">
          <div v-if="!selectedStep" class="no-selection">
            <p>请选择一个步骤来编辑属性</p>
          </div>

          <div v-else class="step-properties">
            <el-form :model="selectedStep" label-width="80px" size="small">
              <el-form-item label="步骤名称">
                <el-input v-model="selectedStep.name" placeholder="输入步骤名称" />
              </el-form-item>

              <el-form-item label="智能体" v-if="selectedStep.type === 'agent'">
                <el-select v-model="selectedStep.agent_id" placeholder="选择智能体" style="width: 100%;">
                  <el-option
                    v-for="agent in availableAgents"
                    :key="agent.id"
                    :label="agent.name"
                    :value="agent.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="技能" v-if="selectedStep.agent_id">
                <el-select v-model="selectedStep.skill_name" placeholder="选择技能" style="width: 100%;">
                  <el-option
                    v-for="skill in getAgentSkills(selectedStep.agent_id)"
                    :key="skill.value"
                    :label="skill.label"
                    :value="skill.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="执行模式">
                <el-select v-model="selectedStep.execution_mode" style="width: 100%;">
                  <el-option label="顺序执行" value="sequential" />
                  <el-option label="并行执行" value="parallel" />
                </el-select>
              </el-form-item>

              <el-form-item label="超时时间">
                <el-input-number
                  v-model="selectedStep.timeout"
                  :min="0"
                  :max="3600"
                  style="width: 100%;"
                />
                <span style="margin-left: 8px; font-size: 12px; color: var(--el-text-color-secondary);">秒</span>
              </el-form-item>

              <el-form-item label="重试次数">
                <el-input-number
                  v-model="selectedStep.retry_count"
                  :min="0"
                  :max="10"
                  style="width: 100%;"
                />
              </el-form-item>

              <el-form-item label="错误处理">
                <el-select v-model="selectedStep.on_error" style="width: 100%;">
                  <el-option label="失败停止" value="fail" />
                  <el-option label="继续执行" value="continue" />
                  <el-option label="重试" value="retry" />
                </el-select>
              </el-form-item>

              <el-form-item label="输入映射">
                <el-input
                  v-model="selectedStep.input_mapping_text"
                  type="textarea"
                  :rows="4"
                  placeholder="JSON格式的输入映射"
                  @blur="updateInputMapping"
                />
              </el-form-item>

              <el-form-item label="输出映射">
                <el-input
                  v-model="selectedStep.output_mapping_text"
                  type="textarea"
                  :rows="4"
                  placeholder="JSON格式的输出映射"
                  @blur="updateOutputMapping"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- 打开工作流对话框 -->
    <el-dialog v-model="openDialogVisible" title="打开工作流" width="600px">
      <el-table :data="availableWorkflows" @row-click="selectWorkflowToOpen">
        <el-table-column prop="name" label="工作流名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="openSelectedWorkflow(row)">
              打开
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="openDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 工作流设置对话框 -->
    <el-dialog v-model="settingsDialogVisible" title="工作流设置" width="500px">
      <el-form :model="currentWorkflow" label-width="100px" v-if="currentWorkflow">
        <el-form-item label="工作流名称">
          <el-input v-model="currentWorkflow.name" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="currentWorkflow.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="版本">
          <el-input v-model="currentWorkflow.version" />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="currentWorkflow.tags_text" placeholder="用逗号分隔" />
        </el-form-item>
        <el-form-item label="最大迭代">
          <el-input-number v-model="currentWorkflow.max_iterations" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="超时时间">
          <el-input-number v-model="currentWorkflow.timeout" :min="0" :max="7200" />
          <span style="margin-left: 8px; font-size: 12px; color: var(--el-text-color-secondary);">秒</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="settingsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveWorkflowSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { workflowApi, agentApi } from '@/api'
import dayjs from 'dayjs'

// 响应式数据
const currentWorkflow = ref(null)
const selectedStep = ref(null)
const availableAgents = ref([])
const availableWorkflows = ref([])
const connections = ref([])

// 对话框状态
const openDialogVisible = ref(false)
const settingsDialogVisible = ref(false)

// 运行状态
const isRunning = ref(false)

// 拖拽状态
const dragState = ref({
  isDragging: false,
  dragType: null,
  dragData: null,
  startX: 0,
  startY: 0
})

// 可用智能体
const mockAgents = [
  { id: 'intent_recognizer', name: '意图识别智能体' },
  { id: 'task_decomposer', name: '任务分解智能体' },
  { id: 'code_generator', name: '代码生成智能体' },
  { id: 'market_researcher', name: '市场调研智能体' },
  { id: 'result_validator', name: '结果验证智能体' }
]

// 智能体技能映射
const agentSkillsMap = {
  'intent_recognizer': [
    { label: '识别意图', value: 'recognize_intent' },
    { label: '提取实体', value: 'extract_entities' }
  ],
  'task_decomposer': [
    { label: '分解任务', value: 'decompose_task' },
    { label: '优化任务', value: 'optimize_tasks' }
  ],
  'code_generator': [
    { label: '生成代码', value: 'generate_code' },
    { label: '优化代码', value: 'optimize_code' },
    { label: '审查代码', value: 'review_code' }
  ],
  'market_researcher': [
    { label: '市场调研', value: 'conduct_market_research' },
    { label: '竞争分析', value: 'analyze_competition' },
    { label: '趋势分析', value: 'analyze_trends' }
  ],
  'result_validator': [
    { label: '验证结果', value: 'validate_result' },
    { label: '评估质量', value: 'assess_quality' },
    { label: '匹配需求', value: 'verify_requirement_match' }
  ]
}

// 方法
const createNewWorkflow = () => {
  currentWorkflow.value = {
    id: `workflow_${Date.now()}`,
    name: '新工作流',
    description: '',
    version: '1.0.0',
    steps: [],
    tags: [],
    tags_text: '',
    max_iterations: 3,
    timeout: 3600,
    input_schema: {},
    output_schema: {},
    metadata: {}
  }
  selectedStep.value = null
  connections.value = []
  ElMessage.success('新工作流已创建')
}

const openWorkflow = async () => {
  try {
    // 这里应该从后端加载工作流列表
    const mockWorkflows = [
      {
        id: 'code_generation_workflow',
        name: '代码生成工作流',
        description: '基于用户需求生成高质量代码的完整工作流',
        updated_at: '2024-01-15T10:30:00Z'
      },
      {
        id: 'market_research_workflow',
        name: '市场调研工作流',
        description: '全面的市场调研和分析工作流',
        updated_at: '2024-01-14T15:20:00Z'
      }
    ]

    availableWorkflows.value = mockWorkflows
    openDialogVisible.value = true

  } catch (error) {
    console.error('加载工作流列表失败:', error)
    ElMessage.error('加载工作流列表失败')
  }
}

const openSelectedWorkflow = async (workflow) => {
  try {
    // 这里应该从后端加载具体的工作流定义
    const mockWorkflowData = {
      ...workflow,
      steps: [
        {
          id: 'step_1',
          name: '意图识别',
          type: 'agent',
          agent_id: 'intent_recognizer',
          skill_name: 'recognize_intent',
          execution_mode: 'sequential',
          x: 100,
          y: 100,
          timeout: 30,
          retry_count: 3,
          on_error: 'fail',
          input_mapping: {},
          output_mapping: {},
          input_mapping_text: '{}',
          output_mapping_text: '{}'
        },
        {
          id: 'step_2',
          name: '任务分解',
          type: 'agent',
          agent_id: 'task_decomposer',
          skill_name: 'decompose_task',
          execution_mode: 'sequential',
          x: 300,
          y: 100,
          timeout: 60,
          retry_count: 3,
          on_error: 'fail',
          input_mapping: {},
          output_mapping: {},
          input_mapping_text: '{}',
          output_mapping_text: '{}'
        }
      ]
    }

    currentWorkflow.value = mockWorkflowData
    currentWorkflow.value.tags_text = mockWorkflowData.tags?.join(', ') || ''

    // 初始化步骤的文本字段
    currentWorkflow.value.steps.forEach(step => {
      step.input_mapping_text = JSON.stringify(step.input_mapping || {}, null, 2)
      step.output_mapping_text = JSON.stringify(step.output_mapping || {}, null, 2)
    })

    openDialogVisible.value = false
    selectedStep.value = null

    ElMessage.success('工作流已打开')

  } catch (error) {
    console.error('打开工作流失败:', error)
    ElMessage.error('打开工作流失败')
  }
}

const saveWorkflow = async () => {
  if (!currentWorkflow.value) return

  try {
    // 准备保存数据
    const workflowData = {
      ...currentWorkflow.value,
      tags: currentWorkflow.value.tags_text ?
        currentWorkflow.value.tags_text.split(',').map(tag => tag.trim()) : []
    }

    // 这里应该调用后端API保存工作流
    ElMessage.success('工作流已保存')

  } catch (error) {
    console.error('保存工作流失败:', error)
    ElMessage.error('保存工作流失败')
  }
}

const runWorkflow = async () => {
  if (!currentWorkflow.value) return

  try {
    isRunning.value = true

    // 这里应该调用后端API执行工作流
    ElMessage.success('工作流开始执行')

    // 模拟执行过程
    setTimeout(() => {
      isRunning.value = false
      ElMessage.success('工作流执行完成')
    }, 5000)

  } catch (error) {
    console.error('执行工作流失败:', error)
    ElMessage.error('执行工作流失败')
    isRunning.value = false
  }
}

const stopWorkflow = () => {
  isRunning.value = false
  ElMessage.warning('工作流已停止')
}

const showWorkflowSettings = () => {
  if (!currentWorkflow.value) return
  settingsDialogVisible.value = true
}

const saveWorkflowSettings = () => {
  if (currentWorkflow.value.tags_text) {
    currentWorkflow.value.tags = currentWorkflow.value.tags_text
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag)
  }

  settingsDialogVisible.value = false
  ElMessage.success('工作流设置已保存')
}

// 拖拽处理
const handleDragStart = (event, type, data) => {
  dragState.value = {
    isDragging: true,
    dragType: type,
    dragData: data
  }
  event.dataTransfer.effectAllowed = 'copy'
}

const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
}

const handleDrop = (event) => {
  event.preventDefault()

  if (!currentWorkflow.value || !dragState.value.isDragging) return

  const rect = event.currentTarget.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const newStep = {
    id: `step_${Date.now()}`,
    name: '',
    type: dragState.value.dragType,
    x: x - 75, // 调整位置使组件居中
    y: y - 40,
    execution_mode: 'sequential',
    timeout: 30,
    retry_count: 3,
    on_error: 'fail',
    input_mapping: {},
    output_mapping: {},
    input_mapping_text: '{}',
    output_mapping_text: '{}'
  }

  if (dragState.value.dragType === 'agent') {
    newStep.agent_id = dragState.value.dragData.id
    newStep.name = dragState.value.dragData.name
  } else {
    newStep.name = dragState.value.dragData.type
  }

  currentWorkflow.value.steps.push(newStep)

  dragState.value = {
    isDragging: false,
    dragType: null,
    dragData: null
  }
}

// 步骤操作
const selectStep = (step) => {
  selectedStep.value = step
}

const clearSelection = () => {
  selectedStep.value = null
}

const removeStep = (step) => {
  const index = currentWorkflow.value.steps.findIndex(s => s.id === step.id)
  if (index > -1) {
    currentWorkflow.value.steps.splice(index, 1)
    if (selectedStep.value?.id === step.id) {
      selectedStep.value = null
    }
  }
}

const startDrag = (event, step) => {
  const startX = event.clientX - step.x
  const startY = event.clientY - step.y

  const handleMouseMove = (e) => {
    step.x = e.clientX - startX
    step.y = e.clientY - startY
  }

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 属性更新
const updateInputMapping = () => {
  if (!selectedStep.value) return

  try {
    selectedStep.value.input_mapping = JSON.parse(selectedStep.value.input_mapping_text || '{}')
  } catch (error) {
    ElMessage.error('输入映射JSON格式错误')
    selectedStep.value.input_mapping_text = JSON.stringify(selectedStep.value.input_mapping || {}, null, 2)
  }
}

const updateOutputMapping = () => {
  if (!selectedStep.value) return

  try {
    selectedStep.value.output_mapping = JSON.parse(selectedStep.value.output_mapping_text || '{}')
  } catch (error) {
    ElMessage.error('输出映射JSON格式错误')
    selectedStep.value.output_mapping_text = JSON.stringify(selectedStep.value.output_mapping || {}, null, 2)
  }
}

// 工具方法
const getStepDisplayName = (step) => {
  if (step.type === 'agent') {
    const agent = availableAgents.value.find(a => a.id === step.agent_id)
    return agent ? agent.name : step.agent_id
  }
  return step.type
}

const getAgentSkills = (agentId) => {
  return agentSkillsMap[agentId] || []
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  availableAgents.value = mockAgents
})
</script>

<style scoped>
.workflow-designer {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.designer-toolbar {
  height: 60px;
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--el-bg-color);
  flex-shrink: 0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.designer-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.components-panel {
  width: 250px;
  border-right: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color);
  display: flex;
  flex-direction: column;
}

.properties-panel {
  width: 300px;
  border-left: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color);
  display: flex;
  flex-direction: column;
}

.panel-header {
  height: 50px;
  padding: 0 15px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  align-items: center;
  background-color: var(--el-fill-color-lighter);
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.panel-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.component-group {
  margin-bottom: 20px;
}

.component-group h5 {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  text-transform: uppercase;
  font-weight: 600;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 5px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  background-color: var(--el-fill-color-blank);
}

.component-item:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  transform: translateX(2px);
}

.component-item:active {
  cursor: grabbing;
}

.component-item span {
  font-size: 12px;
  font-weight: 500;
}

.canvas-area {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: var(--el-bg-color-page);
  background-image:
    radial-gradient(circle, var(--el-border-color-lighter) 1px, transparent 1px);
  background-size: 20px 20px;
  overflow: auto;
}

.empty-canvas {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-secondary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
}

.workflow-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 800px;
  min-width: 1200px;
}

.workflow-step {
  position: absolute;
  width: 150px;
  min-height: 80px;
  background-color: var(--el-bg-color);
  border: 2px solid var(--el-border-color);
  border-radius: 8px;
  cursor: move;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workflow-step:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.workflow-step.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 10px;
  background-color: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-lighter);
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  font-weight: 600;
}

.step-header .remove-btn {
  margin-left: auto;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.workflow-step:hover .remove-btn {
  opacity: 1;
}

.step-content {
  padding: 10px;
}

.step-info {
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--el-color-primary);
  border: 2px solid var(--el-bg-color);
  border-radius: 50%;
  cursor: crosshair;
}

.connection-point.input {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point.output {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.no-selection {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
}

.step-properties {
  padding: 0;
}

.step-properties .el-form-item {
  margin-bottom: 15px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .components-panel {
    width: 200px;
  }

  .properties-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .designer-toolbar {
    flex-direction: column;
    height: auto;
    padding: 10px;
    gap: 10px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .components-panel,
  .properties-panel {
    display: none;
  }

  .canvas-area {
    width: 100%;
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.canvas::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.panel-content::-webkit-scrollbar-track,
.canvas::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.canvas::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.canvas::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}
</style>
