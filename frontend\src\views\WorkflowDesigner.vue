<template>
  <div class="workflow-designer">
    <div class="designer-header">
      <div class="header-left">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="workflow-info">
          <h1>{{ isEditing ? '编辑工作流' : '创建工作流' }}</h1>
          <span v-if="workflowName">{{ workflowName }}</span>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button @click="saveWorkflow">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button type="primary" @click="deployWorkflow">
          <el-icon><Upload /></el-icon>
          部署
        </el-button>
      </div>
    </div>

    <div class="designer-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="tool-section">
          <h3>节点类型</h3>
          <div class="node-types">
            <div 
              v-for="nodeType in nodeTypes" 
              :key="nodeType.type"
              class="node-type-item"
              draggable="true"
              @dragstart="onDragStart($event, nodeType)"
            >
              <el-icon>{{ nodeType.icon }}</el-icon>
              <span>{{ nodeType.label }}</span>
            </div>
          </div>
        </div>
        
        <div class="tool-section">
          <h3>操作</h3>
          <el-button size="small" @click="clearCanvas">
            <el-icon><Delete /></el-icon>
            清空画布
          </el-button>
          <el-button size="small" @click="autoLayout">
            <el-icon><Grid /></el-icon>
            自动布局
          </el-button>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas-container">
        <div 
          ref="canvas"
          class="canvas"
          @drop="onDrop"
          @dragover="onDragOver"
          @click="onCanvasClick"
        >
          <!-- 网格背景 -->
          <div class="grid-background"></div>
          
          <!-- 工作流节点 -->
          <div 
            v-for="node in nodes" 
            :key="node.id"
            class="workflow-node"
            :class="{ selected: selectedNode?.id === node.id }"
            :style="{ left: node.x + 'px', top: node.y + 'px' }"
            @click.stop="selectNode(node)"
            @mousedown="startDrag($event, node)"
          >
            <div class="node-header">
              <el-icon>{{ getNodeIcon(node.type) }}</el-icon>
              <span>{{ node.name }}</span>
            </div>
            <div class="node-content">
              <p>{{ node.description }}</p>
            </div>
            <div class="node-ports">
              <div class="input-port" @click.stop="connectPort(node, 'input')"></div>
              <div class="output-port" @click.stop="connectPort(node, 'output')"></div>
            </div>
          </div>
          
          <!-- 连接线 -->
          <svg class="connections" :width="canvasWidth" :height="canvasHeight">
            <path 
              v-for="connection in connections" 
              :key="connection.id"
              :d="getConnectionPath(connection)"
              class="connection-line"
              @click="selectConnection(connection)"
            />
          </svg>
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <div v-if="selectedNode" class="node-properties">
          <h3>节点属性</h3>
          <el-form :model="selectedNode" label-width="80px">
            <el-form-item label="名称">
              <el-input v-model="selectedNode.name" />
            </el-form-item>
            <el-form-item label="描述">
              <el-input 
                v-model="selectedNode.description" 
                type="textarea" 
                :rows="3"
              />
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="selectedNode.type" disabled>
                <el-option 
                  v-for="type in nodeTypes" 
                  :key="type.type"
                  :label="type.label" 
                  :value="type.type"
                />
              </el-select>
            </el-form-item>
          </el-form>
          
          <el-button type="danger" size="small" @click="deleteNode">
            删除节点
          </el-button>
        </div>
        
        <div v-else class="no-selection">
          <p>选择一个节点来编辑属性</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Document, Upload, Delete, Grid } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const canvas = ref(null)
const canvasWidth = ref(800)
const canvasHeight = ref(600)
const workflowName = ref('')
const isEditing = ref(false)
const selectedNode = ref(null)
const selectedConnection = ref(null)
const nodes = ref([])
const connections = ref([])
const nodeIdCounter = ref(1)
const connectionIdCounter = ref(1)

// 节点类型定义
const nodeTypes = [
  { type: 'start', label: '开始', icon: 'VideoPlay' },
  { type: 'task', label: '任务', icon: 'Operation' },
  { type: 'condition', label: '条件', icon: 'QuestionFilled' },
  { type: 'api', label: 'API调用', icon: 'Connection' },
  { type: 'data', label: '数据处理', icon: 'DataAnalysis' },
  { type: 'end', label: '结束', icon: 'VideoStop' }
]

onMounted(() => {
  // 检查是否是编辑模式
  if (route.query.id) {
    isEditing.value = true
    loadWorkflow(route.query.id)
  }
  
  // 设置画布尺寸
  updateCanvasSize()
  window.addEventListener('resize', updateCanvasSize)
})

const updateCanvasSize = () => {
  if (canvas.value) {
    const rect = canvas.value.getBoundingClientRect()
    canvasWidth.value = rect.width
    canvasHeight.value = rect.height
  }
}

const loadWorkflow = (id) => {
  // 模拟加载工作流数据
  workflowName.value = '示例工作流'
  nodes.value = [
    {
      id: 1,
      type: 'start',
      name: '开始',
      description: '工作流开始节点',
      x: 100,
      y: 100
    },
    {
      id: 2,
      type: 'task',
      name: '数据处理',
      description: '处理输入数据',
      x: 300,
      y: 100
    }
  ]
  nodeIdCounter.value = 3
}

const goBack = () => {
  router.push('/workflows')
}

const saveWorkflow = () => {
  // 模拟保存工作流
  ElMessage.success('工作流保存成功')
}

const deployWorkflow = () => {
  // 模拟部署工作流
  ElMessage.success('工作流部署成功')
}

const onDragStart = (event, nodeType) => {
  event.dataTransfer.setData('nodeType', JSON.stringify(nodeType))
}

const onDragOver = (event) => {
  event.preventDefault()
}

const onDrop = (event) => {
  event.preventDefault()
  const nodeTypeData = event.dataTransfer.getData('nodeType')
  if (nodeTypeData) {
    const nodeType = JSON.parse(nodeTypeData)
    const rect = canvas.value.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    createNode(nodeType, x, y)
  }
}

const createNode = (nodeType, x, y) => {
  const newNode = {
    id: nodeIdCounter.value++,
    type: nodeType.type,
    name: nodeType.label,
    description: `${nodeType.label}节点`,
    x: x - 75, // 居中
    y: y - 40
  }
  
  nodes.value.push(newNode)
}

const selectNode = (node) => {
  selectedNode.value = node
  selectedConnection.value = null
}

const onCanvasClick = () => {
  selectedNode.value = null
  selectedConnection.value = null
}

const deleteNode = () => {
  if (selectedNode.value) {
    const index = nodes.value.findIndex(n => n.id === selectedNode.value.id)
    if (index > -1) {
      nodes.value.splice(index, 1)
      selectedNode.value = null
    }
  }
}

const clearCanvas = () => {
  nodes.value = []
  connections.value = []
  selectedNode.value = null
  selectedConnection.value = null
}

const autoLayout = () => {
  // 简单的自动布局算法
  nodes.value.forEach((node, index) => {
    node.x = 100 + (index % 3) * 200
    node.y = 100 + Math.floor(index / 3) * 150
  })
}

const getNodeIcon = (type) => {
  const nodeType = nodeTypes.find(nt => nt.type === type)
  return nodeType ? nodeType.icon : 'Operation'
}

const startDrag = (event, node) => {
  const startX = event.clientX - node.x
  const startY = event.clientY - node.y
  
  const onMouseMove = (e) => {
    node.x = e.clientX - startX
    node.y = e.clientY - startY
  }
  
  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

const connectPort = (node, portType) => {
  // 连接端口逻辑
  console.log('连接端口:', node.name, portType)
}

const getConnectionPath = (connection) => {
  // 生成连接线路径
  return `M ${connection.startX} ${connection.startY} L ${connection.endX} ${connection.endY}`
}

const selectConnection = (connection) => {
  selectedConnection.value = connection
  selectedNode.value = null
}
</script>

<style scoped>
.workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.workflow-info h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.toolbar {
  width: 200px;
  background: #f5f7fa;
  border-right: 1px solid #e4e7ed;
  padding: 16px;
  overflow-y: auto;
}

.tool-section {
  margin-bottom: 24px;
}

.tool-section h3 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.node-types {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-type-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.3s ease;
}

.node-type-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.node-type-item:active {
  cursor: grabbing;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background: #fafafa;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

.workflow-node {
  position: absolute;
  width: 150px;
  background: #fff;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  cursor: move;
  transition: all 0.3s ease;
}

.workflow-node:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.workflow-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 6px 6px 0 0;
  font-weight: 600;
  font-size: 12px;
}

.node-content {
  padding: 8px 12px;
}

.node-content p {
  margin: 0;
  font-size: 11px;
  color: #666;
  line-height: 1.4;
}

.node-ports {
  position: relative;
}

.input-port,
.output-port {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409eff;
  border: 2px solid #fff;
  border-radius: 50%;
  cursor: pointer;
}

.input-port {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.output-port {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connections {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.connection-line {
  stroke: #409eff;
  stroke-width: 2;
  fill: none;
  pointer-events: stroke;
  cursor: pointer;
}

.properties-panel {
  width: 250px;
  background: #fff;
  border-left: 1px solid #e4e7ed;
  padding: 16px;
  overflow-y: auto;
}

.node-properties h3 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.no-selection {
  text-align: center;
  color: #909399;
  margin-top: 40px;
}
</style>