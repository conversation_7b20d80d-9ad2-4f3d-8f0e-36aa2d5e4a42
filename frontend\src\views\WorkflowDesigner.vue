<template>
  <div class="workflow-designer">
    <div class="designer-toolbar">
      <el-button type="primary">
        <el-icon><Plus /></el-icon>
        新建工作流
      </el-button>
      <el-button>
        <el-icon><FolderOpened /></el-icon>
        打开
      </el-button>
      <el-button>
        <el-icon><Document /></el-icon>
        保存
      </el-button>
      <el-divider direction="vertical" />
      <el-button>
        <el-icon><VideoPlay /></el-icon>
        运行
      </el-button>
      <el-button>
        <el-icon><VideoPause /></el-icon>
        停止
      </el-button>
    </div>
    
    <div class="designer-canvas">
      <div class="empty-container">
        <el-icon class="empty-icon"><Connection /></el-icon>
        <div class="empty-text">工作流设计器功能开发中...</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 工作流设计器页面 - 待实现
</script>

<style scoped>
.workflow-designer {
  height: calc(100vh - 120px);
}

.designer-toolbar {
  height: 60px;
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: var(--el-bg-color);
}

.designer-canvas {
  height: calc(100% - 60px);
  position: relative;
  overflow: hidden;
  background-color: var(--el-bg-color-page);
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-secondary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
}
</style>
