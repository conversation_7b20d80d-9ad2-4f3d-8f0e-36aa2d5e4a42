<template>
  <div class="workflow-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-row :gutter="20" align="middle">
        <el-col :span="18">
          <div class="header-left">
            <el-button 
              type="text" 
              @click="$router.go(-1)"
              class="back-btn"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div class="workflow-info">
              <h1>{{ workflow.name }}</h1>
              <div class="workflow-meta">
                <el-tag :type="getStatusType(workflow.status)">{{ workflow.status }}</el-tag>
                <span class="meta-item">创建时间：{{ workflow.created_at }}</span>
                <span class="meta-item">最后修改：{{ workflow.updated_at }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="header-actions">
            <el-button type="primary" @click="editWorkflow">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button 
              :type="workflow.status === '运行中' ? 'danger' : 'success'"
              @click="toggleWorkflow"
            >
              <el-icon><VideoPlay v-if="workflow.status !== '运行中'" /><VideoPause v-else /></el-icon>
              {{ workflow.status === '运行中' ? '停止' : '启动' }}
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button>
                更多操作
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="duplicate">复制工作流</el-dropdown-item>
                  <el-dropdown-item command="export">导出配置</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除工作流</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 工作流详情内容 -->
    <div class="workflow-content">
      <el-row :gutter="20">
        <!-- 左侧：工作流图表 -->
        <el-col :span="16">
          <el-card class="workflow-graph-card">
            <template #header>
              <div class="card-header">
                <span>工作流图表</span>
                <div class="header-actions">
                  <el-button size="small" @click="zoomIn">
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                  <el-button size="small" @click="zoomOut">
                    <el-icon><ZoomOut /></el-icon>
                  </el-button>
                  <el-button size="small" @click="resetZoom">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
            <div class="workflow-graph" ref="graphContainer">
              <!-- 工作流节点图表 -->
              <div class="graph-content">
                <div 
                  v-for="node in workflow.nodes" 
                  :key="node.id"
                  class="workflow-node"
                  :class="{
                    'node-running': node.status === 'running',
                    'node-success': node.status === 'success',
                    'node-error': node.status === 'error',
                    'node-pending': node.status === 'pending'
                  }"
                  :style="{
                    left: node.x + 'px',
                    top: node.y + 'px'
                  }"
                  @click="selectNode(node)"
                >
                  <div class="node-icon">
                    <el-icon><component :is="getNodeIcon(node.type)" /></el-icon>
                  </div>
                  <div class="node-label">{{ node.name }}</div>
                  <div class="node-status">
                    <el-icon v-if="node.status === 'running'" class="status-icon running"><Loading /></el-icon>
                    <el-icon v-else-if="node.status === 'success'" class="status-icon success"><SuccessFilled /></el-icon>
                    <el-icon v-else-if="node.status === 'error'" class="status-icon error"><CircleCloseFilled /></el-icon>
                    <el-icon v-else class="status-icon pending"><Clock /></el-icon>
                  </div>
                </div>
                <!-- 连接线 -->
                <svg class="connections" :style="{ width: '100%', height: '100%' }">
                  <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                     refX="9" refY="3.5" orient="auto">
                      <polygon points="0 0, 10 3.5, 0 7" fill="#409EFF" />
                    </marker>
                  </defs>
                  <line 
                    v-for="connection in workflow.connections" 
                    :key="connection.id"
                    :x1="getNodePosition(connection.from).x + 50"
                    :y1="getNodePosition(connection.from).y + 25"
                    :x2="getNodePosition(connection.to).x"
                    :y2="getNodePosition(connection.to).y + 25"
                    stroke="#409EFF"
                    stroke-width="2"
                    marker-end="url(#arrowhead)"
                  />
                </svg>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：详情面板 -->
        <el-col :span="8">
          <!-- 工作流基本信息 -->
          <el-card class="info-card">
            <template #header>
              <span>基本信息</span>
            </template>
            <div class="info-content">
              <div class="info-item">
                <label>工作流ID：</label>
                <span>{{ workflow.id }}</span>
              </div>
              <div class="info-item">
                <label>描述：</label>
                <span>{{ workflow.description || '暂无描述' }}</span>
              </div>
              <div class="info-item">
                <label>触发方式：</label>
                <span>{{ workflow.trigger_type }}</span>
              </div>
              <div class="info-item">
                <label>执行次数：</label>
                <span>{{ workflow.execution_count }}</span>
              </div>
              <div class="info-item">
                <label>成功率：</label>
                <span>{{ workflow.success_rate }}%</span>
              </div>
            </div>
          </el-card>

          <!-- 节点详情 -->
          <el-card class="node-detail-card" v-if="selectedNode">
            <template #header>
              <span>节点详情</span>
            </template>
            <div class="node-detail-content">
              <div class="info-item">
                <label>节点名称：</label>
                <span>{{ selectedNode.name }}</span>
              </div>
              <div class="info-item">
                <label>节点类型：</label>
                <span>{{ selectedNode.type }}</span>
              </div>
              <div class="info-item">
                <label>状态：</label>
                <el-tag :type="getStatusType(selectedNode.status)">{{ selectedNode.status }}</el-tag>
              </div>
              <div class="info-item">
                <label>执行时间：</label>
                <span>{{ selectedNode.execution_time || '未执行' }}</span>
              </div>
              <div class="info-item">
                <label>配置：</label>
                <el-button size="small" @click="showNodeConfig">查看配置</el-button>
              </div>
            </div>
          </el-card>

          <!-- 执行历史 -->
          <el-card class="history-card">
            <template #header>
              <span>执行历史</span>
            </template>
            <div class="history-content">
              <el-timeline>
                <el-timeline-item
                  v-for="record in executionHistory"
                  :key="record.id"
                  :timestamp="record.timestamp"
                  :type="getTimelineType(record.status)"
                >
                  <div class="history-item">
                    <div class="history-title">{{ record.title }}</div>
                    <div class="history-desc">{{ record.description }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 节点配置对话框 -->
    <el-dialog
      v-model="nodeConfigVisible"
      title="节点配置"
      width="60%"
    >
      <div class="node-config-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="节点ID">{{ selectedNode?.id }}</el-descriptions-item>
          <el-descriptions-item label="节点名称">{{ selectedNode?.name }}</el-descriptions-item>
          <el-descriptions-item label="节点类型">{{ selectedNode?.type }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ selectedNode?.status }}</el-descriptions-item>
        </el-descriptions>
        <div class="config-json" v-if="selectedNode?.config">
          <h4>配置参数</h4>
          <el-input
            type="textarea"
            :rows="10"
            :value="JSON.stringify(selectedNode.config, null, 2)"
            readonly
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  VideoPlay,
  VideoPause,
  ArrowDown,
  ZoomIn,
  ZoomOut,
  Refresh,
  Loading,
  SuccessFilled,
  CircleCloseFilled,
  Clock,
  User,
  Setting,
  DataAnalysis,
  Connection
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const workflow = reactive({
  id: route.params.id,
  name: '客户数据分析工作流',
  description: '自动分析客户行为数据并生成报告',
  status: '运行中',
  trigger_type: '定时触发',
  execution_count: 156,
  success_rate: 98.5,
  created_at: '2024-01-15 10:30:00',
  updated_at: '2024-01-20 14:20:00',
  nodes: [
    {
      id: 'node1',
      name: '数据收集',
      type: 'data_collector',
      status: 'success',
      x: 50,
      y: 100,
      execution_time: '2.3s',
      config: {
        source: 'database',
        query: 'SELECT * FROM customers',
        timeout: 30
      }
    },
    {
      id: 'node2',
      name: '数据清洗',
      type: 'data_processor',
      status: 'success',
      x: 250,
      y: 100,
      execution_time: '1.8s',
      config: {
        rules: ['remove_duplicates', 'fill_missing'],
        output_format: 'json'
      }
    },
    {
      id: 'node3',
      name: '数据分析',
      type: 'analyzer',
      status: 'running',
      x: 450,
      y: 100,
      execution_time: null,
      config: {
        algorithm: 'clustering',
        parameters: { k: 5 }
      }
    },
    {
      id: 'node4',
      name: '生成报告',
      type: 'reporter',
      status: 'pending',
      x: 650,
      y: 100,
      execution_time: null,
      config: {
        template: 'customer_analysis',
        format: 'pdf'
      }
    }
  ],
  connections: [
    { id: 'conn1', from: 'node1', to: 'node2' },
    { id: 'conn2', from: 'node2', to: 'node3' },
    { id: 'conn3', from: 'node3', to: 'node4' }
  ]
})

const selectedNode = ref(null)
const nodeConfigVisible = ref(false)
const graphContainer = ref(null)

const executionHistory = ref([
  {
    id: 1,
    title: '工作流启动',
    description: '开始执行客户数据分析工作流',
    timestamp: '2024-01-20 14:20:00',
    status: 'success'
  },
  {
    id: 2,
    title: '数据收集完成',
    description: '成功收集到 1,234 条客户记录',
    timestamp: '2024-01-20 14:20:15',
    status: 'success'
  },
  {
    id: 3,
    title: '数据清洗完成',
    description: '清洗完成，有效数据 1,198 条',
    timestamp: '2024-01-20 14:20:30',
    status: 'success'
  },
  {
    id: 4,
    title: '数据分析进行中',
    description: '正在执行聚类分析算法',
    timestamp: '2024-01-20 14:20:45',
    status: 'running'
  }
])

// 计算属性
const getStatusType = (status) => {
  const statusMap = {
    '运行中': 'success',
    'running': 'success',
    '已停止': 'info',
    'stopped': 'info',
    '错误': 'danger',
    'error': 'danger',
    '待执行': 'warning',
    'pending': 'warning',
    'success': 'success'
  }
  return statusMap[status] || 'info'
}

const getTimelineType = (status) => {
  const typeMap = {
    'success': 'success',
    'running': 'primary',
    'error': 'danger',
    'pending': 'warning'
  }
  return typeMap[status] || 'info'
}

const getNodeIcon = (type) => {
  const iconMap = {
    'data_collector': DataAnalysis,
    'data_processor': Setting,
    'analyzer': Connection,
    'reporter': User
  }
  return iconMap[type] || Setting
}

const getNodePosition = (nodeId) => {
  const node = workflow.nodes.find(n => n.id === nodeId)
  return node ? { x: node.x, y: node.y } : { x: 0, y: 0 }
}

// 方法
const editWorkflow = () => {
  router.push(`/workflow-designer/${workflow.id}`)
}

const toggleWorkflow = async () => {
  try {
    const action = workflow.status === '运行中' ? '停止' : '启动'
    await ElMessageBox.confirm(
      `确定要${action}工作流吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用
    workflow.status = workflow.status === '运行中' ? '已停止' : '运行中'
    ElMessage.success(`工作流已${action}`)
  } catch {
    // 用户取消操作
  }
}

const handleCommand = async (command) => {
  switch (command) {
    case 'duplicate':
      ElMessage.success('工作流复制功能开发中')
      break
    case 'export':
      ElMessage.success('配置导出功能开发中')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          '删除后无法恢复，确定要删除此工作流吗？',
          '确认删除',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        ElMessage.success('工作流删除功能开发中')
      } catch {
        // 用户取消删除
      }
      break
  }
}

const selectNode = (node) => {
  selectedNode.value = node
}

const showNodeConfig = () => {
  nodeConfigVisible.value = true
}

const zoomIn = () => {
  ElMessage.info('放大功能开发中')
}

const zoomOut = () => {
  ElMessage.info('缩小功能开发中')
}

const resetZoom = () => {
  ElMessage.info('重置缩放功能开发中')
}

// 生命周期
onMounted(() => {
  // 初始化时选择第一个节点
  if (workflow.nodes.length > 0) {
    selectedNode.value = workflow.nodes[0]
  }
})
</script>

<style scoped>
.workflow-detail {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-btn {
  font-size: 16px;
  color: #409EFF;
}

.workflow-info h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.workflow-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #606266;
}

.meta-item {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.workflow-content {
  gap: 20px;
}

.workflow-graph-card {
  height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflow-graph {
  height: 520px;
  position: relative;
  overflow: auto;
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.graph-content {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 800px;
  min-height: 400px;
}

.workflow-node {
  position: absolute;
  width: 100px;
  height: 50px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-node:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-node.node-running {
  border-color: #409EFF;
  background: #ecf5ff;
}

.workflow-node.node-success {
  border-color: #67c23a;
  background: #f0f9ff;
}

.workflow-node.node-error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.workflow-node.node-pending {
  border-color: #e6a23c;
  background: #fdf6ec;
}

.node-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.node-label {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

.node-status {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 12px;
}

.status-icon.running {
  color: #409EFF;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.error {
  color: #f56c6c;
}

.status-icon.pending {
  color: #e6a23c;
}

.connections {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.info-card,
.node-detail-card,
.history-card {
  margin-bottom: 20px;
}

.info-content,
.node-detail-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 10px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.history-content {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 8px;
}

.history-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.history-desc {
  font-size: 12px;
  color: #909399;
}

.node-config-content {
  padding: 20px 0;
}

.config-json {
  margin-top: 20px;
}

.config-json h4 {
  margin-bottom: 10px;
  color: #303133;
}
</style>