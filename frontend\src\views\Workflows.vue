<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>工作流管理</span>
          <div class="header-actions">
            <el-button @click="refreshWorkflows">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              创建工作流
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="workflows" v-loading="loading" style="width: 100%">
        <el-table-column prop="workflow_id" label="工作流ID" width="200" show-overflow-tooltip />
        <el-table-column prop="name" label="名称" width="200" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column label="步骤数" width="100">
          <template #default="{ row }">
            {{ row.steps?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="标签" width="200">
          <template #default="{ row }">
            <el-tag v-for="tag in row.tags" :key="tag" size="small" style="margin-right: 5px;">
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'info'">
              {{ row.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewWorkflow(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="editWorkflow(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="executeWorkflow(row)">
              执行
            </el-button>
            <el-button type="text" size="small" @click="duplicateWorkflow(row)">
              复制
            </el-button>
            <el-button type="text" size="small" danger @click="deleteWorkflow(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建工作流对话框 -->
    <el-dialog v-model="createDialogVisible" title="创建工作流" width="600px">
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="工作流ID" prop="workflow_id">
          <el-input v-model="createForm.workflow_id" placeholder="请输入工作流ID" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入工作流名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="createForm.description" type="textarea" :rows="3" placeholder="请输入工作流描述" />
        </el-form-item>
        <el-form-item label="标签">
          <el-select v-model="createForm.tags" multiple filterable allow-create placeholder="请选择或输入标签">
            <el-option v-for="tag in commonTags" :key="tag" :label="tag" :value="tag" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板">
          <el-select v-model="createForm.template" placeholder="选择工作流模板（可选）">
            <el-option label="空白工作流" value="" />
            <el-option label="代码生成工作流" value="code_generation" />
            <el-option label="市场调研工作流" value="market_research" />
            <el-option label="产品分析工作流" value="product_analysis" />
            <el-option label="旅游规划工作流" value="travel_planning" />
            <el-option label="数据分析工作流" value="data_analysis" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createWorkflow" :loading="createLoading">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 执行工作流对话框 -->
    <el-dialog v-model="executeDialogVisible" title="执行工作流" width="600px">
      <div v-if="selectedWorkflow">
        <h4>工作流: {{ selectedWorkflow.name }}</h4>
        <p>{{ selectedWorkflow.description }}</p>

        <el-form :model="executeForm" label-width="100px" style="margin-top: 20px;">
          <el-form-item label="输入数据">
            <el-input
              v-model="executeForm.input_data"
              type="textarea"
              :rows="6"
              placeholder="请输入JSON格式的输入数据"
            />
          </el-form-item>
          <el-form-item label="会话ID">
            <el-input v-model="executeForm.session_id" placeholder="可选，留空将自动生成" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="executeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="startExecution" :loading="executeLoading">
          开始执行
        </el-button>
      </template>
    </el-dialog>

    <!-- 工作流详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="工作流详情" width="800px">
      <div v-if="selectedWorkflow">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流ID">
            {{ selectedWorkflow.workflow_id }}
          </el-descriptions-item>
          <el-descriptions-item label="名称">
            {{ selectedWorkflow.name }}
          </el-descriptions-item>
          <el-descriptions-item label="版本">
            {{ selectedWorkflow.version }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedWorkflow.is_active ? 'success' : 'info'">
              {{ selectedWorkflow.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedWorkflow.description }}
          </el-descriptions-item>
        </el-descriptions>

        <h4 style="margin: 20px 0 10px 0;">工作流步骤</h4>
        <el-table :data="selectedWorkflow.steps" style="width: 100%">
          <el-table-column prop="step_id" label="步骤ID" width="150" />
          <el-table-column prop="agent_id" label="智能体" width="150" />
          <el-table-column label="执行模式" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ getExecutionModeText(row.execution_mode) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="condition" label="条件" show-overflow-tooltip />
        </el-table>

        <h4 style="margin: 20px 0 10px 0;">输入输出模式</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <h5>输入模式</h5>
            <pre>{{ JSON.stringify(selectedWorkflow.input_schema, null, 2) }}</pre>
          </el-col>
          <el-col :span="12">
            <h5>输出模式</h5>
            <pre>{{ JSON.stringify(selectedWorkflow.output_schema, null, 2) }}</pre>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { workflowApi } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const workflows = ref([])
const selectedWorkflow = ref(null)

// 对话框状态
const createDialogVisible = ref(false)
const executeDialogVisible = ref(false)
const detailDialogVisible = ref(false)

// 加载状态
const createLoading = ref(false)
const executeLoading = ref(false)

// 表单数据
const createForm = ref({
  workflow_id: '',
  name: '',
  description: '',
  tags: [],
  template: ''
})

const executeForm = ref({
  input_data: '',
  session_id: ''
})

// 表单引用
const createFormRef = ref()

// 表单验证规则
const createRules = {
  workflow_id: [
    { required: true, message: '请输入工作流ID', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '工作流ID只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入工作流名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入工作流描述', trigger: 'blur' }
  ]
}

// 常用标签
const commonTags = [
  '代码生成', '市场调研', '数据分析', '产品分析', '自动化',
  '机器学习', '自然语言处理', '图像处理', '业务流程'
]

// 方法
const loadWorkflows = async () => {
  loading.value = true
  try {
    const response = await workflowApi.getWorkflows()
    workflows.value = response.workflows || []
  } catch (error) {
    console.error('加载工作流列表失败:', error)
    ElMessage.error('加载工作流列表失败')
  } finally {
    loading.value = false
  }
}

const refreshWorkflows = () => {
  loadWorkflows()
}

const showCreateDialog = () => {
  createForm.value = {
    workflow_id: '',
    name: '',
    description: '',
    tags: [],
    template: ''
  }
  createDialogVisible.value = true
}

const createWorkflow = async () => {
  if (!createFormRef.value) return

  try {
    await createFormRef.value.validate()

    createLoading.value = true

    // 构建工作流数据
    const workflowData = {
      workflow_id: createForm.value.workflow_id,
      name: createForm.value.name,
      description: createForm.value.description,
      tags: createForm.value.tags,
      steps: [],
      input_schema: {},
      output_schema: {},
      metadata: {
        template: createForm.value.template,
        created_by: 'user'
      }
    }

    // 如果选择了模板，应用模板配置
    if (createForm.value.template) {
      workflowData.steps = getTemplateSteps(createForm.value.template)
      workflowData.input_schema = getTemplateInputSchema(createForm.value.template)
      workflowData.output_schema = getTemplateOutputSchema(createForm.value.template)
    }

    await workflowApi.createWorkflow(workflowData)

    ElMessage.success('工作流创建成功')
    createDialogVisible.value = false
    loadWorkflows()

  } catch (error) {
    console.error('创建工作流失败:', error)
    ElMessage.error('创建工作流失败')
  } finally {
    createLoading.value = false
  }
}

const viewWorkflow = (workflow) => {
  selectedWorkflow.value = workflow
  detailDialogVisible.value = true
}

const editWorkflow = (workflow) => {
  // 跳转到工作流设计器
  router.push(`/workflow-designer?id=${workflow.workflow_id}`)
}

const executeWorkflow = (workflow) => {
  selectedWorkflow.value = workflow
  executeForm.value = {
    input_data: JSON.stringify(getDefaultInputData(workflow), null, 2),
    session_id: ''
  }
  executeDialogVisible.value = true
}

const startExecution = async () => {
  try {
    executeLoading.value = true

    // 解析输入数据
    let inputData = {}
    if (executeForm.value.input_data.trim()) {
      inputData = JSON.parse(executeForm.value.input_data)
    }

    const executionData = {
      input_data: inputData,
      session_id: executeForm.value.session_id || undefined
    }

    const response = await workflowApi.executeWorkflow(selectedWorkflow.value.workflow_id, executionData)

    ElMessage.success('工作流执行已启动')
    executeDialogVisible.value = false

    // 跳转到会话详情页面
    if (response.session_id) {
      router.push(`/session/${response.session_id}`)
    }

  } catch (error) {
    console.error('执行工作流失败:', error)
    if (error.message.includes('JSON')) {
      ElMessage.error('输入数据格式错误，请检查JSON格式')
    } else {
      ElMessage.error('执行工作流失败')
    }
  } finally {
    executeLoading.value = false
  }
}

const duplicateWorkflow = async (workflow) => {
  try {
    const newWorkflowData = {
      ...workflow,
      workflow_id: `${workflow.workflow_id}_copy_${Date.now()}`,
      name: `${workflow.name} (副本)`,
      metadata: {
        ...workflow.metadata,
        copied_from: workflow.workflow_id,
        created_by: 'user'
      }
    }

    await workflowApi.createWorkflow(newWorkflowData)
    ElMessage.success('工作流复制成功')
    loadWorkflows()

  } catch (error) {
    console.error('复制工作流失败:', error)
    ElMessage.error('复制工作流失败')
  }
}

const deleteWorkflow = async (workflow) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工作流 "${workflow.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await workflowApi.deleteWorkflow(workflow.workflow_id)
    ElMessage.success('工作流删除成功')
    loadWorkflows()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除工作流失败:', error)
      ElMessage.error('删除工作流失败')
    }
  }
}

// 工具方法
const getExecutionModeText = (mode) => {
  const modeMap = {
    sequential: '顺序',
    parallel: '并行',
    loop: '循环',
    branch: '分支'
  }
  return modeMap[mode] || mode
}

const getDefaultInputData = (workflow) => {
  const schema = workflow.input_schema || {}
  const defaultData = {}

  for (const [key, config] of Object.entries(schema)) {
    if (config.default !== undefined) {
      defaultData[key] = config.default
    } else if (config.type === 'string') {
      defaultData[key] = ''
    } else if (config.type === 'array') {
      defaultData[key] = []
    } else if (config.type === 'object') {
      defaultData[key] = {}
    } else {
      defaultData[key] = null
    }
  }

  return defaultData
}

const getTemplateSteps = (template) => {
  // 根据模板返回预定义的步骤
  const templates = {
    code_generation: [
      {
        step_id: 'intent_recognition',
        agent_id: 'intent_recognizer',
        execution_mode: 'sequential'
      },
      {
        step_id: 'task_decomposition',
        agent_id: 'task_decomposer',
        execution_mode: 'sequential'
      },
      {
        step_id: 'code_generation',
        agent_id: 'code_generator',
        execution_mode: 'sequential'
      }
    ],
    market_research: [
      {
        step_id: 'research_planning',
        agent_id: 'task_decomposer',
        execution_mode: 'sequential'
      },
      {
        step_id: 'parallel_research',
        execution_mode: 'parallel',
        parallel_agents: [
          { agent: 'market_researcher' },
          { agent: 'market_researcher' }
        ]
      }
    ]
  }

  return templates[template] || []
}

const getTemplateInputSchema = (template) => {
  const schemas = {
    code_generation: {
      user_request: { type: 'string', required: true, description: '用户需求描述' },
      programming_language: { type: 'string', default: 'Python', description: '编程语言' }
    },
    market_research: {
      research_topic: { type: 'string', required: true, description: '调研主题' },
      research_scope: { type: 'string', default: '全球市场', description: '调研范围' }
    }
  }

  return schemas[template] || {}
}

const getTemplateOutputSchema = (template) => {
  const schemas = {
    code_generation: {
      generated_code: { type: 'string', description: '生成的代码' },
      code_explanation: { type: 'string', description: '代码说明' }
    },
    market_research: {
      final_report: { type: 'string', description: '最终调研报告' },
      market_recommendations: { type: 'array', description: '市场建议' }
    }
  }

  return schemas[template] || {}
}

// 生命周期
onMounted(() => {
  loadWorkflows()
})
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

pre {
  background-color: var(--el-fill-color-lighter);
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.el-table {
  margin-top: 20px;
}

.el-dialog .el-form {
  margin-top: 20px;
}
</style>
