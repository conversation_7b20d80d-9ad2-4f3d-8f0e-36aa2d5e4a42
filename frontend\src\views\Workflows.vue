<template>
  <div class="workflows-container">
    <div class="page-header">
      <h1>工作流管理</h1>
      <p>管理和监控您的工作流程</p>
    </div>
    
    <div class="toolbar">
      <el-button type="primary" @click="createWorkflow">
        <el-icon><Plus /></el-icon>
        创建工作流
      </el-button>
      <el-button @click="refreshWorkflows">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <div class="workflows-grid">
      <el-card 
        v-for="workflow in workflows" 
        :key="workflow.id" 
        class="workflow-card"
        @click="openWorkflow(workflow)"
      >
        <template #header>
          <div class="card-header">
            <span class="workflow-name">{{ workflow.name }}</span>
            <el-tag :type="getStatusType(workflow.status)">{{ workflow.status }}</el-tag>
          </div>
        </template>
        
        <div class="workflow-info">
          <p class="description">{{ workflow.description }}</p>
          <div class="meta-info">
            <span>节点数: {{ workflow.nodeCount }}</span>
            <span>最后运行: {{ formatDate(workflow.lastRun) }}</span>
          </div>
        </div>
        
        <template #footer>
          <div class="card-actions">
            <el-button size="small" @click.stop="editWorkflow(workflow)">
              编辑
            </el-button>
            <el-button size="small" @click.stop="runWorkflow(workflow)">
              运行
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click.stop="deleteWorkflow(workflow)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-card>
    </div>

    <!-- 空状态 -->
    <div v-if="workflows.length === 0" class="empty-state">
      <el-empty description="暂无工作流">
        <el-button type="primary" @click="createWorkflow">
          创建第一个工作流
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'

const router = useRouter()
const workflows = ref([])

// 模拟数据
const mockWorkflows = [
  {
    id: 1,
    name: '数据处理流程',
    description: '自动化数据清洗和分析流程',
    status: 'active',
    nodeCount: 5,
    lastRun: new Date('2024-01-15')
  },
  {
    id: 2,
    name: '报告生成',
    description: '定期生成业务报告',
    status: 'inactive',
    nodeCount: 3,
    lastRun: new Date('2024-01-10')
  }
]

onMounted(() => {
  loadWorkflows()
})

const loadWorkflows = () => {
  // 模拟API调用
  workflows.value = mockWorkflows
}

const refreshWorkflows = () => {
  loadWorkflows()
  ElMessage.success('工作流列表已刷新')
}

const createWorkflow = () => {
  router.push('/workflows/designer')
}

const openWorkflow = (workflow) => {
  router.push(`/workflows/${workflow.id}`)
}

const editWorkflow = (workflow) => {
  router.push(`/workflows/designer?id=${workflow.id}`)
}

const runWorkflow = async (workflow) => {
  try {
    // 模拟运行工作流
    ElMessage.success(`工作流 "${workflow.name}" 开始运行`)
  } catch (error) {
    ElMessage.error('运行工作流失败')
  }
}

const deleteWorkflow = async (workflow) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工作流 "${workflow.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟删除
    const index = workflows.value.findIndex(w => w.id === workflow.id)
    if (index > -1) {
      workflows.value.splice(index, 1)
      ElMessage.success('工作流删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'info',
    error: 'danger',
    running: 'warning'
  }
  return statusMap[status] || 'info'
}

const formatDate = (date) => {
  if (!date) return '从未运行'
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.workflows-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.workflows-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.workflow-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.workflow-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflow-name {
  font-weight: 600;
  font-size: 16px;
}

.workflow-info {
  padding: 16px 0;
}

.description {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  margin-top: 60px;
}
</style>