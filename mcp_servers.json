{"mcpServers": {"filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "."], "env": {"NODE_ENV": "production"}}, "git": {"command": "npx", "args": ["@modelcontextprotocol/server-git", "--repository", "."], "env": {"NODE_ENV": "production"}}, "web_search": {"command": "python", "args": ["-m", "mcp_server_web_search"], "env": {"PYTHONPATH": "./mcp_servers", "SEARCH_ENGINE": "google"}}, "database": {"command": "python", "args": ["-m", "mcp_server_database"], "env": {"PYTHONPATH": "./mcp_servers", "DATABASE_URL": "sqlite:///./data/a2a.db"}}, "code_analysis": {"command": "python", "args": ["-m", "mcp_server_code_analysis"], "env": {"PYTHONPATH": "./mcp_servers"}}}}