#!/usr/bin/env python3
"""
MCP Database Server
提供数据库操作功能的MCP服务器
"""

import asyncio
import json
import sys
import sqlite3
import os
from typing import Any, Dict, List, Optional
from pathlib import Path

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

class DatabaseServer:
    def __init__(self):
        self.server = Server("database")
        self.db_path = os.getenv("DATABASE_URL", "sqlite:///./data/a2a.db").replace("sqlite:///", "")
        self.ensure_database()
        
    def ensure_database(self):
        """确保数据库和表存在"""
        # 确保数据目录存在
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建数据库连接并初始化表
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建工作流表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS workflows (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    version TEXT DEFAULT '1.0.0',
                    steps TEXT,  -- JSON格式的步骤定义
                    input_schema TEXT,  -- JSON格式的输入模式
                    output_schema TEXT,  -- JSON格式的输出模式
                    tags TEXT,  -- JSON格式的标签数组
                    is_active BOOLEAN DEFAULT TRUE,
                    max_iterations INTEGER DEFAULT 3,
                    timeout INTEGER DEFAULT 3600,
                    metadata TEXT,  -- JSON格式的元数据
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建智能体表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS agents (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    type TEXT NOT NULL,
                    capabilities TEXT,  -- JSON格式的能力列表
                    config TEXT,  -- JSON格式的配置
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建会话表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    workflow_id TEXT,
                    workflow_name TEXT,
                    status TEXT DEFAULT 'pending',
                    input_data TEXT,  -- JSON格式的输入数据
                    output_data TEXT,  -- JSON格式的输出数据
                    current_step INTEGER DEFAULT 0,
                    total_steps INTEGER DEFAULT 0,
                    progress REAL DEFAULT 0.0,
                    error_message TEXT,
                    metadata TEXT,  -- JSON格式的元数据
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (workflow_id) REFERENCES workflows (id)
                )
            """)
            
            # 创建任务表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    session_id TEXT,
                    agent_id TEXT,
                    agent_name TEXT,
                    task_name TEXT,
                    task_description TEXT,
                    status TEXT DEFAULT 'pending',
                    input_data TEXT,  -- JSON格式的输入数据
                    output_data TEXT,  -- JSON格式的输出数据
                    progress REAL DEFAULT 0.0,
                    error_message TEXT,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 3,
                    timeout INTEGER DEFAULT 300,
                    metadata TEXT,  -- JSON格式的元数据
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES sessions (id),
                    FOREIGN KEY (agent_id) REFERENCES agents (id)
                )
            """)
            
            # 创建MCP服务器表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS mcp_servers (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    description TEXT,
                    command TEXT,
                    args TEXT,  -- JSON格式的参数数组
                    cwd TEXT,
                    env TEXT,  -- JSON格式的环境变量
                    base_url TEXT,
                    ws_url TEXT,
                    headers TEXT,  -- JSON格式的请求头
                    capabilities TEXT,  -- JSON格式的能力列表
                    status TEXT DEFAULT 'disconnected',
                    is_active BOOLEAN DEFAULT TRUE,
                    auto_start BOOLEAN DEFAULT FALSE,
                    pid INTEGER,
                    last_ping TIMESTAMP,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建系统日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_id TEXT,
                    task_id TEXT,
                    agent_id TEXT,
                    context TEXT,  -- JSON格式的上下文数据
                    FOREIGN KEY (session_id) REFERENCES sessions (id),
                    FOREIGN KEY (task_id) REFERENCES tasks (id),
                    FOREIGN KEY (agent_id) REFERENCES agents (id)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_workflow_id ON sessions (workflow_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_session_id ON tasks (session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_agent_id ON tasks (agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_level ON system_logs (level)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON system_logs (timestamp)")
            
            conn.commit()

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
            else:
                conn.commit()
                return [{"affected_rows": cursor.rowcount, "lastrowid": cursor.lastrowid}]

    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)

    def list_tables(self) -> List[str]:
        """列出所有表"""
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        results = self.execute_query(query)
        return [row['name'] for row in results]

def setup_server():
    """设置MCP服务器"""
    db_server = DatabaseServer()
    
    @db_server.server.list_tools()
    async def list_tools() -> List[Tool]:
        return [
            Tool(
                name="execute_query",
                description="执行SQL查询",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "要执行的SQL查询"
                        },
                        "params": {
                            "type": "array",
                            "description": "查询参数",
                            "items": {"type": "string"},
                            "default": []
                        }
                    },
                    "required": ["query"]
                }
            ),
            Tool(
                name="get_table_schema",
                description="获取表结构信息",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "table_name": {
                            "type": "string",
                            "description": "表名"
                        }
                    },
                    "required": ["table_name"]
                }
            ),
            Tool(
                name="list_tables",
                description="列出所有数据库表",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            )
        ]

    @db_server.server.call_tool()
    async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
        try:
            if name == "execute_query":
                query = arguments.get("query", "")
                params = tuple(arguments.get("params", []))
                
                results = db_server.execute_query(query, params)
                
                response = {
                    "query": query,
                    "results": results,
                    "count": len(results)
                }
                
                return [TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2, default=str)
                )]
                
            elif name == "get_table_schema":
                table_name = arguments.get("table_name", "")
                schema = db_server.get_table_schema(table_name)
                
                response = {
                    "table_name": table_name,
                    "schema": schema
                }
                
                return [TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
                
            elif name == "list_tables":
                tables = db_server.list_tables()
                
                response = {
                    "tables": tables,
                    "count": len(tables)
                }
                
                return [TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
                
            else:
                raise ValueError(f"Unknown tool: {name}")
                
        except Exception as e:
            error_response = {
                "error": str(e),
                "tool": name,
                "arguments": arguments
            }
            
            return [TextContent(
                type="text",
                text=json.dumps(error_response, ensure_ascii=False, indent=2)
            )]

    return db_server.server

async def main():
    """主函数"""
    server = setup_server()
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
