#!/usr/bin/env python3
"""
MCP Web Search Server
提供网络搜索功能的MCP服务器
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional
import aiohttp
import os
from urllib.parse import quote_plus

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# 搜索引擎配置
SEARCH_ENGINES = {
    "google": {
        "url": "https://www.googleapis.com/customsearch/v1",
        "params": {
            "key": os.getenv("GOOGLE_API_KEY"),
            "cx": os.getenv("GOOGLE_SEARCH_ENGINE_ID"),
        }
    },
    "bing": {
        "url": "https://api.bing.microsoft.com/v7.0/search",
        "headers": {
            "Ocp-Apim-Subscription-Key": os.getenv("BING_API_KEY")
        }
    }
}

class WebSearchServer:
    def __init__(self):
        self.server = Server("web-search")
        self.search_engine = os.getenv("SEARCH_ENGINE", "google")
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def search_web(self, query: str, num_results: int = 10) -> List[Dict[str, Any]]:
        """执行网络搜索"""
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        if self.search_engine == "google":
            return await self._search_google(query, num_results)
        elif self.search_engine == "bing":
            return await self._search_bing(query, num_results)
        else:
            raise ValueError(f"Unsupported search engine: {self.search_engine}")

    async def _search_google(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """使用Google Custom Search API搜索"""
        config = SEARCH_ENGINES["google"]
        
        if not config["params"]["key"] or not config["params"]["cx"]:
            # 如果没有API密钥，返回模拟结果
            return self._get_mock_results(query, num_results)
            
        params = {
            **config["params"],
            "q": query,
            "num": min(num_results, 10)
        }
        
        try:
            async with self.session.get(config["url"], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    for item in data.get("items", []):
                        results.append({
                            "title": item.get("title", ""),
                            "url": item.get("link", ""),
                            "snippet": item.get("snippet", ""),
                            "source": "Google"
                        })
                    
                    return results
                else:
                    raise Exception(f"Search API error: {response.status}")
                    
        except Exception as e:
            print(f"Search error: {e}", file=sys.stderr)
            return self._get_mock_results(query, num_results)

    async def _search_bing(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """使用Bing Search API搜索"""
        config = SEARCH_ENGINES["bing"]
        
        if not config["headers"]["Ocp-Apim-Subscription-Key"]:
            return self._get_mock_results(query, num_results)
            
        params = {
            "q": query,
            "count": min(num_results, 50)
        }
        
        try:
            async with self.session.get(
                config["url"], 
                params=params, 
                headers=config["headers"]
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    for item in data.get("webPages", {}).get("value", []):
                        results.append({
                            "title": item.get("name", ""),
                            "url": item.get("url", ""),
                            "snippet": item.get("snippet", ""),
                            "source": "Bing"
                        })
                    
                    return results
                else:
                    raise Exception(f"Search API error: {response.status}")
                    
        except Exception as e:
            print(f"Search error: {e}", file=sys.stderr)
            return self._get_mock_results(query, num_results)

    def _get_mock_results(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """返回模拟搜索结果"""
        mock_results = [
            {
                "title": f"搜索结果 {i+1}: {query}",
                "url": f"https://example.com/result-{i+1}",
                "snippet": f"这是关于 '{query}' 的搜索结果 {i+1}。包含相关信息和详细描述。",
                "source": "Mock"
            }
            for i in range(min(num_results, 5))
        ]
        return mock_results

    async def fetch_content(self, url: str) -> str:
        """获取网页内容"""
        if not self.session:
            raise RuntimeError("Session not initialized")
            
        try:
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    content = await response.text()
                    # 这里可以添加HTML解析逻辑
                    return content[:5000]  # 限制内容长度
                else:
                    return f"Error fetching content: HTTP {response.status}"
        except Exception as e:
            return f"Error fetching content: {str(e)}"

def setup_server():
    """设置MCP服务器"""
    search_server = WebSearchServer()
    
    @search_server.server.list_tools()
    async def list_tools() -> List[Tool]:
        return [
            Tool(
                name="search_web",
                description="在网络上搜索信息",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索查询字符串"
                        },
                        "num_results": {
                            "type": "integer",
                            "description": "返回结果数量",
                            "default": 10,
                            "minimum": 1,
                            "maximum": 50
                        }
                    },
                    "required": ["query"]
                }
            ),
            Tool(
                name="fetch_content",
                description="获取指定URL的网页内容",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "url": {
                            "type": "string",
                            "description": "要获取内容的URL"
                        }
                    },
                    "required": ["url"]
                }
            )
        ]

    @search_server.server.call_tool()
    async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
        async with search_server:
            if name == "search_web":
                query = arguments.get("query", "")
                num_results = arguments.get("num_results", 10)
                
                results = await search_server.search_web(query, num_results)
                
                response = {
                    "query": query,
                    "num_results": len(results),
                    "results": results
                }
                
                return [TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
                
            elif name == "fetch_content":
                url = arguments.get("url", "")
                content = await search_server.fetch_content(url)
                
                return [TextContent(
                    type="text",
                    text=content
                )]
                
            else:
                raise ValueError(f"Unknown tool: {name}")

    return search_server.server

async def main():
    """主函数"""
    server = setup_server()
    
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
