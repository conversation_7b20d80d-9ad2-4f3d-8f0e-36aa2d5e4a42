# A2A多智能体协作系统 - 最小依赖包列表（Python 3.12兼容）
# 使用方法: pip install -r requirements-minimal.txt
# 要求使用Python 3.12+

# ==================== 核心框架 ====================
# FastAPI和Web服务
fastapi==0.108.0
uvicorn[standard]==0.25.0
pydantic==2.5.3
pydantic-settings==2.1.0
websockets==12.0
python-multipart==0.0.6

# ==================== 数据库 ====================
# SQLAlchemy和数据库工具
sqlalchemy==2.0.25
alembic==1.13.1
pymysql==1.1.0

# ==================== 异步和HTTP ====================
# 异步HTTP和文件操作
httpx==0.26.0
aiohttp==3.9.1
aiofiles==23.2.1
requests==2.31.0

# ==================== AI和LLM ====================
# OpenAI生态
openai==1.7.2
tiktoken==0.5.2

# 主流LLM提供商
anthropic==0.8.1
google-generativeai==0.3.2

# 国内LLM提供商
dashscope==1.17.0
zhipuai==2.0.1

# ==================== 数据处理 ====================
# 基础数据处理
pandas==2.1.4
numpy==1.26.2

# ==================== 配置和工具 ====================
# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0
jinja2==3.1.2

# JSON和数据验证
jsonschema==4.21.1

# 日志
loguru==0.7.2

# 系统监控
psutil==5.9.7
watchdog==4.0.0

# ==================== 安全和认证 ====================
# 加密和安全
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# ==================== 时间和日期 ====================
python-dateutil==2.8.2

# ==================== 工作流管理 ====================
statemachine==2.1.2

# ==================== 开发和测试工具 ====================
# 测试框架
pytest==7.4.4
pytest-asyncio==0.23.2

# 代码质量
black==23.12.1
flake8==7.0.0
mypy==1.8.0

# ==================== 终端UI ====================
rich==13.7.0

# ==================== A2A协议实现 ====================
asyncio-mqtt==0.16.1
pydantic-core==2.14.6

# ==================== 可选依赖 ====================
# 任务队列（可选）
# celery==5.3.6
# redis==5.0.1
# aioredis==2.0.1

# MySQL客户端（可选，如果使用MySQL）
# mysqlclient==2.2.4

# 数据可视化（可选）
# matplotlib==3.8.2
# seaborn==0.13.0

# 监控（可选）
# prometheus-client==0.19.0

# 文档生成（可选）
# markdown==3.5.2
# pygments==2.17.2
