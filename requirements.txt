# A2A多智能体协作系统 - 核心依赖包（Python 3.12兼容）
# 使用方法: pip install -r requirements.txt

# ==================== 核心框架 ====================
fastapi==0.108.0
uvicorn[standard]==0.25.0
pydantic==2.5.3

# ==================== 千问Plus API ====================
dashscope==1.17.0

# ==================== HTTP客户端 ====================
httpx==0.26.0
aiohttp==3.9.1

# ==================== 配置和工具 ====================
pyyaml==6.0.1
python-dotenv==1.0.0
python-multipart==0.0.6

# ==================== 日志 ====================
loguru==0.7.2

# ==================== 开发工具（可选） ====================
# 测试框架
pytest==7.4.4
pytest-asyncio==0.23.2

# 代码质量
black==23.12.1
flake8==7.0.0
