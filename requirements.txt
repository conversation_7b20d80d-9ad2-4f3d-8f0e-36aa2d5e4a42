# A2A多智能体协作系统 - Python依赖包列表
# 使用方法: pip install -r requirements.txt
# 建议使用Python 3.9+

# 数据库相关
mysqlclient==2.2.0
sqlalchemy==2.0.23
alembic==1.12.1
pymysql==1.1.0

# FastAPI和Web相关
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
websockets==12.0

# 异步和任务队列
celery==5.3.4
redis==5.0.1

# AI和LLM相关（OpenAI兼容）
openai==1.3.0
anthropic==0.7.0
google-generativeai==0.3.0
tiktoken==0.5.1
transformers==4.35.0

# 国内LLM提供商
dashscope==1.14.1  # 阿里千问
zhipuai==2.0.1     # 智谱AI GLM
openai-python-client==1.3.0  # 兼容深度求索、月之暗面等OpenAI兼容接口

# MCP协议和服务集成
mcp-client==0.1.0
requests==2.31.0
httpx==0.25.0
aiofiles==23.2.1
watchdog==3.0.0
jsonschema==4.20.0
psutil==5.9.6

# 数据处理和分析
pandas==2.1.0
numpy==1.24.0
matplotlib==3.7.0
seaborn==0.12.0

# 其他工具
pyyaml==6.0.1
jinja2==3.1.0
python-dotenv==1.0.0
loguru==0.7.0

# 开发和测试工具
pytest==7.4.0
pytest-asyncio==0.21.0
black==23.0.0
flake8==6.0.0
mypy==1.5.0

# CORS支持
fastapi-cors==0.0.6

# 文件上传支持
python-multipart==0.0.6

# 时间处理
python-dateutil==2.8.2

# 加密和安全
cryptography==41.0.7
passlib==1.7.4
python-jose==3.3.0

# 配置管理
pydantic-settings==2.1.0

# SQLite数据库支持（默认数据库）
# sqlite3是Python内置模块，无需单独安装

# 工作流和状态管理
fsm==0.3.1
statemachine==2.1.2

# 并发和异步增强
aioredis==2.0.1
aiohttp==3.9.0

# 监控和日志增强
prometheus-client==0.19.0
rich==13.7.0

# 文档生成
markdown==3.5.1
pygments==2.17.0