#!/usr/bin/env python3
"""
A2A多智能体协作系统启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 12):
        print(f"❌ 错误: 需要Python 3.12+，当前版本: {sys.version}")
        print("请升级Python版本或使用正确的Python环境")
        sys.exit(1)
    else:
        print(f"✅ Python版本检查通过: {sys.version}")

def check_environment():
    """检查环境配置"""
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  警告: .env文件不存在")
            print(f"请复制 {env_example} 为 .env 并配置相关参数")
            print("特别是阿里千问API密钥 (A2A_QWEN_API_KEY)")
        else:
            print("❌ 错误: 环境配置文件不存在")
        return False
    
    # 检查关键配置
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
        
        qwen_api_key = os.getenv("A2A_QWEN_API_KEY")
        if not qwen_api_key or qwen_api_key == "your_qwen_api_key_here":
            print("❌ 错误: 请在.env文件中配置阿里千问API密钥 (A2A_QWEN_API_KEY)")
            print("获取API密钥: https://dashscope.aliyun.com/")
            return False
        
        print("✅ 环境配置检查通过")
        return True
        
    except ImportError:
        print("⚠️  警告: python-dotenv未安装，跳过环境配置检查")
        return True
    except Exception as e:
        print(f"❌ 环境配置检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        "a2a-sdk",
        "fastapi", 
        "uvicorn",
        "pydantic",
        "openai",
        "dashscope",
        "loguru"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖包检查通过")
    return True

def create_logs_directory():
    """创建日志目录"""
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    print(f"✅ 日志目录已创建: {logs_dir}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 启动A2A多智能体协作系统")
    print("=" * 60)
    print(f"项目目录: {project_root}")
    print(f"Python版本要求: 3.12+")
    print(f"主要LLM: 阿里千问Plus")
    print(f"协议: Google A2A")
    print("=" * 60)
    
    # 检查Python版本
    check_python_version()
    
    # 检查环境配置
    if not check_environment():
        sys.exit(1)
    
    # 检查依赖包
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    create_logs_directory()
    
    print("=" * 60)
    print("🎯 所有检查通过，启动系统...")
    print("=" * 60)
    
    # 启动应用
    try:
        from src.main import main as app_main
        app_main()
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
