#!/usr/bin/env python3
"""
A2A多智能体协作系统启动脚本
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("PYTHONPATH", str(project_root))

# 导入主程序
from src.main import main

if __name__ == "__main__":
    print("=" * 60)
    print("🤖 A2A多智能体协作系统")
    print("基于Google Agent2Agent协议和阿里千问Plus")
    print("=" * 60)
    print()
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 检查千问API密钥
    if not os.getenv("QWEN_API_KEY"):
        print("⚠️  警告: 未设置千问API密钥")
        print("请设置环境变量: export QWEN_API_KEY=your_api_key")
        print("或在.env文件中添加: QWEN_API_KEY=your_api_key")
        print()
    
    try:
        # 运行主程序
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        sys.exit(1)
