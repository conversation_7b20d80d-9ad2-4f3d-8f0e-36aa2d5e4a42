#!/usr/bin/env python3
"""
Python 3.12兼容性检查脚本
检查项目依赖是否与Python 3.12兼容
"""

import sys
import subprocess
import pkg_resources
from typing import List, Dict, Tuple
import requests
import json
from packaging import version


def check_python_version():
    """检查当前Python版本"""
    current_version = sys.version_info
    print(f"当前Python版本: {current_version.major}.{current_version.minor}.{current_version.micro}")
    
    if current_version < (3, 12):
        print("⚠️  警告: 建议使用Python 3.12+")
        return False
    elif current_version >= (3, 13):
        print("⚠️  警告: Python 3.13+可能存在兼容性问题")
        return True
    else:
        print("✅ Python版本符合要求")
        return True


def get_package_info(package_name: str) -> Dict:
    """从PyPI获取包信息"""
    try:
        response = requests.get(f"https://pypi.org/pypi/{package_name}/json", timeout=10)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        print(f"获取{package_name}信息失败: {e}")
    return {}


def check_python312_support(package_name: str, package_version: str = None) -> Tuple[bool, str]:
    """检查包是否支持Python 3.12"""
    package_info = get_package_info(package_name)
    
    if not package_info:
        return False, "无法获取包信息"
    
    # 获取最新版本信息
    latest_version = package_info.get("info", {}).get("version", "")
    classifiers = package_info.get("info", {}).get("classifiers", [])
    
    # 检查Python版本支持
    python_versions = [c for c in classifiers if c.startswith("Programming Language :: Python ::")]
    supports_312 = any("3.12" in v for v in python_versions)
    supports_3x = any("3" in v and not any(f"3.{i}" in v for i in range(0, 12)) for v in python_versions)
    
    if supports_312:
        return True, f"明确支持Python 3.12 (最新版本: {latest_version})"
    elif supports_3x:
        return True, f"支持Python 3.x (最新版本: {latest_version})"
    elif any("3.11" in v for v in python_versions):
        return True, f"支持Python 3.11，可能兼容3.12 (最新版本: {latest_version})"
    else:
        return False, f"不明确支持Python 3.12 (最新版本: {latest_version})"


def parse_requirements_file(file_path: str) -> List[Tuple[str, str]]:
    """解析requirements.txt文件"""
    packages = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 解析包名和版本
                    if '==' in line:
                        package_name, package_version = line.split('==', 1)
                        packages.append((package_name.strip(), package_version.strip()))
                    elif '>=' in line:
                        package_name = line.split('>=')[0].strip()
                        packages.append((package_name, None))
                    elif '[' in line and ']' in line:
                        # 处理带额外依赖的包，如 uvicorn[standard]
                        package_name = line.split('[')[0].strip()
                        if '==' in line:
                            package_version = line.split('==')[1].strip()
                            packages.append((package_name, package_version))
                        else:
                            packages.append((package_name, None))
                    else:
                        packages.append((line.strip(), None))
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
    
    return packages


def main():
    """主函数"""
    print("=" * 60)
    print("Python 3.12兼容性检查")
    print("=" * 60)
    
    # 检查Python版本
    python_ok = check_python_version()
    print()
    
    # 检查requirements.txt中的包
    print("检查requirements.txt中的包...")
    packages = parse_requirements_file("requirements.txt")
    
    if not packages:
        print("未找到requirements.txt或文件为空")
        return
    
    compatible_packages = []
    incompatible_packages = []
    unknown_packages = []
    
    for i, (package_name, package_version) in enumerate(packages, 1):
        print(f"[{i}/{len(packages)}] 检查 {package_name}...", end=" ")
        
        try:
            is_compatible, message = check_python312_support(package_name, package_version)
            
            if is_compatible:
                compatible_packages.append((package_name, message))
                print("✅")
            else:
                incompatible_packages.append((package_name, message))
                print("❌")
        except Exception as e:
            unknown_packages.append((package_name, str(e)))
            print("❓")
    
    # 输出结果
    print("\n" + "=" * 60)
    print("检查结果汇总")
    print("=" * 60)
    
    print(f"✅ 兼容的包: {len(compatible_packages)}")
    print(f"❌ 不兼容的包: {len(incompatible_packages)}")
    print(f"❓ 未知状态的包: {len(unknown_packages)}")
    
    if incompatible_packages:
        print("\n不兼容的包:")
        for package_name, message in incompatible_packages:
            print(f"  - {package_name}: {message}")
    
    if unknown_packages:
        print("\n未知状态的包:")
        for package_name, message in unknown_packages:
            print(f"  - {package_name}: {message}")
    
    # 给出建议
    print("\n" + "=" * 60)
    print("建议")
    print("=" * 60)
    
    if incompatible_packages:
        print("1. 对于不兼容的包，建议:")
        print("   - 查找替代包")
        print("   - 等待包更新支持Python 3.12")
        print("   - 使用虚拟环境隔离")
    
    if unknown_packages:
        print("2. 对于未知状态的包，建议:")
        print("   - 在测试环境中验证")
        print("   - 查看包的官方文档")
        print("   - 考虑降级到Python 3.11")
    
    print("3. 总体建议:")
    print("   - 在生产环境部署前进行充分测试")
    print("   - 使用Docker容器确保环境一致性")
    print("   - 定期更新依赖包版本")


if __name__ == "__main__":
    main()
