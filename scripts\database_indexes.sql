-- A2A多智能体协作系统 - 数据库索引优化脚本
-- 为提升查询性能而设计的索引策略
-- 注意：此脚本适用于MySQL数据库

-- 使用A2A数据库
USE a2a;

-- 注意：如果索引已存在，MySQL会报错但不会影响数据库结构
-- 建议在生产环境中先检查索引是否存在再创建
-- 可以使用以下查询检查索引：
-- SHOW INDEX FROM table_name WHERE Key_name = 'index_name';

-- ================================
-- sessions 表索引
-- ================================
-- 注意：sessions表已有基础索引，这里添加额外的复合索引

-- 复合索引：用户ID + 状态（用于查询用户的特定状态会话）
CREATE INDEX idx_sessions_user_status ON sessions(user_id, status);

-- 复合索引：状态 + 创建时间（用于查询特定状态的最新会话）
CREATE INDEX idx_sessions_status_created ON sessions(status, created_at DESC);

-- 复合索引：工作流名称 + 状态（用于查询特定工作流的会话状态）
CREATE INDEX idx_sessions_workflow_status ON sessions(workflow_name, status);

-- ================================
-- tasks 表索引
-- ================================
-- 注意：tasks表已有基础索引，这里添加额外的复合索引

-- 复合索引：会话ID + 状态（用于查询会话的特定状态任务）
CREATE INDEX idx_tasks_session_status ON tasks(session_id, status);

-- 复合索引：智能体名称 + 状态（用于监控智能体执行）
CREATE INDEX idx_tasks_agent_status ON tasks(agent_name, status);

-- 复合索引：状态 + 创建时间（用于查询特定状态的最新任务）
CREATE INDEX idx_tasks_status_created ON tasks(status, created_at DESC);

-- 复合索引：执行模式 + 状态（用于统计分析）
CREATE INDEX idx_tasks_mode_status ON tasks(execution_mode, status);

-- 复合索引：步骤类型 + 状态（用于工作流分析）
CREATE INDEX idx_tasks_step_status ON tasks(step_type, status);

-- 复合索引：迭代次数 + 状态（用于循环任务分析）
CREATE INDEX idx_tasks_iteration_status ON tasks(iteration_number, status);

-- 时间范围查询索引
CREATE INDEX idx_tasks_time_range ON tasks(started_at, completed_at);

-- ================================
-- agent_messages 表索引
-- ================================
-- 注意：agent_messages表已有基础索引，这里添加额外的复合索引

-- 复合索引：会话ID + 时间戳（用于查询会话的消息历史）
CREATE INDEX idx_agent_messages_session_time ON agent_messages(session_id, timestamp);

-- 复合索引：发送者 + 接收者（用于查询智能体间的通信）
CREATE INDEX idx_agent_messages_sender_receiver ON agent_messages(sender, receiver);

-- 复合索引：消息类型 + 时间戳（用于查询特定类型的最新消息）
CREATE INDEX idx_agent_messages_type_time ON agent_messages(message_type, timestamp DESC);

-- 复合索引：发送者 + 消息类型（用于分析智能体通信模式）
CREATE INDEX idx_agent_messages_sender_type ON agent_messages(sender, message_type);

-- 复合索引：接收者 + 消息类型（用于分析智能体接收模式）
CREATE INDEX idx_agent_messages_receiver_type ON agent_messages(receiver, message_type);

-- ================================
-- workflow_logs 表索引
-- ================================
-- 注意：workflow_logs表已有基础索引，这里添加额外的复合索引

-- 复合索引：会话ID + 日志级别（用于查询会话的特定级别日志）
CREATE INDEX idx_workflow_logs_session_level ON workflow_logs(session_id, level);

-- 复合索引：会话ID + 时间戳（用于查询会话的执行时间线）
CREATE INDEX idx_workflow_logs_session_time ON workflow_logs(session_id, timestamp);

-- 复合索引：日志级别 + 时间戳（用于查询特定级别的最新日志）
CREATE INDEX idx_workflow_logs_level_time ON workflow_logs(level, timestamp DESC);

-- 时间范围查询索引（用于日志清理和归档）
CREATE INDEX idx_workflow_logs_timestamp_only ON workflow_logs(timestamp);

-- ================================
-- mcp_servers 表索引
-- ================================
-- 注意：mcp_servers表已有基础索引，这里添加额外的复合索引

-- 复合索引：类型 + 是否启用（用于查询特定类型的可用服务器）
CREATE INDEX idx_mcp_servers_type_active ON mcp_servers(type, is_active);

-- 复合索引：是否启用 + 创建时间（用于查询最新的可用服务器）
CREATE INDEX idx_mcp_servers_active_created ON mcp_servers(is_active, created_at DESC);

-- ================================
-- agents 表索引
-- ================================
-- 注意：agents表已有基础索引，这里添加额外的复合索引

-- 复合索引：提供商 + 是否启用（用于查询特定提供商的可用智能体）
CREATE INDEX idx_agents_provider_active ON agents(provider, is_active);

-- 复合索引：模型 + 是否启用（用于查询特定模型的可用智能体）
CREATE INDEX idx_agents_model_active ON agents(model, is_active);

-- 复合索引：是否支持函数调用 + 是否启用（用于查询支持函数调用的智能体）
CREATE INDEX idx_agents_function_active ON agents(supports_function_calling, is_active);

-- 复合索引：是否启用 + 创建时间（用于查询最新的可用智能体）
CREATE INDEX idx_agents_active_created ON agents(is_active, created_at DESC);

-- ================================
-- agent_mcp_assignments 表索引
-- ================================
-- 注意：agent_mcp_assignments表已有基础索引，这里添加额外的复合索引

-- 复合索引：智能体ID + 创建时间（用于查询智能体的MCP分配历史）
CREATE INDEX idx_agent_mcp_agent_created ON agent_mcp_assignments(agent_id, created_at DESC);

-- 复合索引：MCP服务器ID + 创建时间（用于查询MCP服务器的分配历史）
CREATE INDEX idx_agent_mcp_server_created ON agent_mcp_assignments(mcp_server_id, created_at DESC);

-- ================================
-- 性能优化建议
-- ================================

-- 1. 定期分析表统计信息（MySQL）
-- ANALYZE TABLE sessions;
-- ANALYZE TABLE tasks;
-- ANALYZE TABLE agent_messages;
-- ANALYZE TABLE workflow_logs;
-- ANALYZE TABLE mcp_servers;
-- ANALYZE TABLE agents;
-- ANALYZE TABLE agent_mcp_assignments;

-- 2. 定期清理过期数据（建议保留最近3个月的数据）
-- DELETE FROM agent_messages WHERE timestamp < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- DELETE FROM workflow_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 3 MONTH);
-- DELETE FROM sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH) AND status = 'completed';

-- 3. 监控索引使用情况
-- 可以使用 EXPLAIN 来检查查询是否使用了合适的索引
-- EXPLAIN SELECT * FROM sessions WHERE user_id = 'user123' AND status = 'active';

-- 4. 考虑分区策略（对于大数据量）
-- 可以按时间对 agent_messages 和 workflow_logs 表进行分区
-- ALTER TABLE agent_messages PARTITION BY RANGE (YEAR(timestamp)) (
--   PARTITION p2024 VALUES LESS THAN (2025),
--   PARTITION p2025 VALUES LESS THAN (2026)
-- );

-- 5. 定期优化表
-- OPTIMIZE TABLE sessions;
-- OPTIMIZE TABLE tasks;
-- OPTIMIZE TABLE agent_messages;
-- OPTIMIZE TABLE workflow_logs;

-- ================================
-- 常用查询优化示例
-- ================================

-- 查询用户的活跃会话（使用 idx_sessions_user_status 索引）
-- SELECT * FROM sessions WHERE user_id = 'user123' AND status = 'active';

-- 查询特定工作流的会话状态（使用 idx_sessions_workflow_status 索引）
-- SELECT * FROM sessions WHERE workflow_name = 'code_generation' AND status = 'running';

-- 查询最近的任务（使用 idx_tasks_status_created 索引）
-- SELECT * FROM tasks WHERE status = 'running' ORDER BY created_at DESC LIMIT 10;

-- 查询智能体的任务执行情况（使用 idx_tasks_agent_status 索引）
-- SELECT agent_name, status, COUNT(*) FROM tasks 
-- WHERE agent_name = 'code_generator' GROUP BY agent_name, status;

-- 查询会话的消息历史（使用 idx_agent_messages_session_time 索引）
-- SELECT * FROM agent_messages WHERE session_id = 'session123' ORDER BY timestamp;

-- 查询智能体间的通信（使用 idx_agent_messages_sender_receiver 索引）
-- SELECT * FROM agent_messages WHERE sender = 'agent1' AND receiver = 'agent2';

-- 查询会话的错误日志（使用 idx_workflow_logs_session_level 索引）
-- SELECT * FROM workflow_logs WHERE session_id = 'session123' AND level = 'ERROR';

-- 查询可用的特定类型MCP服务器（使用 idx_mcp_servers_type_active 索引）
-- SELECT * FROM mcp_servers WHERE type = 'stdio' AND is_active = TRUE;

-- 查询特定提供商的可用智能体（使用 idx_agents_provider_active 索引）
-- SELECT * FROM agents WHERE provider = 'openai' AND is_active = TRUE;

-- 查询智能体的MCP分配（使用 idx_agent_mcp_agent_created 索引）
-- SELECT * FROM agent_mcp_assignments WHERE agent_id = 'agent-003' ORDER BY created_at DESC;

-- ================================
-- 索引维护脚本
-- ================================

-- 查看表的索引信息
-- SHOW INDEX FROM sessions;
-- SHOW INDEX FROM tasks;
-- SHOW INDEX FROM agent_messages;
-- SHOW INDEX FROM workflow_logs;
-- SHOW INDEX FROM mcp_servers;
-- SHOW INDEX FROM agents;
-- SHOW INDEX FROM agent_mcp_assignments;

-- 检查索引使用情况（需要开启慢查询日志）
-- SHOW STATUS LIKE 'Handler_read%';

-- 查看表统计信息
-- SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH 
-- FROM information_schema.TABLES 
-- WHERE TABLE_SCHEMA = 'a2a';

-- 检查未使用的索引（需要performance_schema开启）
-- SELECT OBJECT_SCHEMA, OBJECT_NAME, INDEX_NAME 
-- FROM performance_schema.table_io_waits_summary_by_index_usage 
-- WHERE INDEX_NAME IS NOT NULL 
-- AND COUNT_STAR = 0 
-- AND OBJECT_SCHEMA = 'a2a';

-- 重建索引（如果需要）
-- ALTER TABLE sessions DROP INDEX idx_sessions_user_status, ADD INDEX idx_sessions_user_status (user_id, status);

-- ================================
-- 备注
-- ================================
-- 1. 所有索引都使用 IF NOT EXISTS 子句，避免重复创建
-- 2. 索引命名遵循 idx_表名_字段名 的规范
-- 3. 复合索引的字段顺序经过优化，最常用的查询条件放在前面
-- 4. 时间相关的索引使用 DESC 排序，适合查询最新数据
-- 5. 建议在生产环境中监控索引的使用情况和性能影响
-- 6. 此脚本专为MySQL数据库设计，与init_database.sql中的表结构完全匹配
-- 7. 执行前请确保已经运行了init_database.sql创建基础表结构
-- 8. 建议在非高峰期执行索引创建操作，避免影响业务

-- 执行完成提示
SELECT 'A2A数据库索引优化完成！' AS status;
SELECT 'Please run ANALYZE TABLE on all tables to update statistics.' AS recommendation;