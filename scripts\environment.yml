# A2A多智能体协作系统 Conda环境配置文件
# 使用方法: conda env create -f environment.yml
# 激活环境: conda activate a2a
# 更新环境: conda env update -f environment.yml

name: a2a
channels:
  - conda-forge
  - defaults
  - pytorch
  - nvidia

dependencies:
  # Python版本
  - python=3.12
  
  # 系统依赖
  - pip
  - setuptools
  - wheel
  - git
  
  # 数据库相关
  - sqlite
  - mysql-connector-python
  - pymysql
  - postgresql
  
  # 数据处理和科学计算
  - pandas=2.1.4
  - numpy=1.25.2
  - matplotlib=3.8.2
  - seaborn=0.13.0
  - scipy=1.11.4
  - scikit-learn=1.3.2
  
  # 网络和HTTP
  - requests=2.31.0
  - urllib3=2.1.0
  
  # 系统监控和工具
  - psutil=5.9.6
  - watchdog=3.0.0
  
  # 配置和序列化
  - pyyaml=6.0.1
  - toml=0.10.2
  
  # 时间处理
  - python-dateutil=2.8.2
  
  # 加密和安全
  - cryptography>=41.0.0
  
  # 开发工具
  - ipython=8.18.1
  - jupyter=1.0.0
  - notebook=7.0.6
  
  # 通过pip安装的包
  - pip:
    # ==================== 核心框架 ====================
    # FastAPI和Web服务
    - fastapi==0.108.0
    - uvicorn[standard]==0.25.0
    - pydantic==2.5.3
    - pydantic-settings==2.1.0
    - websockets==12.0
    - python-multipart==0.0.6
    - python-jose[cryptography]==3.3.0
    - passlib[bcrypt]==1.7.4

    # CORS和中间件
    - fastapi-cors==0.0.6
    - starlette==0.28.1
    
    # ==================== 数据库 ====================
    # SQLAlchemy和数据库工具
    - sqlalchemy==2.0.25
    - alembic==1.13.1
    - databases[sqlite,mysql,postgresql]==0.8.0
    - asyncpg==0.29.0
    - aiomysql==0.2.0
    - aiosqlite==0.20.0
    - mysqlclient==2.2.4
    
    # ==================== 异步和任务队列 ====================
    # Redis和异步支持
    - redis==5.0.1
    - aioredis==2.0.1
    - celery==5.3.6
    - kombu==5.3.4

    # 异步HTTP和文件操作
    - httpx==0.26.0
    - aiohttp==3.9.1
    - aiofiles==23.2.1
    - asyncio-mqtt==0.16.1
    
    # ==================== AI和LLM ====================
    # OpenAI生态
    - openai==1.7.2
    - tiktoken==0.5.2

    # 主流LLM提供商
    - anthropic==0.8.1
    - google-generativeai==0.3.2
    - cohere==4.37

    # 国内LLM提供商
    - dashscope==1.17.0        # 阿里千问
    - zhipuai==2.0.1           # 智谱AI GLM
    - volcengine-python-sdk==1.0.92  # 字节豆包
    - qianfan==0.3.5           # 百度千帆
    - minimax==0.1.0           # MiniMax
    
    # Hugging Face生态
    - transformers==4.36.2
    - tokenizers==0.15.0
    - datasets==2.16.1
    - accelerate==0.25.0
    - safetensors==0.4.1
    
    # 向量数据库和嵌入
    - sentence-transformers==2.2.2
    - chromadb==0.4.18
    - faiss-cpu==1.7.4
    - pinecone-client==2.2.4
    - weaviate-client==3.25.3
    
    # ==================== MCP协议和服务集成 ====================
    # MCP协议支持
    - jsonschema==4.21.1
    - jsonrpc-base==2.2.0
    - jsonrpc-websocket==3.1.5
    
    # 外部服务集成
    - slack-sdk==3.26.1
    - discord.py==2.3.2
    - telegram-bot-api==7.0.1
    - github3.py==4.0.1
    - jira==3.5.2
    
    # ==================== 工作流和状态管理 ====================
    # 状态机和工作流
    - statemachine==2.1.2
    - transitions==0.9.0
    - prefect==2.14.21
    - airflow==2.7.3
    
    # 任务调度
    - apscheduler==3.10.4
    - schedule==1.2.0
    - crontab==1.0.1
    
    # ==================== 数据处理和分析 ====================
    # 数据处理增强
    - polars==0.20.2
    - pyarrow==14.0.2
    - openpyxl==3.1.2
    - xlsxwriter==3.1.9
    
    # 图像和文档处理
    - pillow==10.1.0
    - opencv-python==********
    - pypdf2==3.0.1
    - python-docx==1.1.0
    - markdown==3.5.1
    - beautifulsoup4==4.12.2
    
    # ==================== 网络爬虫和搜索 ====================
    # 网络爬虫
    - scrapy==2.11.0
    - selenium==4.16.0
    - playwright==1.40.0
    
    # 搜索引擎集成
    - google-search-results==2.4.2
    - duckduckgo-search==3.9.6
    - wikipedia==1.4.0
    
    # ==================== 监控和日志 ====================
    # 日志和监控
    - loguru==0.7.2
    - structlog==23.2.0
    - prometheus-client==0.19.0
    - grafana-api==1.0.3
    
    # 性能监控
    - py-spy==0.3.14
    - memory-profiler==0.61.0
    - line-profiler==4.1.1
    
    # ==================== 可视化和UI ====================
    # 数据可视化
    - plotly==5.17.0
    - bokeh==3.3.2
    - altair==5.2.0
    - pygments==2.17.2
    
    # 终端UI
    - rich==13.7.0
    - typer==0.9.0
    - click==8.1.7
    - tqdm==4.66.1
    
    # ==================== 模板和配置 ====================
    # 模板引擎
    - jinja2==3.1.2
    - mako==1.3.0
    
    # 配置管理
    - python-dotenv==1.0.0
    - dynaconf==3.2.4
    - hydra-core==1.3.2
    
    # ==================== 测试和开发工具 ====================
    # 测试框架
    - pytest==7.4.4
    - pytest-asyncio==0.23.2
    - pytest-cov==4.1.0
    - pytest-mock==3.12.0
    - pytest-xdist==3.5.0

    # 代码质量
    - black==23.12.1
    - isort==5.13.2
    - flake8==7.0.0
    - mypy==1.8.0
    - bandit==1.7.5
    - pre-commit==3.6.0
    
    # 文档生成
    - sphinx==7.2.6
    - mkdocs==1.5.3
    - mkdocs-material==9.4.14
    
    # ==================== 部署和容器 ====================
    # 容器和部署
    - docker==6.1.3
    - kubernetes==28.1.0
    - gunicorn==21.2.0
    
    # ==================== 安全和认证 ====================
    # JWT和认证
    - pyjwt==2.8.0
    - authlib==1.2.1
    - oauth2lib==0.6.1
    
    # ==================== 实用工具 ====================
    # 通用工具
    - more-itertools==10.1.0
    - toolz==0.12.0
    - funcy==2.0
    - retrying==1.3.4
    - tenacity==8.2.3
    
    # 时间和日期
    - arrow==1.3.0
    - pendulum==2.1.2
    
    # 文件和路径
    - pathlib2==2.3.7
    - send2trash==1.8.2
    
    # ==================== 前端构建工具（可选） ====================
    # Node.js工具（用于前端构建）
    - nodeenv==1.8.0
    
    # ==================== 特定领域工具 ====================
    # 金融数据
    - yfinance==0.2.28
    - pandas-datareader==0.10.0
    
    # 地理信息
    - folium==0.15.1
    - geopandas==0.14.1
    
    # 机器学习增强
    - xgboost==2.0.2
    - lightgbm==4.1.0
    - catboost==1.2.2
    
    # 自然语言处理
    - spacy==3.7.2
    - nltk==3.8.1
    - jieba==0.42.1
    
    # ==================== 实验性和新兴技术 ====================
    # 图数据库
    - neo4j==5.15.0
    - networkx==3.2.1
    
    # 区块链（如果需要）
    - web3==6.12.0
    
    # 量子计算（如果需要）
    - qiskit==0.45.1