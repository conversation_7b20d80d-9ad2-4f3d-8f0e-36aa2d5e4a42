-- A2A系统数据库初始化脚本
-- 创建时间: 2024年
-- 描述: 初始化A2A多智能体协作系统所需的MySQL数据库和表结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS a2a CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE a2a;

-- 会话表
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(36) PRIMARY KEY COMMENT '会话唯一标识符',
    user_id VARCHAR(100) COMMENT '用户ID',
    workflow_name VARCHAR(100) NOT NULL COMMENT '工作流名称',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '会话状态：待处理、运行中、已完成、失败',
    input_data JSON COMMENT '输入数据（JSON格式）',
    output_data JSON COMMENT '输出数据（JSON格式）',
    current_iteration INT DEFAULT 0 COMMENT '当前循环迭代次数',
    max_iterations INT DEFAULT 3 COMMENT '最大循环次数',
    context_size INT DEFAULT 0 COMMENT '当前上下文大小（字符数）',
    context_compressed BOOLEAN DEFAULT FALSE COMMENT '上下文是否已压缩',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT='会话管理表，记录用户会话和工作流执行信息';

-- 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id VARCHAR(36) PRIMARY KEY COMMENT '任务唯一标识符',
    session_id VARCHAR(36) NOT NULL COMMENT '所属会话ID',
    agent_name VARCHAR(100) NOT NULL COMMENT '执行任务的智能体名称',
    task_name VARCHAR(200) COMMENT '任务名称',
    task_description TEXT COMMENT '任务描述',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '任务状态：待处理、运行中、已完成、失败',
    input_data JSON COMMENT '任务输入数据（JSON格式）',
    output_data JSON COMMENT '任务输出数据（JSON格式）',
    error_message TEXT COMMENT '错误信息',
    step_type ENUM('agent', 'branch', 'loop', 'end') DEFAULT 'agent' COMMENT '步骤类型：智能体、分支、循环、结束',
    step_id VARCHAR(100) COMMENT '步骤标识符',
    execution_mode ENUM('sequential', 'parallel') DEFAULT 'sequential' COMMENT '执行模式（顺序/并发）',
    parallel_agents JSON DEFAULT NULL COMMENT '并发执行的智能体列表（JSON格式）',
    aggregation_strategy ENUM('merge_all', 'select_best', 'voting', 'weighted_average') DEFAULT 'merge_all' COMMENT '并发结果聚合策略',
    parallel_timeout INT DEFAULT 60 COMMENT '并发执行超时时间（秒）',
    iteration_number INT DEFAULT 0 COMMENT '迭代次数',
    validation_score DECIMAL(3,2) COMMENT '验证分数（0.00-1.00）',
    validation_feedback TEXT COMMENT '验证反馈信息',
    context_tokens INT DEFAULT 0 COMMENT '上下文令牌数量',
    started_at TIMESTAMP NULL COMMENT '任务开始时间',
    completed_at TIMESTAMP NULL COMMENT '任务完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_agent_name (agent_name),
    INDEX idx_status (status),
    INDEX idx_step_type (step_type),
    INDEX idx_iteration_number (iteration_number)
) COMMENT='任务管理表，记录智能体执行的具体任务信息';

-- 智能体消息表
CREATE TABLE IF NOT EXISTS agent_messages (
    id VARCHAR(36) PRIMARY KEY COMMENT '消息唯一标识符',
    session_id VARCHAR(36) NOT NULL COMMENT '所属会话ID',
    sender VARCHAR(100) NOT NULL COMMENT '发送方智能体名称',
    receiver VARCHAR(100) NOT NULL COMMENT '接收方智能体名称',
    message_type VARCHAR(50) NOT NULL COMMENT '消息类型（如：request、response、notification等）',
    content JSON COMMENT '消息内容（JSON格式）',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间戳',
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_sender (sender),
    INDEX idx_receiver (receiver),
    INDEX idx_timestamp (timestamp)
) COMMENT='智能体消息表，记录智能体之间的通信消息';

-- 工作流执行日志表
CREATE TABLE IF NOT EXISTS workflow_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志记录唯一标识符',
    session_id VARCHAR(36) NOT NULL COMMENT '所属会话ID',
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR') DEFAULT 'INFO' COMMENT '日志级别：调试、信息、警告、错误',
    message TEXT NOT NULL COMMENT '日志消息内容',
    context JSON COMMENT '日志上下文信息（JSON格式）',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '日志记录时间',
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_level (level),
    INDEX idx_timestamp (timestamp)
) COMMENT='工作流执行日志表，记录系统运行过程中的日志信息';

-- MCP服务器配置表
CREATE TABLE IF NOT EXISTS mcp_servers (
    id VARCHAR(36) PRIMARY KEY COMMENT 'MCP服务器唯一标识符',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT 'MCP服务器名称（唯一）',
    description TEXT COMMENT 'MCP服务器描述',
    type ENUM('stdio', 'http', 'websocket') NOT NULL COMMENT 'MCP服务器类型：标准输入输出、HTTP、WebSocket',
    config JSON NOT NULL COMMENT 'MCP服务器配置信息（JSON格式）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用该MCP服务器',
    capabilities JSON COMMENT 'MCP服务器能力列表（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
) COMMENT='MCP服务器配置表，管理Model Context Protocol服务器的配置信息';

-- 智能体配置表
CREATE TABLE IF NOT EXISTS agents (
    id VARCHAR(36) PRIMARY KEY COMMENT '智能体唯一标识符',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '智能体名称（唯一）',
    description TEXT COMMENT '智能体描述',
    provider VARCHAR(50) NOT NULL COMMENT 'LLM提供商（如：openai、qwen、anthropic等）',
    model VARCHAR(100) NOT NULL COMMENT 'LLM模型名称（如：gpt-4、qwen-max等）',
    temperature DECIMAL(3,2) DEFAULT 0.7 COMMENT '模型温度参数（0.0-1.0）',
    max_tokens INT DEFAULT 2048 COMMENT '最大输出令牌数',
    max_context_tokens INT DEFAULT 128000 COMMENT '最大上下文令牌数',
    context_compression_threshold INT DEFAULT 100000 COMMENT '上下文压缩阈值',
    supports_function_calling BOOLEAN DEFAULT FALSE COMMENT '是否支持函数调用',
    system_prompt TEXT COMMENT '系统提示词',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用该智能体',
    config JSON COMMENT '智能体其他配置信息（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name),
    INDEX idx_provider (provider),
    INDEX idx_is_active (is_active)
) COMMENT='智能体配置表，管理系统中各个智能体的配置信息';

-- 智能体MCP分配表
CREATE TABLE IF NOT EXISTS agent_mcp_assignments (
    id VARCHAR(36) PRIMARY KEY COMMENT '分配记录唯一标识符',
    agent_id VARCHAR(36) NOT NULL COMMENT '智能体ID',
    mcp_server_id VARCHAR(36) NOT NULL COMMENT 'MCP服务器ID',
    capabilities JSON COMMENT '分配给智能体的MCP服务器能力列表（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配创建时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (mcp_server_id) REFERENCES mcp_servers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_agent_mcp (agent_id, mcp_server_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_mcp_server_id (mcp_server_id)
) COMMENT='智能体MCP分配表，管理智能体与MCP服务器的关联关系';

-- 插入示例数据
-- 示例MCP服务器配置
INSERT IGNORE INTO mcp_servers (id, name, description, type, config, capabilities) VALUES
('mcp-001', 'filesystem', '文件系统操作服务', 'stdio', 
 JSON_OBJECT(
   'command', 'npx',
   'args', JSON_ARRAY('-y', '@modelcontextprotocol/server-filesystem', '/path/to/allowed/files')
 ), 
 JSON_ARRAY('read_file', 'write_file', 'list_directory')
),
('mcp-002', 'brave-search', '网络搜索服务', 'stdio',
 JSON_OBJECT(
   'command', 'npx',
   'args', JSON_ARRAY('-y', '@modelcontextprotocol/server-brave-search'),
   'env', JSON_OBJECT('BRAVE_API_KEY', '${BRAVE_API_KEY}')
 ),
 JSON_ARRAY('web_search', 'search_results')
),
('mcp-003', 'sqlite', 'SQLite数据库操作', 'stdio',
 JSON_OBJECT(
   'command', 'npx',
   'args', JSON_ARRAY('-y', '@modelcontextprotocol/server-sqlite', '/path/to/database.db')
 ),
 JSON_ARRAY('query', 'execute', 'schema')
),
('mcp-004', 'data-analysis', '数据分析服务', 'stdio',
 JSON_OBJECT(
   'command', 'python',
   'args', JSON_ARRAY('-m', 'mcp_servers.data_analysis')
 ),
 JSON_ARRAY('process_data', 'statistical_analysis', 'trend_analysis')
),
('mcp-005', 'chart-generation', '图表生成服务', 'stdio',
 JSON_OBJECT(
   'command', 'python',
   'args', JSON_ARRAY('-m', 'mcp_servers.chart_generation')
 ),
 JSON_ARRAY('create_chart', 'generate_visualization', 'export_chart')
),
('mcp-006', 'git-operations', 'Git操作服务', 'stdio',
 JSON_OBJECT(
   'command', 'python',
   'args', JSON_ARRAY('-m', 'mcp_servers.git_operations')
 ),
 JSON_ARRAY('clone_repository', 'commit_changes', 'push_changes')
),
('mcp-007', 'map-services', '地图服务', 'http',
 JSON_OBJECT(
   'base_url', 'https://api.mapbox.com',
   'api_key_env', 'MAPBOX_API_KEY'
 ),
 JSON_ARRAY('get_location_info', 'calculate_distance', 'find_nearby_places')
);

-- 示例智能体配置
INSERT IGNORE INTO agents (id, name, description, provider, model, temperature, max_tokens, max_context_tokens, context_compression_threshold, supports_function_calling, system_prompt) VALUES
('agent-001', 'intent_recognizer', '意图识别智能体', 'openai', 'gpt-4', 0.1, 1024, 128000, 100000, FALSE,
 '分析用户输入，识别用户意图。请返回最匹配的意图类型和置信度。'),
('agent-002', 'task_decomposer', '任务分解智能体', 'qwen', 'qwen-max', 0.2, 2048, 128000, 100000, FALSE,
 '根据识别的意图，将任务分解为具体的子任务。请提供详细的任务分解方案。'),
('agent-003', 'code_generator', '代码生成智能体', 'openai', 'gpt-4', 0.3, 4096, 128000, 100000, TRUE,
 '你是一个专业的代码生成专家。请生成高质量的代码。'),
('agent-004', 'market_researcher', '市场调研智能体', 'anthropic', 'claude-3-sonnet-20240229', 0.4, 2048, 200000, 150000, FALSE,
 '你是一个专业的市场调研分析师。请提供详细的市场分析报告。'),
('agent-005', 'result_validator', '结果验证智能体', 'openai', 'gpt-4', 0.2, 2048, 128000, 100000, FALSE,
 '你是一个专业的结果验证专家。请对任务执行结果进行全面评估，包括完整性、准确性、可用性等维度，并提供0-1的分数和改进建议。'),
('agent-006', 'requirement_refiner', '需求优化智能体', 'anthropic', 'claude-3-sonnet-20240229', 0.3, 2048, 128000, 100000, FALSE,
 '你是一个需求分析专家。根据验证反馈，优化和细化用户需求，提供更明确的任务指导。'),
('agent-007', 'product_analyzer', '产品分析智能体', 'deepseek', 'deepseek-chat', 0.4, 2048, 32768, 25000, FALSE,
 '你是一个专业的产品分析师。请对产品进行全面分析，包括功能特性、市场定位、竞品对比、用户体验等维度。'),
('agent-008', 'travel_planner', '旅游规划智能体', 'moonshot', 'moonshot-v1-8k', 0.5, 2048, 8192, 6000, FALSE,
 '你是一个专业的旅游规划师。请根据用户需求制定详细的旅游计划，包括行程安排、景点推荐、住宿建议、交通方案等。'),
('agent-009', 'data_analyst', '数据分析智能体', 'zhipu', 'glm-4', 0.3, 4095, 128000, 100000, TRUE,
 '你是一个专业的数据分析师。请对数据进行深入分析，提供统计分析、趋势预测、可视化建议等专业见解。'),
('agent-010', 'code_reviewer', '代码审查智能体', 'openai', 'gpt-4', 0.2, 3072, 128000, 100000, TRUE,
 '你是一个资深的代码审查专家。请对代码进行全面审查，包括代码规范、安全性、性能优化、可维护性等方面。');

-- 示例智能体MCP分配
INSERT IGNORE INTO agent_mcp_assignments (id, agent_id, mcp_server_id, capabilities) VALUES
('assign-001', 'agent-003', 'mcp-001', JSON_ARRAY('read_file', 'write_file')),
('assign-002', 'agent-004', 'mcp-002', JSON_ARRAY('web_search')),
('assign-003', 'agent-003', 'mcp-003', JSON_ARRAY('query', 'execute')),
('assign-004', 'agent-003', 'mcp-006', JSON_ARRAY('clone_repository', 'commit_changes')),
('assign-005', 'agent-007', 'mcp-002', JSON_ARRAY('web_search', 'search_results')),
('assign-006', 'agent-008', 'mcp-002', JSON_ARRAY('web_search')),
('assign-007', 'agent-008', 'mcp-007', JSON_ARRAY('get_location_info', 'find_nearby_places')),
('assign-008', 'agent-009', 'mcp-004', JSON_ARRAY('process_data', 'statistical_analysis')),
('assign-009', 'agent-009', 'mcp-005', JSON_ARRAY('create_chart', 'generate_visualization')),
('assign-010', 'agent-010', 'mcp-001', JSON_ARRAY('read_file', 'write_file'));

-- 显示创建结果
SHOW TABLES;

SELECT 'A2A数据库初始化完成！' AS status;
SELECT COUNT(*) AS mcp_servers_count FROM mcp_servers;
SELECT COUNT(*) AS agents_count FROM agents;
SELECT COUNT(*) AS assignments_count FROM agent_mcp_assignments;