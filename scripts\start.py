#!/usr/bin/env python3
"""
A2A系统启动脚本
自动启动后端和前端服务
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BACKEND_DIR = PROJECT_ROOT / "src"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

class A2ASystemLauncher:
    """A2A系统启动器"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 12):
            print("❌ 需要Python 3.12或更高版本")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ 需要安装Node.js")
                return False
            print(f"✅ Node.js版本: {result.stdout.strip()}")
        except FileNotFoundError:
            print("❌ 需要安装Node.js")
            return False
        
        # 检查npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ 需要安装npm")
                return False
            print(f"✅ npm版本: {result.stdout.strip()}")
        except FileNotFoundError:
            print("❌ 需要安装npm")
            return False
        
        print("✅ 依赖检查通过")
        return True
    
    def install_backend_dependencies(self):
        """安装后端依赖"""
        print("📦 安装后端依赖...")
        
        # 检查requirements.txt是否存在
        requirements_file = PROJECT_ROOT / "requirements.txt"
        if not requirements_file.exists():
            print("⚠️  requirements.txt不存在，跳过后端依赖安装")
            return True
        
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True, cwd=PROJECT_ROOT)
            print("✅ 后端依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 后端依赖安装失败: {e}")
            return False
    
    def install_frontend_dependencies(self):
        """安装前端依赖"""
        print("📦 安装前端依赖...")
        
        # 检查package.json是否存在
        package_json = FRONTEND_DIR / "package.json"
        if not package_json.exists():
            print("⚠️  frontend/package.json不存在，跳过前端依赖安装")
            return True
        
        try:
            subprocess.run(["npm", "install"], check=True, cwd=FRONTEND_DIR)
            print("✅ 前端依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 前端依赖安装失败: {e}")
            return False
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env["PYTHONPATH"] = str(PROJECT_ROOT)
            
            # 启动后端
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "src.main"
            ], cwd=PROJECT_ROOT, env=env)
            
            # 等待后端启动
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✅ 后端服务启动成功 (http://localhost:8000)")
                return True
            else:
                print("❌ 后端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动后端服务失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🚀 启动前端服务...")
        
        try:
            # 检查是否已安装依赖
            node_modules = FRONTEND_DIR / "node_modules"
            if not node_modules.exists():
                print("⚠️  前端依赖未安装，正在安装...")
                if not self.install_frontend_dependencies():
                    return False
            
            # 启动前端开发服务器
            self.frontend_process = subprocess.Popen([
                "npm", "run", "dev"
            ], cwd=FRONTEND_DIR)
            
            # 等待前端启动
            time.sleep(5)
            
            if self.frontend_process.poll() is None:
                print("✅ 前端服务启动成功 (http://localhost:3000)")
                return True
            else:
                print("❌ 前端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动前端服务失败: {e}")
            return False
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print("\n🛑 收到停止信号，正在关闭服务...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """关闭所有服务"""
        self.running = False
        
        if self.backend_process:
            print("🛑 关闭后端服务...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
        
        if self.frontend_process:
            print("🛑 关闭前端服务...")
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
        
        print("✅ 所有服务已关闭")
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)
            
            # 检查后端进程
            if self.backend_process and self.backend_process.poll() is not None:
                print("❌ 后端服务异常退出")
                self.running = False
                break
            
            # 检查前端进程
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("❌ 前端服务异常退出")
                self.running = False
                break
    
    def run(self):
        """运行系统"""
        print("🎯 A2A多智能体协作系统启动器")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 安装依赖
        if not self.install_backend_dependencies():
            return False
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 启动后端
        if not self.start_backend():
            return False
        
        # 启动前端
        if not self.start_frontend():
            self.shutdown()
            return False
        
        print("\n🎉 A2A系统启动完成!")
        print("📊 系统监控面板: http://localhost:3000")
        print("🔧 API文档: http://localhost:8000/docs")
        print("💡 按 Ctrl+C 停止系统")
        print("=" * 50)
        
        # 监控进程
        monitor_thread = threading.Thread(target=self.monitor_processes)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            # 保持主线程运行
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.shutdown()
        
        return True


def main():
    """主函数"""
    launcher = A2ASystemLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
