"""
Google A2A智能体实现
严格按照Google A2A协议规范
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from abc import ABC, abstractmethod
from loguru import logger
from datetime import datetime

from .protocol import (
    A2AMessage, A2ARequest, A2AResponse, A2ATask, A2ATaskStatus,
    A2AAgentCard, A2ASkill, A2ASkillParameter, A2AInteractionMode,
    A2AMessageType, A2AError
)


class A2AAgent(ABC):
    """Google A2A智能体基类"""
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        version: str = "1.0.0",
        endpoint: Optional[str] = None
    ):
        """初始化A2A智能体"""
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.version = version
        self.endpoint = endpoint
        
        # 技能注册表
        self.skills: Dict[str, A2ASkill] = {}
        self.skill_handlers: Dict[str, Callable] = {}
        
        # 统计信息
        self.statistics = {
            "total_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "last_activity": None,
            "is_active": True
        }
        
        # 注册默认技能
        self._register_default_skills()
        
        logger.info(f"A2A智能体 {self.name} ({self.agent_id}) 初始化完成")
    
    def _register_default_skills(self):
        """注册默认技能"""
        # 健康检查技能
        health_skill = A2ASkill(
            name="health_check",
            description="智能体健康检查",
            parameters=[],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(health_skill, self._handle_health_check)
        
        # 获取技能列表
        list_skills_skill = A2ASkill(
            name="list_skills",
            description="获取智能体技能列表",
            parameters=[],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(list_skills_skill, self._handle_list_skills)
    
    def register_skill(self, skill: A2ASkill, handler: Callable):
        """注册技能"""
        self.skills[skill.name] = skill
        self.skill_handlers[skill.name] = handler
        logger.debug(f"智能体 {self.agent_id} 注册技能: {skill.name}")
    
    async def process_message(self, message: A2AMessage) -> Optional[A2AMessage]:
        """处理A2A消息"""
        try:
            self.statistics["total_requests"] += 1
            self.statistics["last_activity"] = datetime.utcnow().isoformat()
            
            start_time = datetime.utcnow()
            
            if message.message_type == A2AMessageType.REQUEST:
                response = await self._handle_request(message)
            else:
                logger.warning(f"不支持的消息类型: {message.message_type}")
                response = self._create_error_response(
                    message, "UNSUPPORTED_MESSAGE_TYPE", 
                    f"不支持的消息类型: {message.message_type}"
                )
            
            # 更新统计信息
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            self._update_statistics(response_time, response.content.get("status") == "completed")
            
            return response
            
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            self.statistics["failed_requests"] += 1
            return self._create_error_response(message, "INTERNAL_ERROR", str(e))
    
    async def _handle_request(self, message: A2AMessage) -> A2AMessage:
        """处理请求消息"""
        try:
            # 解析请求
            request_data = message.content
            skill_name = request_data.get("skill_name")
            parameters = request_data.get("parameters", {})
            request_id = request_data.get("request_id", message.message_id)
            
            if not skill_name:
                return self._create_error_response(
                    message, "MISSING_SKILL_NAME", "请求中缺少技能名称"
                )
            
            if skill_name not in self.skills:
                return self._create_error_response(
                    message, "SKILL_NOT_FOUND", f"技能 {skill_name} 未找到"
                )
            
            # 执行技能
            handler = self.skill_handlers[skill_name]
            result = await handler(parameters)
            
            # 创建响应
            response = A2AResponse(
                request_id=request_id,
                status=A2ATaskStatus.COMPLETED,
                result=result,
                progress=1.0
            )
            
            return A2AMessage(
                message_type=A2AMessageType.RESPONSE,
                sender=self.agent_id,
                receiver=message.sender,
                correlation_id=message.message_id,
                content=response.dict()
            )
            
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            return self._create_error_response(message, "EXECUTION_ERROR", str(e))
    
    def _create_error_response(self, original_message: A2AMessage, error_code: str, error_message: str) -> A2AMessage:
        """创建错误响应"""
        error = A2AError(
            error_code=error_code,
            error_message=error_message
        )
        
        return A2AMessage(
            message_type=A2AMessageType.ERROR,
            sender=self.agent_id,
            receiver=original_message.sender,
            correlation_id=original_message.message_id,
            content=error.dict()
        )
    
    def _update_statistics(self, response_time: float, success: bool):
        """更新统计信息"""
        if success:
            self.statistics["completed_requests"] += 1
        else:
            self.statistics["failed_requests"] += 1
        
        # 更新平均响应时间
        total_requests = self.statistics["completed_requests"] + self.statistics["failed_requests"]
        if total_requests > 0:
            current_avg = self.statistics["average_response_time"]
            self.statistics["average_response_time"] = (
                (current_avg * (total_requests - 1) + response_time) / total_requests
            )
    
    async def _handle_health_check(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理健康检查"""
        return {
            "status": "healthy",
            "agent_id": self.agent_id,
            "name": self.name,
            "version": self.version,
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": self.statistics
        }
    
    async def _handle_list_skills(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理技能列表请求"""
        skills_list = []
        for skill_name, skill in self.skills.items():
            skills_list.append({
                "name": skill.name,
                "description": skill.description,
                "parameters": [param.dict() for param in skill.parameters],
                "interaction_modes": skill.interaction_modes
            })
        
        return {
            "skills": skills_list,
            "total_skills": len(skills_list)
        }
    
    def get_agent_card(self) -> A2AAgentCard:
        """获取智能体卡片"""
        return A2AAgentCard(
            agent_id=self.agent_id,
            name=self.name,
            description=self.description,
            version=self.version,
            skills=list(self.skills.values()),
            capabilities=list(self.skills.keys()),
            endpoint=self.endpoint,
            metadata={
                "statistics": self.statistics,
                "skill_count": len(self.skills)
            }
        )
    
    @abstractmethod
    async def execute_skill(self, skill_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行技能（抽象方法）"""
        pass


# 便利别名
AgentCard = A2AAgentCard
Skill = A2ASkill
