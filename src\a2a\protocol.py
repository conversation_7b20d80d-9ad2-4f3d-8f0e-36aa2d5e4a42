"""
Google Agent2Agent协议定义
严格按照Google A2A协议规范实现
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
import uuid
from datetime import datetime


class A2ATaskStatus(str, Enum):
    """A2A任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class A2AMessageType(str, Enum):
    """A2A消息类型"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"


class A2AInteractionMode(str, Enum):
    """A2A交互模式"""
    REQUEST_RESPONSE = "request_response"
    STREAMING = "streaming"
    ASYNC = "async"


class A2AMessage(BaseModel):
    """A2A消息基类"""
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    message_type: A2AMessageType
    sender: str
    receiver: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None
    content: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class A2ARequest(BaseModel):
    """A2A请求"""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    skill_name: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    interaction_mode: A2AInteractionMode = A2AInteractionMode.REQUEST_RESPONSE
    timeout: Optional[int] = None
    priority: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)


class A2AResponse(BaseModel):
    """A2A响应"""
    request_id: str
    status: A2ATaskStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: float = 0.0
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class A2ASkillParameter(BaseModel):
    """A2A技能参数定义"""
    name: str
    type: str
    description: str
    required: bool = False
    default: Optional[Any] = None
    enum: Optional[List[Any]] = None


class A2ASkill(BaseModel):
    """A2A技能定义"""
    name: str
    description: str
    parameters: List[A2ASkillParameter] = Field(default_factory=list)
    interaction_modes: List[A2AInteractionMode] = Field(default_factory=lambda: [A2AInteractionMode.REQUEST_RESPONSE])
    examples: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class A2AAgentCard(BaseModel):
    """A2A智能体卡片"""
    agent_id: str
    name: str
    description: str
    version: str = "1.0.0"
    skills: List[A2ASkill] = Field(default_factory=list)
    capabilities: List[str] = Field(default_factory=list)
    endpoint: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class A2ATask(BaseModel):
    """A2A任务"""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    agent_id: str
    skill_name: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    status: A2ATaskStatus = A2ATaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: float = 0.0
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class A2AWorkflowStep(BaseModel):
    """A2A工作流步骤"""
    step_id: str
    agent_id: str
    skill_name: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
    condition: Optional[str] = None
    timeout: Optional[int] = None
    retry_count: int = 0
    on_error: str = "fail"  # fail, continue, retry
    metadata: Dict[str, Any] = Field(default_factory=dict)


class A2AWorkflow(BaseModel):
    """A2A工作流"""
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    steps: List[A2AWorkflowStep] = Field(default_factory=list)
    input_schema: Dict[str, Any] = Field(default_factory=dict)
    output_schema: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class A2ASession(BaseModel):
    """A2A会话"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workflow_id: Optional[str] = None
    user_id: Optional[str] = None
    status: A2ATaskStatus = A2ATaskStatus.PENDING
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Optional[Dict[str, Any]] = None
    context: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class A2AError(BaseModel):
    """A2A错误"""
    error_code: str
    error_message: str
    error_details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
