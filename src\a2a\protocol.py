"""
A2A协议核心数据结构和消息定义
基于Google Agent2Agent协议规范
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime
import uuid


class A2AMessageType(str, Enum):
    """A2A消息类型"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    TASK_UPDATE = "task_update"
    TASK_CANCEL = "task_cancel"
    SKILL_QUERY = "skill_query"
    SKILL_RESPONSE = "skill_response"
    AGENT_DISCOVERY = "agent_discovery"
    AGENT_CARD = "agent_card"
    ERROR = "error"


class A2ATaskStatus(str, Enum):
    """A2A任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class A2AInteractionMode(str, Enum):
    """A2A交互模式"""
    TEXT = "text"
    FORM = "form"
    MEDIA = "media"
    STREAMING = "streaming"


class A2ASkill(BaseModel):
    """A2A技能定义"""
    name: str = Field(description="技能名称")
    description: str = Field(description="技能描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="技能参数")
    interaction_modes: List[A2AInteractionMode] = Field(
        default_factory=lambda: [A2AInteractionMode.TEXT],
        description="支持的交互模式"
    )
    examples: List[Dict[str, Any]] = Field(default_factory=list, description="使用示例")
    
    class Config:
        use_enum_values = True


class A2AAgentCard(BaseModel):
    """A2A智能体卡片"""
    agent_id: str = Field(description="智能体唯一标识")
    name: str = Field(description="智能体名称")
    description: str = Field(description="智能体描述")
    version: str = Field(default="1.0.0", description="智能体版本")
    skills: List[A2ASkill] = Field(description="智能体技能列表")
    endpoint: str = Field(description="智能体服务端点")
    supported_modes: List[A2AInteractionMode] = Field(
        default_factory=lambda: [A2AInteractionMode.TEXT],
        description="支持的交互模式"
    )
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        use_enum_values = True


class A2AMessage(BaseModel):
    """A2A消息"""
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="消息唯一标识")
    message_type: A2AMessageType = Field(description="消息类型")
    sender: str = Field(description="发送方智能体ID")
    receiver: str = Field(description="接收方智能体ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="消息时间戳")
    content: Dict[str, Any] = Field(description="消息内容")
    correlation_id: Optional[str] = Field(default=None, description="关联消息ID")
    
    class Config:
        use_enum_values = True


class A2ATaskRequest(BaseModel):
    """A2A任务请求"""
    task_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="任务唯一标识")
    skill_name: str = Field(description="请求的技能名称")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="任务参数")
    interaction_mode: A2AInteractionMode = Field(
        default=A2AInteractionMode.TEXT,
        description="交互模式"
    )
    priority: int = Field(default=5, ge=1, le=10, description="任务优先级（1-10）")
    timeout: Optional[int] = Field(default=None, description="任务超时时间（秒）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="任务元数据")
    
    class Config:
        use_enum_values = True


class A2ATaskResponse(BaseModel):
    """A2A任务响应"""
    task_id: str = Field(description="任务唯一标识")
    status: A2ATaskStatus = Field(description="任务状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    progress: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="任务进度（0-1）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")
    
    class Config:
        use_enum_values = True


class A2ATaskUpdate(BaseModel):
    """A2A任务更新"""
    task_id: str = Field(description="任务唯一标识")
    status: A2ATaskStatus = Field(description="任务状态")
    progress: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="任务进度（0-1）")
    partial_result: Optional[Dict[str, Any]] = Field(default=None, description="部分结果")
    message: Optional[str] = Field(default=None, description="更新消息")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="更新元数据")
    
    class Config:
        use_enum_values = True


class A2ATask(BaseModel):
    """A2A任务"""
    task_id: str = Field(description="任务唯一标识")
    request: A2ATaskRequest = Field(description="任务请求")
    status: A2ATaskStatus = Field(default=A2ATaskStatus.PENDING, description="任务状态")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="任务进度")
    
    class Config:
        use_enum_values = True


class A2AError(BaseModel):
    """A2A错误"""
    error_code: str = Field(description="错误代码")
    error_message: str = Field(description="错误消息")
    error_details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="错误时间戳")


class A2AProtocol:
    """A2A协议处理器"""
    
    @staticmethod
    def create_task_request_message(
        sender: str,
        receiver: str,
        task_request: A2ATaskRequest
    ) -> A2AMessage:
        """创建任务请求消息"""
        return A2AMessage(
            message_type=A2AMessageType.TASK_REQUEST,
            sender=sender,
            receiver=receiver,
            content=task_request.dict()
        )
    
    @staticmethod
    def create_task_response_message(
        sender: str,
        receiver: str,
        task_response: A2ATaskResponse,
        correlation_id: str
    ) -> A2AMessage:
        """创建任务响应消息"""
        return A2AMessage(
            message_type=A2AMessageType.TASK_RESPONSE,
            sender=sender,
            receiver=receiver,
            content=task_response.dict(),
            correlation_id=correlation_id
        )
    
    @staticmethod
    def create_task_update_message(
        sender: str,
        receiver: str,
        task_update: A2ATaskUpdate,
        correlation_id: str
    ) -> A2AMessage:
        """创建任务更新消息"""
        return A2AMessage(
            message_type=A2AMessageType.TASK_UPDATE,
            sender=sender,
            receiver=receiver,
            content=task_update.dict(),
            correlation_id=correlation_id
        )
    
    @staticmethod
    def create_agent_card_message(
        sender: str,
        receiver: str,
        agent_card: A2AAgentCard
    ) -> A2AMessage:
        """创建智能体卡片消息"""
        return A2AMessage(
            message_type=A2AMessageType.AGENT_CARD,
            sender=sender,
            receiver=receiver,
            content=agent_card.dict()
        )
    
    @staticmethod
    def create_error_message(
        sender: str,
        receiver: str,
        error: A2AError,
        correlation_id: Optional[str] = None
    ) -> A2AMessage:
        """创建错误消息"""
        return A2AMessage(
            message_type=A2AMessageType.ERROR,
            sender=sender,
            receiver=receiver,
            content=error.dict(),
            correlation_id=correlation_id
        )
