"""
A2A服务器实现
基于Google A2A协议的服务器端实现
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from loguru import logger

from ..config.settings import get_settings, get_a2a_config
from ..agents.base_agent import BaseAgent
from .protocol import (
    A2AMessage, A2ATask, A2ATaskRequest, A2ATaskResponse, A2ATaskStatus,
    A2AAgentCard, A2ASkill, A2AProtocol, A2AError, A2AMessageType
)


class A2AServer:
    """A2A协议服务器"""
    
    def __init__(self):
        """初始化A2A服务器"""
        self.settings = get_settings()
        self.a2a_config = get_a2a_config()
        
        # 注册的智能体
        self.agents: Dict[str, BaseAgent] = {}
        
        # 任务管理
        self.active_tasks: Dict[str, A2ATask] = {}
        self.task_history: List[A2ATask] = []
        
        # 消息队列
        self.message_queue: asyncio.Queue = asyncio.Queue()
        
        # 服务器状态
        self.server_id = str(uuid.uuid4())
        self.started_at = datetime.utcnow()
        self.is_running = False
        
        # 创建FastAPI应用
        self.app = self._create_fastapi_app()
        
        logger.info(f"A2A服务器初始化完成，服务器ID: {self.server_id}")
    
    def _create_fastapi_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title=self.a2a_config["server_name"],
            description=self.a2a_config["server_description"],
            version=self.a2a_config["server_version"],
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 添加CORS中间件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 注册路由
        self._register_routes(app)
        
        return app
    
    def _register_routes(self, app: FastAPI):
        """注册A2A协议路由"""
        
        @app.get("/")
        async def root():
            """根路径"""
            return {
                "name": self.a2a_config["server_name"],
                "description": self.a2a_config["server_description"],
                "version": self.a2a_config["server_version"],
                "server_id": self.server_id,
                "status": "running" if self.is_running else "stopped",
                "agents_count": len(self.agents),
                "active_tasks": len(self.active_tasks)
            }
        
        @app.get("/health")
        async def health_check():
            """健康检查"""
            agent_health = {}
            for agent_id, agent in self.agents.items():
                try:
                    health = await agent.health_check()
                    agent_health[agent_id] = health
                except Exception as e:
                    agent_health[agent_id] = {
                        "status": "unhealthy",
                        "error": str(e)
                    }
            
            return {
                "server_status": "healthy" if self.is_running else "stopped",
                "server_id": self.server_id,
                "uptime": (datetime.utcnow() - self.started_at).total_seconds(),
                "agents": agent_health,
                "active_tasks": len(self.active_tasks),
                "total_agents": len(self.agents)
            }
        
        @app.get("/agents")
        async def list_agents():
            """列出所有智能体"""
            agents_info = []
            for agent_id, agent in self.agents.items():
                agent_card = agent.get_agent_card()
                stats = agent.get_statistics()
                agents_info.append({
                    "agent_card": agent_card.dict(),
                    "statistics": stats
                })
            return {"agents": agents_info}
        
        @app.get("/agents/{agent_id}")
        async def get_agent(agent_id: str):
            """获取特定智能体信息"""
            if agent_id not in self.agents:
                raise HTTPException(status_code=404, detail="智能体不存在")
            
            agent = self.agents[agent_id]
            return {
                "agent_card": agent.get_agent_card().dict(),
                "statistics": agent.get_statistics(),
                "health": await agent.health_check()
            }
        
        @app.post("/agents/{agent_id}/tasks")
        async def create_task(agent_id: str, task_request: A2ATaskRequest, background_tasks: BackgroundTasks):
            """为智能体创建任务"""
            if agent_id not in self.agents:
                raise HTTPException(status_code=404, detail="智能体不存在")
            
            agent = self.agents[agent_id]
            
            # 创建任务
            task = A2ATask(
                task_id=task_request.task_id,
                request=task_request,
                status=A2ATaskStatus.PENDING
            )
            
            # 添加到活动任务列表
            self.active_tasks[task.task_id] = task
            
            # 在后台执行任务
            background_tasks.add_task(self._execute_task_background, agent, task)
            
            return {
                "task_id": task.task_id,
                "status": task.status.value,
                "message": "任务已创建并开始执行"
            }
        
        @app.get("/tasks/{task_id}")
        async def get_task_status(task_id: str):
            """获取任务状态"""
            # 先在活动任务中查找
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                return {
                    "task_id": task.task_id,
                    "status": task.status.value,
                    "progress": task.progress,
                    "result": task.result,
                    "error": task.error,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                }
            
            # 在历史任务中查找
            for task in self.task_history:
                if task.task_id == task_id:
                    return {
                        "task_id": task.task_id,
                        "status": task.status.value,
                        "progress": task.progress,
                        "result": task.result,
                        "error": task.error,
                        "created_at": task.created_at.isoformat(),
                        "started_at": task.started_at.isoformat() if task.started_at else None,
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None
                    }
            
            raise HTTPException(status_code=404, detail="任务不存在")
        
        @app.delete("/tasks/{task_id}")
        async def cancel_task(task_id: str):
            """取消任务"""
            if task_id not in self.active_tasks:
                raise HTTPException(status_code=404, detail="任务不存在或已完成")
            
            task = self.active_tasks[task_id]
            task.status = A2ATaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            
            # 移动到历史记录
            del self.active_tasks[task_id]
            self.task_history.append(task)
            
            return {"message": "任务已取消"}
        
        @app.get("/tasks")
        async def list_tasks(status: Optional[str] = None, limit: int = 100):
            """列出任务"""
            all_tasks = list(self.active_tasks.values()) + self.task_history
            
            # 按状态过滤
            if status:
                all_tasks = [t for t in all_tasks if t.status.value == status]
            
            # 按创建时间排序（最新的在前）
            all_tasks.sort(key=lambda t: t.created_at, reverse=True)
            
            # 限制数量
            all_tasks = all_tasks[:limit]
            
            tasks_info = []
            for task in all_tasks:
                tasks_info.append({
                    "task_id": task.task_id,
                    "status": task.status.value,
                    "skill_name": task.request.skill_name,
                    "progress": task.progress,
                    "created_at": task.created_at.isoformat(),
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                })
            
            return {"tasks": tasks_info, "total": len(tasks_info)}
    
    async def _execute_task_background(self, agent: BaseAgent, task: A2ATask):
        """在后台执行任务"""
        try:
            # 执行任务
            response = await agent.execute_task(task.request)
            
            # 更新任务状态
            task.status = response.status
            task.result = response.result
            task.error = response.error
            task.progress = response.progress or 1.0
            task.completed_at = datetime.utcnow()
            
            logger.info(f"任务 {task.task_id} 执行完成，状态: {response.status}")
            
        except Exception as e:
            # 处理异常
            error_msg = f"任务执行失败: {str(e)}"
            task.status = A2ATaskStatus.FAILED
            task.error = error_msg
            task.completed_at = datetime.utcnow()
            
            logger.error(f"任务 {task.task_id} 执行失败: {error_msg}")
        
        finally:
            # 移动到历史记录
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            self.task_history.append(task)
    
    def register_agent(self, agent: BaseAgent):
        """注册智能体"""
        self.agents[agent.agent_id] = agent
        logger.info(f"智能体 {agent.name} ({agent.agent_id}) 已注册到A2A服务器")
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.agents:
            agent = self.agents[agent_id]
            del self.agents[agent_id]
            logger.info(f"智能体 {agent.name} ({agent_id}) 已从A2A服务器注销")
    
    async def start(self):
        """启动服务器"""
        self.is_running = True
        logger.info("A2A服务器已启动")
    
    async def stop(self):
        """停止服务器"""
        self.is_running = False
        
        # 取消所有活动任务
        for task in self.active_tasks.values():
            task.status = A2ATaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            self.task_history.append(task)
        
        self.active_tasks.clear()
        logger.info("A2A服务器已停止")
