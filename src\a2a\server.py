"""
Google A2A服务器实现
提供A2A协议的服务器端功能
"""

import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger
import json
from datetime import datetime

from .protocol import A2AMessage, A2AMessageType, A2ATaskStatus
from .agent import A2AAgent


class A2AServer:
    """A2A协议服务器"""
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        """初始化A2A服务器"""
        self.host = host
        self.port = port
        self.agents: Dict[str, A2AAgent] = {}
        self.is_running = False
        self.server = None
        
        # 消息路由表
        self.message_handlers = {
            A2AMessageType.REQUEST: self._handle_request,
            A2AMessageType.RESPONSE: self._handle_response,
            A2AMessageType.NOTIFICATION: self._handle_notification,
            A2AMessageType.ERROR: self._handle_error
        }
        
        logger.info(f"A2A服务器初始化完成: {host}:{port}")
    
    def register_agent(self, agent: A2AAgent):
        """注册智能体"""
        self.agents[agent.agent_id] = agent
        logger.info(f"注册智能体: {agent.name} ({agent.agent_id})")
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.agents:
            agent = self.agents.pop(agent_id)
            logger.info(f"注销智能体: {agent.name} ({agent_id})")
        else:
            logger.warning(f"尝试注销不存在的智能体: {agent_id}")
    
    async def start(self):
        """启动服务器"""
        try:
            self.is_running = True
            logger.info(f"A2A服务器启动: {self.host}:{self.port}")
            
            # 这里可以添加实际的网络服务器启动逻辑
            # 例如使用 websockets 或 FastAPI
            
            logger.info("A2A服务器启动成功")
            
        except Exception as e:
            logger.error(f"A2A服务器启动失败: {e}")
            self.is_running = False
            raise
    
    async def stop(self):
        """停止服务器"""
        try:
            self.is_running = False
            
            if self.server:
                self.server.close()
                await self.server.wait_closed()
            
            logger.info("A2A服务器已停止")
            
        except Exception as e:
            logger.error(f"A2A服务器停止失败: {e}")
    
    async def route_message(self, message: A2AMessage) -> Optional[A2AMessage]:
        """路由消息"""
        try:
            # 验证消息
            if not self._validate_message(message):
                return self._create_error_message(
                    message, "INVALID_MESSAGE", "消息格式无效"
                )
            
            # 查找目标智能体
            target_agent_id = message.receiver
            if target_agent_id not in self.agents:
                return self._create_error_message(
                    message, "AGENT_NOT_FOUND", f"智能体 {target_agent_id} 未找到"
                )
            
            # 路由到智能体
            target_agent = self.agents[target_agent_id]
            response = await target_agent.process_message(message)
            
            return response
            
        except Exception as e:
            logger.error(f"消息路由失败: {e}")
            return self._create_error_message(
                message, "ROUTING_ERROR", str(e)
            )
    
    async def broadcast_message(self, message: A2AMessage, exclude_agents: List[str] = None) -> List[A2AMessage]:
        """广播消息"""
        exclude_agents = exclude_agents or []
        responses = []
        
        for agent_id, agent in self.agents.items():
            if agent_id not in exclude_agents:
                try:
                    # 创建针对每个智能体的消息副本
                    agent_message = A2AMessage(
                        message_type=message.message_type,
                        sender=message.sender,
                        receiver=agent_id,
                        content=message.content,
                        correlation_id=message.correlation_id
                    )
                    
                    response = await agent.process_message(agent_message)
                    if response:
                        responses.append(response)
                        
                except Exception as e:
                    logger.error(f"广播消息到智能体 {agent_id} 失败: {e}")
        
        return responses
    
    async def execute_agent_task(self, agent_id: str, skill_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行智能体任务"""
        try:
            if agent_id not in self.agents:
                raise ValueError(f"智能体 {agent_id} 未找到")
            
            agent = self.agents[agent_id]
            
            # 创建请求消息
            request_message = A2AMessage(
                message_type=A2AMessageType.REQUEST,
                sender="system",
                receiver=agent_id,
                content={
                    "skill_name": skill_name,
                    "parameters": parameters
                }
            )
            
            # 处理请求
            response_message = await agent.process_message(request_message)
            
            if response_message and response_message.message_type == A2AMessageType.RESPONSE:
                response_content = response_message.content
                if response_content.get("status") == A2ATaskStatus.COMPLETED:
                    return response_content.get("result", {})
                else:
                    raise Exception(response_content.get("error", "任务执行失败"))
            else:
                raise Exception("智能体响应无效")
                
        except Exception as e:
            logger.error(f"执行智能体任务失败 {agent_id}.{skill_name}: {e}")
            raise
    
    def _validate_message(self, message: A2AMessage) -> bool:
        """验证消息格式"""
        try:
            # 检查必需字段
            if not message.message_id or not message.sender or not message.receiver:
                return False
            
            # 检查消息类型
            if message.message_type not in A2AMessageType:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _create_error_message(self, original_message: A2AMessage, error_code: str, error_message: str) -> A2AMessage:
        """创建错误消息"""
        return A2AMessage(
            message_type=A2AMessageType.ERROR,
            sender="server",
            receiver=original_message.sender,
            correlation_id=original_message.message_id,
            content={
                "error_code": error_code,
                "error_message": error_message,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _handle_request(self, message: A2AMessage) -> Optional[A2AMessage]:
        """处理请求消息"""
        return await self.route_message(message)
    
    async def _handle_response(self, message: A2AMessage) -> Optional[A2AMessage]:
        """处理响应消息"""
        # 响应消息通常不需要进一步处理
        logger.debug(f"收到响应消息: {message.message_id}")
        return None
    
    async def _handle_notification(self, message: A2AMessage) -> Optional[A2AMessage]:
        """处理通知消息"""
        logger.info(f"收到通知消息: {message.content}")
        return None
    
    async def _handle_error(self, message: A2AMessage) -> Optional[A2AMessage]:
        """处理错误消息"""
        logger.error(f"收到错误消息: {message.content}")
        return None
    
    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        return {
            "is_running": self.is_running,
            "host": self.host,
            "port": self.port,
            "registered_agents": len(self.agents),
            "agents": [
                {
                    "agent_id": agent.agent_id,
                    "name": agent.name,
                    "skills_count": len(agent.skills),
                    "statistics": agent.statistics
                }
                for agent in self.agents.values()
            ],
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def get_agent_list(self) -> List[Dict[str, Any]]:
        """获取智能体列表"""
        return [agent.get_agent_card().dict() for agent in self.agents.values()]
