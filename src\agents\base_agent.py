"""
基础智能体类
定义智能体的基本接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import asyncio
import time
from datetime import datetime
from loguru import logger

from ..config.agent_config import AgentConfig, LLMConfig
from ..a2a.protocol import (
    A2ATask, A2ATaskRequest, A2ATaskResponse, A2ATaskStatus,
    A2ASkill, A2AAgentCard, A2AInteractionMode
)


class BaseAgent(ABC):
    """基础智能体抽象类"""
    
    def __init__(self, config: AgentConfig):
        """初始化智能体"""
        self.config = config
        self.agent_id = config.id
        self.name = config.name
        self.description = config.description
        self.llm_config = config.llm
        self.system_prompt = config.system_prompt
        self.capabilities = config.capabilities
        self.mcp_services = config.mcp_services
        self.a2a_skills = config.a2a_skills
        self.is_active = config.is_active
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay
        
        # 运行时状态
        self.running_tasks: Dict[str, A2ATask] = {}
        self.task_history: List[A2ATask] = []
        self.last_activity = datetime.utcnow()
        
        logger.info(f"智能体 {self.name} ({self.agent_id}) 初始化完成")
    
    @abstractmethod
    async def process_task(self, task: A2ATask) -> A2ATaskResponse:
        """处理A2A任务（抽象方法）"""
        pass
    
    @abstractmethod
    async def call_llm(self, prompt: str, **kwargs) -> str:
        """调用LLM生成响应（抽象方法）"""
        pass
    
    async def execute_task(self, task_request: A2ATaskRequest) -> A2ATaskResponse:
        """执行任务的主要入口点"""
        task = A2ATask(
            task_id=task_request.task_id,
            request=task_request,
            status=A2ATaskStatus.PENDING
        )
        
        # 添加到运行任务列表
        self.running_tasks[task.task_id] = task
        
        try:
            # 更新任务状态为运行中
            task.status = A2ATaskStatus.RUNNING
            task.started_at = datetime.utcnow()
            
            logger.info(f"智能体 {self.name} 开始执行任务 {task.task_id}")
            
            # 处理任务
            response = await self.process_task(task)
            
            # 更新任务状态
            task.status = response.status
            task.result = response.result
            task.error = response.error
            task.progress = response.progress or 1.0
            task.completed_at = datetime.utcnow()
            
            logger.info(f"智能体 {self.name} 完成任务 {task.task_id}，状态: {response.status}")
            
            return response
            
        except Exception as e:
            # 处理异常
            error_msg = f"任务执行失败: {str(e)}"
            logger.error(f"智能体 {self.name} 任务 {task.task_id} 执行失败: {error_msg}")
            
            task.status = A2ATaskStatus.FAILED
            task.error = error_msg
            task.completed_at = datetime.utcnow()
            
            return A2ATaskResponse(
                task_id=task.task_id,
                status=A2ATaskStatus.FAILED,
                error=error_msg
            )
        
        finally:
            # 从运行任务列表移除，添加到历史记录
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]
            self.task_history.append(task)
            self.last_activity = datetime.utcnow()
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.status = A2ATaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            
            del self.running_tasks[task_id]
            self.task_history.append(task)
            
            logger.info(f"智能体 {self.name} 取消任务 {task_id}")
            return True
        
        return False
    
    def get_task_status(self, task_id: str) -> Optional[A2ATaskStatus]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return self.running_tasks[task_id].status
        
        # 在历史记录中查找
        for task in self.task_history:
            if task.task_id == task_id:
                return task.status
        
        return None
    
    def get_agent_card(self) -> A2AAgentCard:
        """获取智能体卡片"""
        skills = []
        for skill_name in self.a2a_skills:
            skill = A2ASkill(
                name=skill_name,
                description=f"{self.name}提供的{skill_name}技能",
                parameters={},
                interaction_modes=[A2AInteractionMode.TEXT]
            )
            skills.append(skill)
        
        return A2AAgentCard(
            agent_id=self.agent_id,
            name=self.name,
            description=self.description,
            skills=skills,
            endpoint=f"/agents/{self.agent_id}",
            supported_modes=[A2AInteractionMode.TEXT],
            metadata={
                "capabilities": self.capabilities,
                "mcp_services": self.mcp_services,
                "llm_provider": self.llm_config.provider,
                "llm_model": self.llm_config.model
            }
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取智能体统计信息"""
        total_tasks = len(self.task_history) + len(self.running_tasks)
        completed_tasks = len([t for t in self.task_history if t.status == A2ATaskStatus.COMPLETED])
        failed_tasks = len([t for t in self.task_history if t.status == A2ATaskStatus.FAILED])
        
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "is_active": self.is_active,
            "running_tasks": len(self.running_tasks),
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": completed_tasks / total_tasks if total_tasks > 0 else 0,
            "last_activity": self.last_activity.isoformat()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 简单的LLM调用测试
            test_response = await self.call_llm("请回复'健康检查通过'")
            
            return {
                "status": "healthy",
                "agent_id": self.agent_id,
                "name": self.name,
                "llm_status": "ok" if test_response else "error",
                "running_tasks": len(self.running_tasks),
                "last_activity": self.last_activity.isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "agent_id": self.agent_id,
                "name": self.name,
                "error": str(e),
                "running_tasks": len(self.running_tasks),
                "last_activity": self.last_activity.isoformat()
            }
    
    def __str__(self) -> str:
        return f"Agent({self.name}, {self.agent_id})"
    
    def __repr__(self) -> str:
        return f"Agent(name='{self.name}', id='{self.agent_id}', active={self.is_active})"
