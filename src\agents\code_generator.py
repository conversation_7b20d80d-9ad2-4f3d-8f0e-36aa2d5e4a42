"""
代码生成智能体
基于千问Plus的代码生成专用智能体
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..a2a.protocol import A2ASkill, A2ASkillParameter, A2AInteractionMode


class CodeGeneratorAgent(QwenAgent):
    """代码生成智能体"""
    
    def __init__(self, api_key: str):
        """初始化代码生成智能体"""
        system_prompt = """你是一个专业的代码生成智能体。你精通多种编程语言，能够根据需求生成高质量的代码。

你的专长包括：
1. Python、JavaScript、Java、C++、Go等主流编程语言
2. Web开发、数据分析、机器学习、系统编程等领域
3. 代码优化、最佳实践、错误处理
4. 代码注释和文档编写

请始终提供：
- 完整可运行的代码
- 详细的代码注释
- 使用说明和示例
- 可能的改进建议"""
        
        super().__init__(
            agent_id="code_generator",
            name="代码生成智能体",
            description="专门用于生成高质量代码的智能体",
            api_key=api_key,
            system_prompt=system_prompt,
            temperature=0.3  # 较低温度确保代码质量
        )
        
        # 注册专门技能
        self._register_code_skills()
    
    def _register_code_skills(self):
        """注册代码生成技能"""
        # 代码生成技能
        generate_code_skill = A2ASkill(
            name="generate_code",
            description="根据需求生成代码",
            parameters=[
                A2ASkillParameter(
                    name="task_description",
                    type="string",
                    description="任务描述",
                    required=True
                ),
                A2ASkillParameter(
                    name="language",
                    type="string",
                    description="编程语言",
                    required=False,
                    default="Python"
                ),
                A2ASkillParameter(
                    name="framework",
                    type="string",
                    description="使用的框架",
                    required=False
                ),
                A2ASkillParameter(
                    name="requirements",
                    type="array",
                    description="具体要求列表",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(generate_code_skill, self._handle_generate_code)
        
        # 代码优化技能
        optimize_code_skill = A2ASkill(
            name="optimize_code",
            description="优化现有代码",
            parameters=[
                A2ASkillParameter(
                    name="code",
                    type="string",
                    description="待优化的代码",
                    required=True
                ),
                A2ASkillParameter(
                    name="optimization_goals",
                    type="array",
                    description="优化目标",
                    required=False,
                    default=["性能", "可读性", "安全性"]
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(optimize_code_skill, self._handle_optimize_code)
        
        # 代码审查技能
        review_code_skill = A2ASkill(
            name="review_code",
            description="审查代码质量",
            parameters=[
                A2ASkillParameter(
                    name="code",
                    type="string",
                    description="待审查的代码",
                    required=True
                ),
                A2ASkillParameter(
                    name="review_criteria",
                    type="array",
                    description="审查标准",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(review_code_skill, self._handle_review_code)
    
    async def _handle_generate_code(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理代码生成"""
        try:
            task_description = parameters.get("task_description", "")
            language = parameters.get("language", "Python")
            framework = parameters.get("framework", "")
            requirements = parameters.get("requirements", [])
            
            if not task_description:
                raise ValueError("任务描述不能为空")
            
            # 构建提示词
            prompt = f"""请根据以下需求生成{language}代码：

任务描述: {task_description}
编程语言: {language}"""
            
            if framework:
                prompt += f"\n使用框架: {framework}"
            
            if requirements:
                prompt += f"\n具体要求:\n" + "\n".join(f"- {req}" for req in requirements)
            
            prompt += """

请提供：
1. 完整的可运行代码
2. 详细的代码注释
3. 使用说明
4. 示例输入输出（如适用）
5. 依赖包列表（如需要）

请确保代码遵循最佳实践，包含适当的错误处理。"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 提取代码块
            code_blocks = self._extract_code_blocks(response)
            
            return {
                "generated_code": code_blocks.get("main_code", response),
                "language": language,
                "framework": framework,
                "explanation": response,
                "code_blocks": code_blocks,
                "task_description": task_description
            }
            
        except Exception as e:
            logger.error(f"代码生成失败: {e}")
            return {
                "generated_code": "",
                "language": language,
                "explanation": f"代码生成失败: {str(e)}",
                "error": str(e)
            }
    
    async def _handle_optimize_code(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理代码优化"""
        try:
            code = parameters.get("code", "")
            optimization_goals = parameters.get("optimization_goals", ["性能", "可读性", "安全性"])
            
            if not code:
                raise ValueError("待优化代码不能为空")
            
            # 构建提示词
            prompt = f"""请优化以下代码：

原始代码:
```
{code}
```

优化目标: {', '.join(optimization_goals)}

请提供：
1. 优化后的代码
2. 优化说明
3. 性能改进点
4. 潜在风险提醒"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 提取优化后的代码
            code_blocks = self._extract_code_blocks(response)
            
            return {
                "optimized_code": code_blocks.get("main_code", ""),
                "original_code": code,
                "optimization_goals": optimization_goals,
                "optimization_explanation": response,
                "improvements": self._extract_improvements(response)
            }
            
        except Exception as e:
            logger.error(f"代码优化失败: {e}")
            return {
                "optimized_code": code,
                "original_code": code,
                "optimization_explanation": f"代码优化失败: {str(e)}",
                "error": str(e)
            }
    
    async def _handle_review_code(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理代码审查"""
        try:
            code = parameters.get("code", "")
            review_criteria = parameters.get("review_criteria", [
                "代码规范", "性能", "安全性", "可维护性", "错误处理"
            ])
            
            if not code:
                raise ValueError("待审查代码不能为空")
            
            # 构建提示词
            prompt = f"""请审查以下代码：

代码:
```
{code}
```

审查标准: {', '.join(review_criteria)}

请提供详细的审查报告，包括：
1. 代码质量评分（1-10分）
2. 发现的问题和建议
3. 优点和亮点
4. 改进建议
5. 安全性评估"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            return {
                "review_report": response,
                "reviewed_code": code,
                "review_criteria": review_criteria,
                "issues": self._extract_issues(response),
                "suggestions": self._extract_suggestions(response)
            }
            
        except Exception as e:
            logger.error(f"代码审查失败: {e}")
            return {
                "review_report": f"代码审查失败: {str(e)}",
                "reviewed_code": code,
                "error": str(e)
            }
    
    def _extract_code_blocks(self, text: str) -> Dict[str, str]:
        """从文本中提取代码块"""
        import re
        
        # 匹配代码块
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        matches = re.findall(code_pattern, text, re.DOTALL)
        
        code_blocks = {}
        main_code = ""
        
        for i, (lang, code) in enumerate(matches):
            if i == 0:  # 第一个代码块作为主代码
                main_code = code.strip()
            code_blocks[f"block_{i+1}"] = {
                "language": lang,
                "code": code.strip()
            }
        
        code_blocks["main_code"] = main_code
        return code_blocks
    
    def _extract_improvements(self, text: str) -> List[str]:
        """从文本中提取改进点"""
        improvements = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['优化', '改进', '提升', '增强']):
                if line and not line.startswith('#'):
                    improvements.append(line)
        
        return improvements[:5]  # 最多返回5个改进点
    
    def _extract_issues(self, text: str) -> List[str]:
        """从文本中提取问题"""
        issues = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['问题', '错误', '风险', '缺陷']):
                if line and not line.startswith('#'):
                    issues.append(line)
        
        return issues[:5]  # 最多返回5个问题
    
    def _extract_suggestions(self, text: str) -> List[str]:
        """从文本中提取建议"""
        suggestions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['建议', '推荐', '应该', '可以']):
                if line and not line.startswith('#'):
                    suggestions.append(line)
        
        return suggestions[:5]  # 最多返回5个建议
