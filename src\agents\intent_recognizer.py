"""
意图识别智能体
基于阿里千问实现用户意图识别
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..config.agent_config import AgentConfig, create_default_qwen_agent_config
from ..a2a.protocol import A2ATask, A2ATaskResponse, A2ATaskStatus


class IntentRecognizerAgent(QwenAgent):
    """意图识别智能体"""
    
    def __init__(self, config: AgentConfig):
        """初始化意图识别智能体"""
        super().__init__(config)
        
        # 预定义的意图类型
        self.intent_types = [
            "generate_code",      # 代码生成
            "market_research",    # 市场调研
            "product_analysis",   # 产品分析
            "travel_planning",    # 旅游规划
            "data_analysis",      # 数据分析
            "text_generation",    # 文本生成
            "question_answering", # 问答
            "translation",        # 翻译
            "summarization",      # 摘要
            "other"              # 其他
        ]
        
        logger.info(f"意图识别智能体 {self.name} 初始化完成，支持 {len(self.intent_types)} 种意图类型")
    
    def _build_task_prompt(self, skill_name: str, parameters: Dict[str, Any]) -> str:
        """构建意图识别任务提示词"""
        user_input = parameters.get("user_input", "")
        intent_types = parameters.get("intent_types", self.intent_types)
        
        prompt = f"""
你是一个专业的意图识别专家。请分析用户输入，识别用户的真实意图。

用户输入：
{user_input}

可能的意图类型：
{', '.join(intent_types)}

请分析用户输入并返回以下JSON格式的结果：
{{
    "intent": "识别出的意图类型",
    "confidence": 0.95,
    "reasoning": "识别理由和分析过程",
    "keywords": ["关键词1", "关键词2"],
    "parameters": {{
        "extracted_param1": "提取的参数值1",
        "extracted_param2": "提取的参数值2"
    }}
}}

分析要求：
1. 仔细分析用户输入的语义和上下文
2. 选择最匹配的意图类型
3. 提供置信度评分（0-1之间）
4. 说明识别理由
5. 提取关键词和相关参数
6. 如果无法明确识别，选择"other"并说明原因
"""
        return prompt
    
    def _parse_result(self, result_text: str, skill_name: str) -> Dict[str, Any]:
        """解析意图识别结果"""
        try:
            # 尝试解析JSON结果
            if result_text.strip().startswith('{'):
                result = json.loads(result_text)
                
                # 验证必要字段
                if "intent" not in result:
                    result["intent"] = "other"
                if "confidence" not in result:
                    result["confidence"] = 0.5
                if "reasoning" not in result:
                    result["reasoning"] = "未提供识别理由"
                if "keywords" not in result:
                    result["keywords"] = []
                if "parameters" not in result:
                    result["parameters"] = {}
                
                # 确保置信度在有效范围内
                result["confidence"] = max(0.0, min(1.0, float(result["confidence"])))
                
                # 确保意图类型有效
                if result["intent"] not in self.intent_types:
                    logger.warning(f"识别出未知意图类型: {result['intent']}，设置为other")
                    result["intent"] = "other"
                
                return result
            else:
                # 如果不是JSON格式，尝试从文本中提取信息
                return self._extract_intent_from_text(result_text)
                
        except json.JSONDecodeError as e:
            logger.warning(f"意图识别结果JSON解析失败: {e}，尝试文本解析")
            return self._extract_intent_from_text(result_text)
        except Exception as e:
            logger.error(f"意图识别结果解析失败: {e}")
            return {
                "intent": "other",
                "confidence": 0.1,
                "reasoning": f"结果解析失败: {str(e)}",
                "keywords": [],
                "parameters": {},
                "raw_output": result_text
            }
    
    def _extract_intent_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取意图信息"""
        text_lower = text.lower()
        
        # 简单的关键词匹配
        intent_keywords = {
            "generate_code": ["代码", "编程", "程序", "开发", "code", "programming"],
            "market_research": ["市场", "调研", "分析", "竞争", "market", "research"],
            "product_analysis": ["产品", "分析", "对比", "评估", "product", "analysis"],
            "travel_planning": ["旅游", "旅行", "规划", "行程", "travel", "trip"],
            "data_analysis": ["数据", "统计", "分析", "图表", "data", "statistics"],
            "text_generation": ["生成", "写作", "创作", "文本", "generate", "write"],
            "question_answering": ["问题", "回答", "解答", "咨询", "question", "answer"],
            "translation": ["翻译", "转换", "translate", "translation"],
            "summarization": ["摘要", "总结", "概括", "summary", "summarize"]
        }
        
        best_intent = "other"
        best_score = 0
        matched_keywords = []
        
        for intent, keywords in intent_keywords.items():
            score = 0
            intent_keywords_found = []
            for keyword in keywords:
                if keyword in text_lower:
                    score += 1
                    intent_keywords_found.append(keyword)
            
            if score > best_score:
                best_score = score
                best_intent = intent
                matched_keywords = intent_keywords_found
        
        confidence = min(0.8, best_score * 0.2) if best_score > 0 else 0.1
        
        return {
            "intent": best_intent,
            "confidence": confidence,
            "reasoning": f"基于关键词匹配识别，匹配到 {best_score} 个关键词",
            "keywords": matched_keywords,
            "parameters": {},
            "raw_output": text
        }


def create_intent_recognizer_agent(api_key: str) -> IntentRecognizerAgent:
    """创建意图识别智能体"""
    config = create_default_qwen_agent_config(
        agent_id="intent_recognizer",
        name="意图识别智能体",
        description="专业的用户意图识别智能体，能够准确识别用户输入的意图类型",
        system_prompt="""你是一个专业的意图识别专家。你的任务是分析用户输入，准确识别用户的真实意图。

你需要：
1. 仔细分析用户输入的语义和上下文
2. 从预定义的意图类型中选择最匹配的类型
3. 提供准确的置信度评分
4. 说明识别的理由和分析过程
5. 提取相关的关键词和参数

请始终以JSON格式返回结果，确保结果准确、完整、有用。""",
        api_key=api_key,
        capabilities=["intent_recognition", "text_analysis", "semantic_understanding"],
        a2a_skills=["recognize_intent", "analyze_text", "extract_keywords"]
    )
    
    return IntentRecognizerAgent(config)
