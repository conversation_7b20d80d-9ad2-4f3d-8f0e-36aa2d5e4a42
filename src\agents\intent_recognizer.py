"""
意图识别智能体
基于千问Plus的意图识别专用智能体
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..a2a.protocol import A2ASkill, A2ASkillParameter, A2AInteractionMode


class IntentRecognizerAgent(QwenAgent):
    """意图识别智能体"""
    
    def __init__(self, api_key: str):
        """初始化意图识别智能体"""
        system_prompt = """你是一个专业的意图识别智能体。你的任务是分析用户输入，识别用户的真实意图。

你需要：
1. 准确识别用户意图类型
2. 提取关键实体信息
3. 评估识别置信度
4. 提供识别理由

请始终以JSON格式返回结果，包含：
- intent: 识别出的意图类型
- confidence: 置信度(0-1)
- entities: 提取的实体信息
- explanation: 识别理由"""
        
        super().__init__(
            agent_id="intent_recognizer",
            name="意图识别智能体",
            description="专门用于识别用户意图和提取实体信息的智能体",
            api_key=api_key,
            system_prompt=system_prompt,
            temperature=0.3  # 较低温度确保一致性
        )
        
        # 注册专门技能
        self._register_intent_skills()
    
    def _register_intent_skills(self):
        """注册意图识别技能"""
        # 意图识别技能
        recognize_skill = A2ASkill(
            name="recognize_intent",
            description="识别用户输入的意图",
            parameters=[
                A2ASkillParameter(
                    name="user_input",
                    type="string",
                    description="用户输入文本",
                    required=True
                ),
                A2ASkillParameter(
                    name="intent_types",
                    type="array",
                    description="可能的意图类型列表",
                    required=False
                ),
                A2ASkillParameter(
                    name="context",
                    type="object",
                    description="上下文信息",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(recognize_skill, self._handle_recognize_intent)
        
        # 实体提取技能
        extract_entities_skill = A2ASkill(
            name="extract_entities",
            description="从文本中提取实体信息",
            parameters=[
                A2ASkillParameter(
                    name="text",
                    type="string",
                    description="待提取文本",
                    required=True
                ),
                A2ASkillParameter(
                    name="entity_types",
                    type="array",
                    description="要提取的实体类型",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(extract_entities_skill, self._handle_extract_entities)
    
    async def _handle_recognize_intent(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理意图识别"""
        try:
            user_input = parameters.get("user_input", "")
            intent_types = parameters.get("intent_types", [])
            context = parameters.get("context", {})
            
            if not user_input:
                raise ValueError("用户输入不能为空")
            
            # 构建提示词
            prompt = f"""请分析以下用户输入的意图：

用户输入: {user_input}"""
            
            if intent_types:
                prompt += f"\n\n可能的意图类型: {', '.join(intent_types)}"
            
            if context:
                prompt += f"\n\n上下文信息: {json.dumps(context, ensure_ascii=False)}"
            
            prompt += """

请返回JSON格式的结果，包含：
1. intent: 识别出的意图类型
2. confidence: 置信度(0-1)
3. entities: 提取的实体信息（字典格式）
4. explanation: 识别理由

示例格式：
{
    "intent": "generate_code",
    "confidence": 0.95,
    "entities": {"language": "Python", "task": "数据处理"},
    "explanation": "用户明确要求生成Python代码进行数据处理"
}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                # 验证结果格式
                if not isinstance(result, dict):
                    raise ValueError("响应不是有效的JSON对象")
                
                # 确保必需字段存在
                result.setdefault("intent", "unknown")
                result.setdefault("confidence", 0.5)
                result.setdefault("entities", {})
                result.setdefault("explanation", "无法确定意图")
                
                return result
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回默认结果
                logger.warning(f"意图识别响应JSON解析失败: {response}")
                return {
                    "intent": "unknown",
                    "confidence": 0.3,
                    "entities": {},
                    "explanation": f"无法解析意图，原始响应: {response[:200]}..."
                }
                
        except Exception as e:
            logger.error(f"意图识别失败: {e}")
            return {
                "intent": "error",
                "confidence": 0.0,
                "entities": {},
                "explanation": f"意图识别过程中发生错误: {str(e)}"
            }
    
    async def _handle_extract_entities(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理实体提取"""
        try:
            text = parameters.get("text", "")
            entity_types = parameters.get("entity_types", [])
            
            if not text:
                raise ValueError("待提取文本不能为空")
            
            # 构建提示词
            prompt = f"""请从以下文本中提取实体信息：

文本: {text}"""
            
            if entity_types:
                prompt += f"\n\n需要提取的实体类型: {', '.join(entity_types)}"
            
            prompt += """

请返回JSON格式的结果，包含提取到的实体信息。

示例格式：
{
    "entities": {
        "person": ["张三", "李四"],
        "location": ["北京", "上海"],
        "time": ["2024年1月"],
        "organization": ["公司A"]
    },
    "total_entities": 6
}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                # 验证结果格式
                if not isinstance(result, dict):
                    raise ValueError("响应不是有效的JSON对象")
                
                entities = result.get("entities", {})
                total_count = sum(len(v) if isinstance(v, list) else 1 for v in entities.values())
                
                return {
                    "entities": entities,
                    "total_entities": total_count,
                    "original_text": text
                }
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回空结果
                logger.warning(f"实体提取响应JSON解析失败: {response}")
                return {
                    "entities": {},
                    "total_entities": 0,
                    "original_text": text,
                    "error": "JSON解析失败"
                }
                
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return {
                "entities": {},
                "total_entities": 0,
                "original_text": text,
                "error": str(e)
            }
