"""
市场调研智能体
基于千问Plus的市场调研专用智能体
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..a2a.protocol import A2ASkill, A2ASkillParameter, A2AInteractionMode


class MarketResearcherAgent(QwenAgent):
    """市场调研智能体"""
    
    def __init__(self, api_key: str):
        """初始化市场调研智能体"""
        system_prompt = """你是一个专业的市场调研分析师。你具备深厚的市场分析经验和商业洞察力。

你的专长包括：
1. 市场规模分析和增长趋势预测
2. 竞争格局分析和竞品对比
3. 目标客户画像和需求分析
4. 行业发展趋势和机会识别
5. 商业模式分析和策略建议

请始终提供：
- 基于数据的客观分析
- 清晰的结论和建议
- 可行的行动方案
- 风险评估和应对策略"""
        
        super().__init__(
            agent_id="market_researcher",
            name="市场调研智能体",
            description="专门用于市场调研和商业分析的智能体",
            api_key=api_key,
            system_prompt=system_prompt,
            temperature=0.4  # 适中温度保证分析的客观性和创新性
        )
        
        # 注册专门技能
        self._register_research_skills()
    
    def _register_research_skills(self):
        """注册市场调研技能"""
        # 市场调研技能
        market_research_skill = A2ASkill(
            name="conduct_market_research",
            description="进行市场调研分析",
            parameters=[
                A2ASkillParameter(
                    name="research_topic",
                    type="string",
                    description="调研主题",
                    required=True
                ),
                A2ASkillParameter(
                    name="research_scope",
                    type="string",
                    description="调研范围",
                    required=False,
                    default="全球市场"
                ),
                A2ASkillParameter(
                    name="target_market",
                    type="string",
                    description="目标市场",
                    required=False
                ),
                A2ASkillParameter(
                    name="analysis_dimensions",
                    type="array",
                    description="分析维度",
                    required=False,
                    default=["市场规模", "竞争格局", "发展趋势", "客户需求"]
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(market_research_skill, self._handle_market_research)
        
        # 竞争分析技能
        competitive_analysis_skill = A2ASkill(
            name="analyze_competition",
            description="分析竞争格局",
            parameters=[
                A2ASkillParameter(
                    name="industry",
                    type="string",
                    description="行业领域",
                    required=True
                ),
                A2ASkillParameter(
                    name="competitors",
                    type="array",
                    description="竞争对手列表",
                    required=False
                ),
                A2ASkillParameter(
                    name="analysis_focus",
                    type="array",
                    description="分析重点",
                    required=False,
                    default=["产品特性", "价格策略", "市场份额", "优劣势"]
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(competitive_analysis_skill, self._handle_competitive_analysis)
        
        # 趋势分析技能
        trend_analysis_skill = A2ASkill(
            name="analyze_trends",
            description="分析市场趋势",
            parameters=[
                A2ASkillParameter(
                    name="market_sector",
                    type="string",
                    description="市场领域",
                    required=True
                ),
                A2ASkillParameter(
                    name="time_horizon",
                    type="string",
                    description="时间范围",
                    required=False,
                    default="未来3-5年"
                ),
                A2ASkillParameter(
                    name="trend_factors",
                    type="array",
                    description="趋势影响因素",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(trend_analysis_skill, self._handle_trend_analysis)
    
    async def _handle_market_research(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理市场调研"""
        try:
            research_topic = parameters.get("research_topic", "")
            research_scope = parameters.get("research_scope", "全球市场")
            target_market = parameters.get("target_market", "")
            analysis_dimensions = parameters.get("analysis_dimensions", [
                "市场规模", "竞争格局", "发展趋势", "客户需求"
            ])
            
            if not research_topic:
                raise ValueError("调研主题不能为空")
            
            # 构建提示词
            prompt = f"""请对以下主题进行详细的市场调研分析：

调研主题: {research_topic}
调研范围: {research_scope}"""
            
            if target_market:
                prompt += f"\n目标市场: {target_market}"
            
            prompt += f"""
分析维度: {', '.join(analysis_dimensions)}

请提供详细的市场调研报告，包括：

1. 市场概况
   - 市场规模和价值
   - 主要参与者
   - 市场成熟度

2. 竞争格局分析
   - 主要竞争对手
   - 市场份额分布
   - 竞争优势对比

3. 目标客户分析
   - 客户画像
   - 需求特点
   - 购买行为

4. 发展趋势预测
   - 增长趋势
   - 技术发展方向
   - 市场机会

5. 风险与挑战
   - 主要风险因素
   - 市场挑战
   - 应对策略

6. 建议与结论
   - 市场进入策略
   - 产品定位建议
   - 营销策略建议

请确保分析客观、数据支撑充分、结论明确。"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            return {
                "research_report": response,
                "research_topic": research_topic,
                "research_scope": research_scope,
                "target_market": target_market,
                "analysis_dimensions": analysis_dimensions,
                "key_findings": self._extract_key_findings(response),
                "recommendations": self._extract_recommendations(response)
            }
            
        except Exception as e:
            logger.error(f"市场调研失败: {e}")
            return {
                "research_report": f"市场调研失败: {str(e)}",
                "research_topic": research_topic,
                "error": str(e)
            }
    
    async def _handle_competitive_analysis(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理竞争分析"""
        try:
            industry = parameters.get("industry", "")
            competitors = parameters.get("competitors", [])
            analysis_focus = parameters.get("analysis_focus", [
                "产品特性", "价格策略", "市场份额", "优劣势"
            ])
            
            if not industry:
                raise ValueError("行业领域不能为空")
            
            # 构建提示词
            prompt = f"""请对{industry}行业进行竞争格局分析：

行业: {industry}"""
            
            if competitors:
                prompt += f"\n主要竞争对手: {', '.join(competitors)}"
            
            prompt += f"""
分析重点: {', '.join(analysis_focus)}

请提供详细的竞争分析报告，包括：

1. 行业概况
2. 主要竞争对手分析
3. 竞争优势对比
4. 市场定位分析
5. 竞争策略评估
6. 竞争威胁与机会

请确保分析客观公正，提供可操作的洞察。"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            return {
                "competitive_analysis": response,
                "industry": industry,
                "competitors": competitors,
                "analysis_focus": analysis_focus,
                "competitive_landscape": self._extract_competitive_landscape(response)
            }
            
        except Exception as e:
            logger.error(f"竞争分析失败: {e}")
            return {
                "competitive_analysis": f"竞争分析失败: {str(e)}",
                "industry": industry,
                "error": str(e)
            }
    
    async def _handle_trend_analysis(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理趋势分析"""
        try:
            market_sector = parameters.get("market_sector", "")
            time_horizon = parameters.get("time_horizon", "未来3-5年")
            trend_factors = parameters.get("trend_factors", [])
            
            if not market_sector:
                raise ValueError("市场领域不能为空")
            
            # 构建提示词
            prompt = f"""请分析{market_sector}领域的市场趋势：

市场领域: {market_sector}
时间范围: {time_horizon}"""
            
            if trend_factors:
                prompt += f"\n关注因素: {', '.join(trend_factors)}"
            
            prompt += """

请提供详细的趋势分析报告，包括：

1. 当前市场状况
2. 主要发展趋势
3. 技术创新影响
4. 消费者行为变化
5. 政策法规影响
6. 未来发展预测
7. 投资机会分析

请基于可靠信息进行分析，提供前瞻性洞察。"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            return {
                "trend_analysis": response,
                "market_sector": market_sector,
                "time_horizon": time_horizon,
                "trend_factors": trend_factors,
                "key_trends": self._extract_key_trends(response),
                "predictions": self._extract_predictions(response)
            }
            
        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return {
                "trend_analysis": f"趋势分析失败: {str(e)}",
                "market_sector": market_sector,
                "error": str(e)
            }
    
    def _extract_key_findings(self, text: str) -> List[str]:
        """提取关键发现"""
        findings = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['发现', '结果', '显示', '表明']):
                if line and len(line) > 10:
                    findings.append(line)
        
        return findings[:5]
    
    def _extract_recommendations(self, text: str) -> List[str]:
        """提取建议"""
        recommendations = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['建议', '推荐', '应该', '需要']):
                if line and len(line) > 10:
                    recommendations.append(line)
        
        return recommendations[:5]
    
    def _extract_competitive_landscape(self, text: str) -> Dict[str, Any]:
        """提取竞争格局信息"""
        return {
            "market_leaders": [],
            "emerging_players": [],
            "market_share_insights": [],
            "competitive_advantages": []
        }
    
    def _extract_key_trends(self, text: str) -> List[str]:
        """提取关键趋势"""
        trends = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['趋势', '发展', '增长', '变化']):
                if line and len(line) > 10:
                    trends.append(line)
        
        return trends[:5]
    
    def _extract_predictions(self, text: str) -> List[str]:
        """提取预测"""
        predictions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['预测', '预计', '预期', '将会']):
                if line and len(line) > 10:
                    predictions.append(line)
        
        return predictions[:5]
