"""
基于阿里千问的智能体实现
使用千问Plus模型作为主要LLM
"""

import asyncio
from typing import Dict, Any, Optional
import json
from openai import AsyncOpenAI
from loguru import logger

from .base_agent import BaseAgent
from ..config.agent_config import AgentConfig, LLMProvider
from ..a2a.protocol import A2ATask, A2ATaskResponse, A2ATaskStatus


class QwenAgent(BaseAgent):
    """基于阿里千问的智能体"""
    
    def __init__(self, config: AgentConfig):
        """初始化千问智能体"""
        super().__init__(config)
        
        # 确保使用千问作为LLM提供商
        if self.llm_config.provider != LLMProvider.QWEN:
            logger.warning(f"智能体 {self.name} 配置的LLM提供商不是千问，将强制使用千问")
            self.llm_config.provider = LLMProvider.QWEN
        
        # 初始化千问客户端（使用OpenAI兼容接口）
        self.client = AsyncOpenAI(
            api_key=self.llm_config.api_key,
            base_url=self.llm_config.base_url
        )
        
        logger.info(f"千问智能体 {self.name} 初始化完成，模型: {self.llm_config.model}")
    
    async def call_llm(self, prompt: str, **kwargs) -> str:
        """调用阿里千问LLM"""
        try:
            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            # 合并参数
            params = {
                "model": self.llm_config.model,
                "messages": messages,
                "temperature": self.llm_config.temperature,
                "max_tokens": self.llm_config.max_tokens,
                **kwargs
            }
            
            # 调用千问API
            response = await self.client.chat.completions.create(**params)
            
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content
                logger.debug(f"千问智能体 {self.name} LLM调用成功，输出长度: {len(content) if content else 0}")
                return content or ""
            else:
                logger.warning(f"千问智能体 {self.name} LLM调用返回空结果")
                return ""
                
        except Exception as e:
            logger.error(f"千问智能体 {self.name} LLM调用失败: {str(e)}")
            raise
    
    async def process_task(self, task: A2ATask) -> A2ATaskResponse:
        """处理A2A任务"""
        try:
            # 获取任务请求
            request = task.request
            skill_name = request.skill_name
            parameters = request.parameters
            
            logger.info(f"千问智能体 {self.name} 处理技能: {skill_name}")
            
            # 检查技能是否支持
            if skill_name not in self.a2a_skills:
                error_msg = f"不支持的技能: {skill_name}"
                logger.warning(f"千问智能体 {self.name} {error_msg}")
                return A2ATaskResponse(
                    task_id=task.task_id,
                    status=A2ATaskStatus.FAILED,
                    error=error_msg
                )
            
            # 构建提示词
            prompt = self._build_task_prompt(skill_name, parameters)
            
            # 调用LLM处理
            result_text = await self.call_llm(prompt)
            
            # 解析结果
            result = self._parse_result(result_text, skill_name)
            
            return A2ATaskResponse(
                task_id=task.task_id,
                status=A2ATaskStatus.COMPLETED,
                result=result,
                progress=1.0,
                metadata={
                    "llm_model": self.llm_config.model,
                    "skill_name": skill_name,
                    "processing_time": (task.completed_at - task.started_at).total_seconds() if task.completed_at and task.started_at else None
                }
            )
            
        except Exception as e:
            error_msg = f"任务处理失败: {str(e)}"
            logger.error(f"千问智能体 {self.name} {error_msg}")
            return A2ATaskResponse(
                task_id=task.task_id,
                status=A2ATaskStatus.FAILED,
                error=error_msg
            )
    
    def _build_task_prompt(self, skill_name: str, parameters: Dict[str, Any]) -> str:
        """构建任务提示词"""
        # 基础提示词模板
        prompt_parts = [
            f"请执行以下任务：{skill_name}",
            "",
            "任务参数："
        ]
        
        # 添加参数信息
        for key, value in parameters.items():
            prompt_parts.append(f"- {key}: {value}")
        
        prompt_parts.extend([
            "",
            "请根据任务要求提供详细的回答。如果需要返回结构化数据，请使用JSON格式。",
            "确保回答准确、完整、有用。"
        ])
        
        return "\n".join(prompt_parts)
    
    def _parse_result(self, result_text: str, skill_name: str) -> Dict[str, Any]:
        """解析LLM返回结果"""
        try:
            # 尝试解析为JSON
            if result_text.strip().startswith('{') and result_text.strip().endswith('}'):
                return json.loads(result_text)
            elif result_text.strip().startswith('[') and result_text.strip().endswith(']'):
                return {"items": json.loads(result_text)}
            else:
                # 纯文本结果
                return {
                    "content": result_text,
                    "type": "text",
                    "skill": skill_name
                }
        except json.JSONDecodeError:
            # JSON解析失败，返回纯文本
            return {
                "content": result_text,
                "type": "text",
                "skill": skill_name
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 测试千问API连接
            test_prompt = "请回复'千问智能体健康检查通过'"
            response = await self.call_llm(test_prompt)
            
            is_healthy = "健康检查通过" in response
            
            base_health = await super().health_check()
            base_health.update({
                "llm_provider": "qwen",
                "llm_model": self.llm_config.model,
                "llm_test_passed": is_healthy,
                "llm_response": response[:100] if response else None  # 只返回前100个字符
            })
            
            return base_health
            
        except Exception as e:
            base_health = await super().health_check()
            base_health.update({
                "llm_provider": "qwen",
                "llm_model": self.llm_config.model,
                "llm_test_passed": False,
                "llm_error": str(e)
            })
            return base_health
