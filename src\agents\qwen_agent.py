"""
千问智能体实现
基于阿里千问Plus的A2A智能体，统一使用qwen-plus模型
"""

import asyncio
import json
from typing import Dict, Any
from loguru import logger
import dashscope
from dashscope import Generation

from ..a2a.agent import A2AAgent
from ..a2a.protocol import A2ASkill, A2ASkillParameter, A2AInteractionMode


class QwenAgent(A2AAgent):
    """基于阿里千问Plus的A2A智能体"""
    
    def __init__(
        self,
        agent_id: str,
        name: str,
        description: str,
        api_key: str,
        system_prompt: str = "",
        temperature: float = 0.7,
        max_tokens: int = 2000
    ):
        """初始化千问智能体"""
        super().__init__(agent_id, name, description)
        
        # 千问配置
        self.api_key = api_key
        self.model = "qwen-plus"  # 强制使用qwen-plus
        self.system_prompt = system_prompt
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        # 设置API密钥
        if self.api_key:
            dashscope.api_key = self.api_key
        else:
            raise ValueError("千问API密钥未设置")
        
        # 注册千问特定技能
        self._register_qwen_skills()
        
        logger.info(f"千问智能体 {self.name} 初始化完成，使用模型: {self.model}")
    
    def _register_qwen_skills(self):
        """注册千问特定技能"""
        # 通用对话技能
        chat_skill = A2ASkill(
            name="chat",
            description="与千问Plus进行对话交流",
            parameters=[
                A2ASkillParameter(
                    name="message",
                    type="string",
                    description="用户消息",
                    required=True
                ),
                A2ASkillParameter(
                    name="context",
                    type="object",
                    description="对话上下文",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(chat_skill, self._handle_chat)
        
        # 文本生成技能
        generate_skill = A2ASkill(
            name="generate_text",
            description="基于提示词生成文本",
            parameters=[
                A2ASkillParameter(
                    name="prompt",
                    type="string",
                    description="生成提示词",
                    required=True
                ),
                A2ASkillParameter(
                    name="max_length",
                    type="integer",
                    description="最大生成长度",
                    required=False,
                    default=1000
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(generate_skill, self._handle_generate_text)
        
        # 文本分析技能
        analyze_skill = A2ASkill(
            name="analyze_text",
            description="分析文本内容",
            parameters=[
                A2ASkillParameter(
                    name="text",
                    type="string",
                    description="待分析文本",
                    required=True
                ),
                A2ASkillParameter(
                    name="analysis_type",
                    type="string",
                    description="分析类型",
                    required=False,
                    default="general",
                    enum=["general", "sentiment", "keywords", "summary"]
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(analyze_skill, self._handle_analyze_text)
    
    async def execute_skill(self, skill_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行技能"""
        if skill_name in self.skill_handlers:
            return await self.skill_handlers[skill_name](parameters)
        else:
            raise ValueError(f"技能 {skill_name} 未找到")
    
    async def call_qwen(self, messages: list, **kwargs) -> str:
        """调用千问Plus API"""
        try:
            # 合并系统提示词
            if self.system_prompt and (not messages or messages[0].get("role") != "system"):
                messages.insert(0, {"role": "system", "content": self.system_prompt})
            
            # 调用千问Plus API
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: Generation.call(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    **kwargs
                )
            )
            
            if response.status_code == 200:
                return response.output.text
            else:
                raise Exception(f"千问Plus API调用失败: {response.message}")
                
        except Exception as e:
            logger.error(f"千问Plus API调用失败: {e}")
            raise
    
    async def _handle_chat(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理对话技能"""
        try:
            message = parameters.get("message", "")
            context = parameters.get("context", {})
            
            if not message:
                raise ValueError("消息内容不能为空")
            
            # 构建消息列表
            messages = []
            
            # 添加历史对话
            if context.get("history"):
                messages.extend(context["history"])
            
            # 添加当前消息
            messages.append({"role": "user", "content": message})
            
            # 调用千问
            response = await self.call_qwen(messages)
            
            return {
                "response": response,
                "model": self.model,
                "timestamp": self.statistics["last_activity"]
            }
            
        except Exception as e:
            logger.error(f"处理对话技能失败: {e}")
            raise
    
    async def _handle_generate_text(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理文本生成技能"""
        try:
            prompt = parameters.get("prompt", "")
            max_length = parameters.get("max_length", 1000)
            
            if not prompt:
                raise ValueError("生成提示词不能为空")
            
            # 构建消息
            messages = [{"role": "user", "content": prompt}]
            
            # 调用千问
            response = await self.call_qwen(messages, max_tokens=max_length)
            
            return {
                "generated_text": response,
                "prompt": prompt,
                "model": self.model,
                "max_length": max_length
            }
            
        except Exception as e:
            logger.error(f"处理文本生成技能失败: {e}")
            raise
    
    async def _handle_analyze_text(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理文本分析技能"""
        try:
            text = parameters.get("text", "")
            analysis_type = parameters.get("analysis_type", "general")
            
            if not text:
                raise ValueError("待分析文本不能为空")
            
            # 根据分析类型构建提示词
            prompts = {
                "general": f"请分析以下文本的主要内容和特点：\n\n{text}",
                "sentiment": f"请分析以下文本的情感倾向（正面、负面、中性）：\n\n{text}",
                "keywords": f"请提取以下文本的关键词：\n\n{text}",
                "summary": f"请总结以下文本的主要内容：\n\n{text}"
            }
            
            prompt = prompts.get(analysis_type, prompts["general"])
            
            # 构建消息
            messages = [{"role": "user", "content": prompt}]
            
            # 调用千问
            response = await self.call_qwen(messages)
            
            return {
                "analysis_result": response,
                "analysis_type": analysis_type,
                "original_text": text,
                "model": self.model
            }
            
        except Exception as e:
            logger.error(f"处理文本分析技能失败: {e}")
            raise
