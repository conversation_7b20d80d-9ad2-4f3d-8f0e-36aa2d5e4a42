"""
结果验证智能体
基于千问Plus的结果验证专用智能体
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..a2a.protocol import A2ASkill, A2ASkillParameter, A2AInteractionMode


class ResultValidatorAgent(QwenAgent):
    """结果验证智能体"""
    
    def __init__(self, api_key: str):
        """初始化结果验证智能体"""
        system_prompt = """你是一个专业的质量验证专家，负责验证任务执行结果是否满足用户需求。

你的职责包括：
1. 完整性验证：检查结果是否完整回答了用户需求
2. 准确性验证：评估结果内容的准确性和可靠性
3. 相关性验证：确认结果与需求的相关程度
4. 质量验证：评估结果的专业性和实用性
5. 格式验证：检查结果格式是否符合要求

请始终提供：
- 客观的验证评估
- 具体的问题指出
- 明确的改进建议
- 是否需要重新执行的判断"""
        
        super().__init__(
            agent_id="result_validator",
            name="结果验证智能体",
            description="专门用于验证任务执行结果质量的智能体",
            api_key=api_key,
            system_prompt=system_prompt,
            temperature=0.2  # 低温度确保验证的一致性和客观性
        )
        
        # 注册专门技能
        self._register_validation_skills()
    
    def _register_validation_skills(self):
        """注册验证技能"""
        # 结果验证技能
        validate_result_skill = A2ASkill(
            name="validate_result",
            description="验证任务执行结果",
            parameters=[
                A2ASkillParameter(
                    name="original_requirement",
                    type="string",
                    description="原始用户需求",
                    required=True
                ),
                A2ASkillParameter(
                    name="execution_result",
                    type="object",
                    description="执行结果",
                    required=True
                ),
                A2ASkillParameter(
                    name="validation_criteria",
                    type="string",
                    description="验证标准",
                    required=False,
                    default="完整性、准确性、相关性、质量"
                ),
                A2ASkillParameter(
                    name="expected_format",
                    type="string",
                    description="期望的结果格式",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(validate_result_skill, self._handle_validate_result)
        
        # 质量评估技能
        assess_quality_skill = A2ASkill(
            name="assess_quality",
            description="评估结果质量",
            parameters=[
                A2ASkillParameter(
                    name="content",
                    type="string",
                    description="待评估内容",
                    required=True
                ),
                A2ASkillParameter(
                    name="quality_dimensions",
                    type="array",
                    description="质量维度",
                    required=False,
                    default=["准确性", "完整性", "清晰度", "实用性"]
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(assess_quality_skill, self._handle_assess_quality)
        
        # 需求匹配验证技能
        verify_requirement_match_skill = A2ASkill(
            name="verify_requirement_match",
            description="验证结果是否匹配需求",
            parameters=[
                A2ASkillParameter(
                    name="requirement",
                    type="string",
                    description="用户需求",
                    required=True
                ),
                A2ASkillParameter(
                    name="result",
                    type="object",
                    description="执行结果",
                    required=True
                ),
                A2ASkillParameter(
                    name="match_threshold",
                    type="number",
                    description="匹配阈值(0-1)",
                    required=False,
                    default=0.8
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(verify_requirement_match_skill, self._handle_verify_requirement_match)
    
    async def _handle_validate_result(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理结果验证"""
        try:
            original_requirement = parameters.get("original_requirement", "")
            execution_result = parameters.get("execution_result", {})
            validation_criteria = parameters.get("validation_criteria", "完整性、准确性、相关性、质量")
            expected_format = parameters.get("expected_format", "")
            
            if not original_requirement:
                raise ValueError("原始需求不能为空")
            
            if not execution_result:
                raise ValueError("执行结果不能为空")
            
            # 构建提示词
            prompt = f"""请验证以下任务执行结果是否满足用户需求：

原始需求:
{original_requirement}

执行结果:
{json.dumps(execution_result, ensure_ascii=False, indent=2)}

验证标准: {validation_criteria}"""
            
            if expected_format:
                prompt += f"\n期望格式: {expected_format}"
            
            prompt += """

请从以下维度进行详细验证：

1. 完整性验证
   - 结果是否完整回答了用户需求
   - 是否遗漏了重要信息
   - 评分: __/10

2. 准确性验证
   - 结果内容是否准确可靠
   - 是否存在错误信息
   - 评分: __/10

3. 相关性验证
   - 结果是否与需求高度相关
   - 是否包含无关内容
   - 评分: __/10

4. 质量验证
   - 结果的专业性和实用性
   - 表达是否清晰易懂
   - 评分: __/10

5. 格式验证
   - 结果格式是否符合要求
   - 结构是否合理
   - 评分: __/10

请返回JSON格式的验证结果：
{
    "validation_passed": true/false,
    "overall_score": 85,
    "dimension_scores": {
        "completeness": 8,
        "accuracy": 9,
        "relevance": 8,
        "quality": 9,
        "format": 8
    },
    "issues_found": ["问题1", "问题2"],
    "improvement_suggestions": ["建议1", "建议2"],
    "retry_needed": false,
    "validation_summary": "验证总结"
}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                # 验证和标准化结果
                result.setdefault("validation_passed", False)
                result.setdefault("overall_score", 0)
                result.setdefault("dimension_scores", {})
                result.setdefault("issues_found", [])
                result.setdefault("improvement_suggestions", [])
                result.setdefault("retry_needed", True)
                result.setdefault("validation_summary", "验证完成")
                
                return result
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，基于文本分析创建结果
                logger.warning(f"验证结果JSON解析失败: {response}")
                
                # 简单的文本分析
                validation_passed = any(keyword in response.lower() for keyword in ['通过', '满足', '符合'])
                issues = self._extract_issues_from_text(response)
                suggestions = self._extract_suggestions_from_text(response)
                
                return {
                    "validation_passed": validation_passed,
                    "overall_score": 70 if validation_passed else 40,
                    "dimension_scores": {},
                    "issues_found": issues,
                    "improvement_suggestions": suggestions,
                    "retry_needed": not validation_passed,
                    "validation_summary": response[:200] + "...",
                    "note": "JSON解析失败，使用文本分析结果"
                }
                
        except Exception as e:
            logger.error(f"结果验证失败: {e}")
            return {
                "validation_passed": False,
                "overall_score": 0,
                "dimension_scores": {},
                "issues_found": [f"验证过程出错: {str(e)}"],
                "improvement_suggestions": ["请检查输入参数并重试"],
                "retry_needed": True,
                "validation_summary": f"验证失败: {str(e)}",
                "error": str(e)
            }
    
    async def _handle_assess_quality(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理质量评估"""
        try:
            content = parameters.get("content", "")
            quality_dimensions = parameters.get("quality_dimensions", [
                "准确性", "完整性", "清晰度", "实用性"
            ])
            
            if not content:
                raise ValueError("待评估内容不能为空")
            
            # 构建提示词
            prompt = f"""请评估以下内容的质量：

内容:
{content}

评估维度: {', '.join(quality_dimensions)}

请从每个维度给出1-10分的评分，并提供具体的评估理由。

请返回JSON格式的评估结果：
{{
    "overall_quality_score": 8.5,
    "dimension_scores": {{
        "准确性": 9,
        "完整性": 8,
        "清晰度": 8,
        "实用性": 9
    }},
    "strengths": ["优点1", "优点2"],
    "weaknesses": ["不足1", "不足2"],
    "improvement_areas": ["改进方向1", "改进方向2"],
    "quality_summary": "质量评估总结"
}}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                result.setdefault("overall_quality_score", 5.0)
                result.setdefault("dimension_scores", {})
                result.setdefault("strengths", [])
                result.setdefault("weaknesses", [])
                result.setdefault("improvement_areas", [])
                result.setdefault("quality_summary", "质量评估完成")
                
                return result
                
            except json.JSONDecodeError:
                logger.warning(f"质量评估响应JSON解析失败: {response}")
                return {
                    "overall_quality_score": 5.0,
                    "dimension_scores": {},
                    "strengths": [],
                    "weaknesses": [],
                    "improvement_areas": [],
                    "quality_summary": response[:200] + "...",
                    "error": "JSON解析失败"
                }
                
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            return {
                "overall_quality_score": 0.0,
                "dimension_scores": {},
                "strengths": [],
                "weaknesses": [f"评估失败: {str(e)}"],
                "improvement_areas": ["请检查输入并重试"],
                "quality_summary": f"质量评估失败: {str(e)}",
                "error": str(e)
            }
    
    async def _handle_verify_requirement_match(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理需求匹配验证"""
        try:
            requirement = parameters.get("requirement", "")
            result = parameters.get("result", {})
            match_threshold = parameters.get("match_threshold", 0.8)
            
            if not requirement:
                raise ValueError("用户需求不能为空")
            
            # 构建提示词
            prompt = f"""请验证执行结果是否匹配用户需求：

用户需求:
{requirement}

执行结果:
{json.dumps(result, ensure_ascii=False, indent=2)}

匹配阈值: {match_threshold}

请分析结果与需求的匹配程度，返回JSON格式：
{{
    "match_score": 0.85,
    "is_match": true,
    "matched_aspects": ["匹配方面1", "匹配方面2"],
    "unmatched_aspects": ["未匹配方面1"],
    "match_analysis": "匹配分析说明"
}}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                match_score = result.get("match_score", 0.5)
                result["is_match"] = match_score >= match_threshold
                result.setdefault("matched_aspects", [])
                result.setdefault("unmatched_aspects", [])
                result.setdefault("match_analysis", "匹配验证完成")
                
                return result
                
            except json.JSONDecodeError:
                logger.warning(f"需求匹配验证响应JSON解析失败: {response}")
                return {
                    "match_score": 0.5,
                    "is_match": False,
                    "matched_aspects": [],
                    "unmatched_aspects": [],
                    "match_analysis": response[:200] + "...",
                    "error": "JSON解析失败"
                }
                
        except Exception as e:
            logger.error(f"需求匹配验证失败: {e}")
            return {
                "match_score": 0.0,
                "is_match": False,
                "matched_aspects": [],
                "unmatched_aspects": [f"验证失败: {str(e)}"],
                "match_analysis": f"需求匹配验证失败: {str(e)}",
                "error": str(e)
            }
    
    def _extract_issues_from_text(self, text: str) -> List[str]:
        """从文本中提取问题"""
        issues = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['问题', '错误', '缺陷', '不足']):
                if line and len(line) > 5:
                    issues.append(line)
        
        return issues[:5]
    
    def _extract_suggestions_from_text(self, text: str) -> List[str]:
        """从文本中提取建议"""
        suggestions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['建议', '改进', '优化', '应该']):
                if line and len(line) > 5:
                    suggestions.append(line)
        
        return suggestions[:5]
