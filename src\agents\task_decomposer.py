"""
任务分解智能体
基于千问Plus的任务分解专用智能体
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..a2a.protocol import A2ASkill, A2ASkillParameter, A2AInteractionMode


class TaskDecomposerAgent(QwenAgent):
    """任务分解智能体"""
    
    def __init__(self, api_key: str):
        """初始化任务分解智能体"""
        system_prompt = """你是一个专业的任务分解智能体。你的任务是将复杂的用户需求分解为可执行的子任务。

你需要：
1. 理解用户的整体需求
2. 将复杂任务分解为简单的子任务
3. 确定任务之间的依赖关系
4. 评估任务的复杂度和优先级

请始终以JSON格式返回结果，包含：
- tasks: 分解后的任务列表
- dependencies: 任务依赖关系
- complexity: 整体复杂度评估
- estimated_time: 预估执行时间"""
        
        super().__init__(
            agent_id="task_decomposer",
            name="任务分解智能体",
            description="专门用于将复杂任务分解为可执行子任务的智能体",
            api_key=api_key,
            system_prompt=system_prompt,
            temperature=0.4  # 适中温度保证创造性和一致性
        )
        
        # 注册专门技能
        self._register_decomposer_skills()
    
    def _register_decomposer_skills(self):
        """注册任务分解技能"""
        # 任务分解技能
        decompose_skill = A2ASkill(
            name="decompose_task",
            description="将复杂任务分解为子任务",
            parameters=[
                A2ASkillParameter(
                    name="user_request",
                    type="string",
                    description="用户需求描述",
                    required=True
                ),
                A2ASkillParameter(
                    name="intent",
                    type="string",
                    description="识别出的用户意图",
                    required=False
                ),
                A2ASkillParameter(
                    name="max_tasks",
                    type="integer",
                    description="最大子任务数量",
                    required=False,
                    default=10
                ),
                A2ASkillParameter(
                    name="context",
                    type="object",
                    description="上下文信息",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(decompose_skill, self._handle_decompose_task)
        
        # 任务优化技能
        optimize_tasks_skill = A2ASkill(
            name="optimize_tasks",
            description="优化任务执行顺序和依赖关系",
            parameters=[
                A2ASkillParameter(
                    name="tasks",
                    type="array",
                    description="任务列表",
                    required=True
                ),
                A2ASkillParameter(
                    name="constraints",
                    type="object",
                    description="约束条件",
                    required=False
                )
            ],
            interaction_modes=[A2AInteractionMode.REQUEST_RESPONSE]
        )
        self.register_skill(optimize_tasks_skill, self._handle_optimize_tasks)
    
    async def _handle_decompose_task(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务分解"""
        try:
            user_request = parameters.get("user_request", "")
            intent = parameters.get("intent", "")
            max_tasks = parameters.get("max_tasks", 10)
            context = parameters.get("context", {})
            
            if not user_request:
                raise ValueError("用户需求描述不能为空")
            
            # 构建提示词
            prompt = f"""请将以下用户需求分解为可执行的子任务：

用户需求: {user_request}"""
            
            if intent:
                prompt += f"\n用户意图: {intent}"
            
            if context:
                prompt += f"\n上下文信息: {json.dumps(context, ensure_ascii=False)}"
            
            prompt += f"""

请将任务分解为不超过{max_tasks}个子任务，并返回JSON格式的结果：

{{
    "tasks": [
        {{
            "id": "task_1",
            "title": "任务标题",
            "description": "详细描述",
            "priority": 1,
            "estimated_time": "5分钟",
            "complexity": "简单",
            "dependencies": [],
            "required_skills": ["技能1", "技能2"]
        }}
    ],
    "dependencies": {{
        "task_2": ["task_1"],
        "task_3": ["task_1", "task_2"]
    }},
    "complexity": "中等",
    "estimated_total_time": "30分钟",
    "execution_strategy": "顺序执行"
}}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                # 验证和标准化结果
                tasks = result.get("tasks", [])
                
                # 确保每个任务都有必需字段
                for i, task in enumerate(tasks):
                    task.setdefault("id", f"task_{i+1}")
                    task.setdefault("title", f"子任务{i+1}")
                    task.setdefault("description", "")
                    task.setdefault("priority", 1)
                    task.setdefault("estimated_time", "未知")
                    task.setdefault("complexity", "中等")
                    task.setdefault("dependencies", [])
                    task.setdefault("required_skills", [])
                
                result["tasks"] = tasks
                result.setdefault("dependencies", {})
                result.setdefault("complexity", "中等")
                result.setdefault("estimated_total_time", "未知")
                result.setdefault("execution_strategy", "顺序执行")
                
                return result
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，创建简单的任务分解
                logger.warning(f"任务分解响应JSON解析失败: {response}")
                
                # 基于响应文本创建简单任务
                simple_tasks = [
                    {
                        "id": "task_1",
                        "title": "分析需求",
                        "description": user_request,
                        "priority": 1,
                        "estimated_time": "10分钟",
                        "complexity": "简单",
                        "dependencies": [],
                        "required_skills": ["分析"]
                    }
                ]
                
                return {
                    "tasks": simple_tasks,
                    "dependencies": {},
                    "complexity": "简单",
                    "estimated_total_time": "10分钟",
                    "execution_strategy": "顺序执行",
                    "note": "JSON解析失败，返回简化任务分解"
                }
                
        except Exception as e:
            logger.error(f"任务分解失败: {e}")
            return {
                "tasks": [],
                "dependencies": {},
                "complexity": "未知",
                "estimated_total_time": "未知",
                "execution_strategy": "未知",
                "error": str(e)
            }
    
    async def _handle_optimize_tasks(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务优化"""
        try:
            tasks = parameters.get("tasks", [])
            constraints = parameters.get("constraints", {})
            
            if not tasks:
                raise ValueError("任务列表不能为空")
            
            # 构建提示词
            prompt = f"""请优化以下任务的执行顺序和依赖关系：

任务列表: {json.dumps(tasks, ensure_ascii=False)}"""
            
            if constraints:
                prompt += f"\n约束条件: {json.dumps(constraints, ensure_ascii=False)}"
            
            prompt += """

请返回优化后的JSON格式结果：
{
    "optimized_tasks": [...],
    "execution_order": ["task_1", "task_2", "task_3"],
    "parallel_groups": [["task_1", "task_2"], ["task_3"]],
    "optimization_notes": "优化说明"
}"""
            
            # 调用千问
            messages = [{"role": "user", "content": prompt}]
            response = await self.call_qwen(messages)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                
                result.setdefault("optimized_tasks", tasks)
                result.setdefault("execution_order", [task.get("id", f"task_{i}") for i, task in enumerate(tasks)])
                result.setdefault("parallel_groups", [])
                result.setdefault("optimization_notes", "任务顺序已优化")
                
                return result
                
            except json.JSONDecodeError:
                logger.warning(f"任务优化响应JSON解析失败: {response}")
                return {
                    "optimized_tasks": tasks,
                    "execution_order": [task.get("id", f"task_{i}") for i, task in enumerate(tasks)],
                    "parallel_groups": [],
                    "optimization_notes": "使用原始任务顺序",
                    "error": "JSON解析失败"
                }
                
        except Exception as e:
            logger.error(f"任务优化失败: {e}")
            return {
                "optimized_tasks": [],
                "execution_order": [],
                "parallel_groups": [],
                "optimization_notes": "优化失败",
                "error": str(e)
            }
