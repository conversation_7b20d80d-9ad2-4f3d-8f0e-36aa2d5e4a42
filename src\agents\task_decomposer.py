"""
任务分解智能体
基于阿里千问实现复杂任务的分解
"""

import json
from typing import Dict, Any, List
from loguru import logger

from .qwen_agent import QwenAgent
from ..config.agent_config import AgentConfig, create_default_qwen_agent_config
from ..a2a.protocol import A2ATask, A2ATaskResponse, A2ATaskStatus


class TaskDecomposerAgent(QwenAgent):
    """任务分解智能体"""
    
    def __init__(self, config: AgentConfig):
        """初始化任务分解智能体"""
        super().__init__(config)
        
        # 任务分解模板
        self.decomposition_templates = {
            "generate_code": {
                "steps": ["需求分析", "技术选型", "架构设计", "代码实现", "测试验证"],
                "max_subtasks": 8
            },
            "market_research": {
                "steps": ["市场定义", "数据收集", "竞争分析", "趋势分析", "报告生成"],
                "max_subtasks": 10
            },
            "product_analysis": {
                "steps": ["产品调研", "功能分析", "竞品对比", "用户体验评估", "总结报告"],
                "max_subtasks": 8
            },
            "travel_planning": {
                "steps": ["目的地研究", "行程规划", "住宿安排", "交通方案", "预算计算"],
                "max_subtasks": 12
            },
            "data_analysis": {
                "steps": ["数据收集", "数据清洗", "探索性分析", "统计分析", "可视化", "结论总结"],
                "max_subtasks": 10
            }
        }
        
        logger.info(f"任务分解智能体 {self.name} 初始化完成，支持 {len(self.decomposition_templates)} 种任务类型")
    
    def _build_task_prompt(self, skill_name: str, parameters: Dict[str, Any]) -> str:
        """构建任务分解提示词"""
        intent = parameters.get("intent", "")
        user_request = parameters.get("user_request", "")
        max_tasks = parameters.get("max_tasks", 8)
        context = parameters.get("context", {})
        
        # 获取任务模板
        template = self.decomposition_templates.get(intent, {
            "steps": ["需求分析", "方案设计", "具体实施", "结果验证"],
            "max_subtasks": max_tasks
        })
        
        prompt = f"""
你是一个专业的任务分解专家。请将复杂任务分解为具体的、可执行的子任务。

用户需求：
{user_request}

识别的意图：{intent}

参考分解步骤：
{', '.join(template['steps'])}

分解要求：
1. 最多分解为 {max_tasks} 个子任务
2. 每个子任务应该具体、明确、可执行
3. 子任务之间应该有逻辑顺序
4. 考虑任务的复杂度和可行性
5. 为每个子任务指定所需的技能和资源

请返回以下JSON格式的结果：
{{
    "total_tasks": 5,
    "estimated_time": "预估总时间",
    "complexity": "简单/中等/复杂",
    "tasks": [
        {{
            "task_id": "task_1",
            "name": "子任务名称",
            "description": "详细描述",
            "priority": 1,
            "estimated_time": "预估时间",
            "required_skills": ["技能1", "技能2"],
            "dependencies": [],
            "parameters": {{
                "param1": "value1"
            }},
            "success_criteria": "成功标准"
        }}
    ],
    "execution_strategy": "执行策略说明",
    "risk_assessment": "风险评估",
    "recommendations": ["建议1", "建议2"]
}}

分解原则：
- 确保每个子任务都有明确的输入和输出
- 考虑任务间的依赖关系
- 平衡任务的粒度，既不过于细碎也不过于宽泛
- 为每个任务提供成功标准
"""
        return prompt
    
    def _parse_result(self, result_text: str, skill_name: str) -> Dict[str, Any]:
        """解析任务分解结果"""
        try:
            # 尝试解析JSON结果
            if result_text.strip().startswith('{'):
                result = json.loads(result_text)
                
                # 验证和补充必要字段
                if "tasks" not in result:
                    result["tasks"] = []
                if "total_tasks" not in result:
                    result["total_tasks"] = len(result["tasks"])
                if "estimated_time" not in result:
                    result["estimated_time"] = "未估算"
                if "complexity" not in result:
                    result["complexity"] = "中等"
                if "execution_strategy" not in result:
                    result["execution_strategy"] = "顺序执行"
                if "risk_assessment" not in result:
                    result["risk_assessment"] = "风险评估待完善"
                if "recommendations" not in result:
                    result["recommendations"] = []
                
                # 验证和补充任务字段
                for i, task in enumerate(result["tasks"]):
                    if "task_id" not in task:
                        task["task_id"] = f"task_{i+1}"
                    if "name" not in task:
                        task["name"] = f"子任务{i+1}"
                    if "description" not in task:
                        task["description"] = "待补充描述"
                    if "priority" not in task:
                        task["priority"] = i + 1
                    if "estimated_time" not in task:
                        task["estimated_time"] = "待估算"
                    if "required_skills" not in task:
                        task["required_skills"] = []
                    if "dependencies" not in task:
                        task["dependencies"] = []
                    if "parameters" not in task:
                        task["parameters"] = {}
                    if "success_criteria" not in task:
                        task["success_criteria"] = "任务完成"
                
                return result
            else:
                # 如果不是JSON格式，尝试从文本中提取任务
                return self._extract_tasks_from_text(result_text)
                
        except json.JSONDecodeError as e:
            logger.warning(f"任务分解结果JSON解析失败: {e}，尝试文本解析")
            return self._extract_tasks_from_text(result_text)
        except Exception as e:
            logger.error(f"任务分解结果解析失败: {e}")
            return {
                "total_tasks": 0,
                "estimated_time": "解析失败",
                "complexity": "未知",
                "tasks": [],
                "execution_strategy": "解析失败",
                "risk_assessment": f"结果解析失败: {str(e)}",
                "recommendations": ["请重新分解任务"],
                "raw_output": result_text
            }
    
    def _extract_tasks_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取任务信息"""
        lines = text.split('\n')
        tasks = []
        current_task = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是任务标题（数字开头或包含"任务"）
            if (line[0].isdigit() and ('.' in line or ')' in line)) or '任务' in line:
                if current_task:
                    tasks.append(current_task)
                
                # 提取任务名称
                task_name = line
                if '.' in line:
                    task_name = line.split('.', 1)[1].strip()
                elif ')' in line:
                    task_name = line.split(')', 1)[1].strip()
                
                current_task = {
                    "task_id": f"task_{len(tasks) + 1}",
                    "name": task_name,
                    "description": task_name,
                    "priority": len(tasks) + 1,
                    "estimated_time": "待估算",
                    "required_skills": [],
                    "dependencies": [],
                    "parameters": {},
                    "success_criteria": "任务完成"
                }
            elif current_task and line:
                # 补充任务描述
                if current_task["description"] == current_task["name"]:
                    current_task["description"] = line
                else:
                    current_task["description"] += " " + line
        
        # 添加最后一个任务
        if current_task:
            tasks.append(current_task)
        
        return {
            "total_tasks": len(tasks),
            "estimated_time": "基于文本解析",
            "complexity": "中等",
            "tasks": tasks,
            "execution_strategy": "顺序执行",
            "risk_assessment": "基于文本解析，可能不够准确",
            "recommendations": ["建议使用结构化输入重新分解"],
            "raw_output": text
        }


def create_task_decomposer_agent(api_key: str) -> TaskDecomposerAgent:
    """创建任务分解智能体"""
    config = create_default_qwen_agent_config(
        agent_id="task_decomposer",
        name="任务分解智能体",
        description="专业的任务分解智能体，能够将复杂任务分解为具体可执行的子任务",
        system_prompt="""你是一个专业的任务分解专家。你的任务是将复杂的用户需求分解为具体、明确、可执行的子任务。

你需要：
1. 深入理解用户需求和意图
2. 将复杂任务分解为逻辑清晰的子任务
3. 为每个子任务指定优先级和依赖关系
4. 估算任务时间和复杂度
5. 识别所需的技能和资源
6. 提供执行策略和风险评估

分解原则：
- 确保子任务具体可执行
- 保持适当的任务粒度
- 考虑任务间的逻辑关系
- 提供明确的成功标准

请始终以JSON格式返回结果，确保结果结构化、完整、实用。""",
        api_key=api_key,
        capabilities=["task_decomposition", "project_planning", "workflow_design"],
        a2a_skills=["decompose_task", "plan_project", "analyze_requirements"]
    )
    
    return TaskDecomposerAgent(config)
