"""
API路由定义
基于FastAPI的RESTful API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime

from ..database.database import get_database
from ..workflow.engine import workflow_engine


# 请求模型
class TaskRequest(BaseModel):
    task_id: Optional[str] = None
    skill_name: str
    parameters: Dict[str, Any] = {}


class WorkflowRequest(BaseModel):
    workflow_id: str
    name: str
    description: str
    steps: List[Dict[str, Any]] = []
    input_schema: Dict[str, Any] = {}
    output_schema: Dict[str, Any] = {}
    tags: List[str] = []
    metadata: Dict[str, Any] = {}


class WorkflowExecutionRequest(BaseModel):
    input_data: Dict[str, Any]
    session_id: Optional[str] = None


class SessionRequest(BaseModel):
    workflow_name: str
    user_id: Optional[str] = None
    input_data: Dict[str, Any]


def create_api_router() -> APIRouter:
    """创建API路由器"""
    router = APIRouter()
    
    # 系统信息
    @router.get("/")
    async def get_system_info():
        """获取系统信息"""
        return {
            "name": "A2A多智能体协作系统",
            "version": "1.0.0",
            "description": "基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统",
            "status": "running",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    @router.get("/health")
    async def health_check():
        """系统健康检查"""
        try:
            # 检查数据库
            db = get_database()
            db_stats = db.get_statistics()
            
            # 检查工作流引擎
            engine_status = workflow_engine.get_engine_status()
            
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "database": db_stats,
                "workflow_engine": engine_status
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")
    
    # 智能体管理
    @router.get("/agents")
    async def get_agents():
        """获取智能体列表"""
        try:
            agents_list = []
            for agent in workflow_engine.list_agents():
                agent_card = agent.get_agent_card()
                agents_list.append({
                    "agent_card": agent_card.dict(),
                    "statistics": agent.statistics
                })
            
            return {"agents": agents_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取智能体列表失败: {str(e)}")
    
    @router.get("/agents/{agent_id}")
    async def get_agent(agent_id: str):
        """获取智能体详情"""
        try:
            if agent_id not in workflow_engine.agents:
                raise HTTPException(status_code=404, detail=f"智能体 {agent_id} 未找到")
            
            agent = workflow_engine.agents[agent_id]
            return {
                "agent_card": agent.get_agent_card().dict(),
                "statistics": agent.statistics,
                "skills": list(agent.skills.keys())
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取智能体详情失败: {str(e)}")
    
    @router.post("/agents/{agent_id}/tasks")
    async def create_agent_task(agent_id: str, task_request: TaskRequest):
        """创建智能体任务"""
        try:
            if agent_id not in workflow_engine.agents:
                raise HTTPException(status_code=404, detail=f"智能体 {agent_id} 未找到")
            
            task_id = task_request.task_id or str(uuid.uuid4())
            
            # 执行任务
            result = await workflow_engine.execute_agent_task(
                agent_id,
                task_request.skill_name,
                task_request.parameters
            )
            
            return {
                "task_id": task_id,
                "status": "completed",
                "result": result,
                "agent_id": agent_id,
                "skill_name": task_request.skill_name
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建智能体任务失败: {str(e)}")
    
    # 工作流管理
    @router.get("/workflows")
    async def get_workflows():
        """获取工作流列表"""
        try:
            workflows_list = []
            for workflow in workflow_engine.list_workflows():
                workflows_list.append({
                    "workflow_id": workflow.workflow_id,
                    "name": workflow.name,
                    "description": workflow.description,
                    "steps": [step.dict() for step in workflow.steps],
                    "input_schema": workflow.input_schema,
                    "output_schema": workflow.output_schema,
                    "tags": workflow.tags,
                    "metadata": workflow.metadata,
                    "version": workflow.version,
                    "is_active": workflow.is_active,
                    "created_at": workflow.created_at.isoformat(),
                    "updated_at": workflow.updated_at.isoformat()
                })
            
            return {"workflows": workflows_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取工作流列表失败: {str(e)}")
    
    @router.get("/workflows/{workflow_id}")
    async def get_workflow(workflow_id: str):
        """获取工作流详情"""
        try:
            workflow = workflow_engine.get_workflow(workflow_id)
            if not workflow:
                raise HTTPException(status_code=404, detail=f"工作流 {workflow_id} 未找到")
            
            return {
                "workflow_id": workflow.workflow_id,
                "name": workflow.name,
                "description": workflow.description,
                "steps": [step.dict() for step in workflow.steps],
                "input_schema": workflow.input_schema,
                "output_schema": workflow.output_schema,
                "tags": workflow.tags,
                "metadata": workflow.metadata,
                "version": workflow.version,
                "is_active": workflow.is_active,
                "created_at": workflow.created_at.isoformat(),
                "updated_at": workflow.updated_at.isoformat()
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取工作流详情失败: {str(e)}")
    
    @router.post("/workflows/{workflow_id}/execute")
    async def execute_workflow(workflow_id: str, execution_request: WorkflowExecutionRequest):
        """执行工作流"""
        try:
            workflow = workflow_engine.get_workflow(workflow_id)
            if not workflow:
                raise HTTPException(status_code=404, detail=f"工作流 {workflow_id} 未找到")
            
            # 创建会话
            db = get_database()
            session_id = execution_request.session_id or str(uuid.uuid4())
            
            db_session_id = db.create_session(
                user_id=None,
                workflow_name=workflow.name,
                input_data=execution_request.input_data
            )
            
            # 执行工作流
            result = await workflow_engine.execute_workflow(
                workflow, 
                execution_request.input_data,
                session_id=db_session_id
            )
            
            # 更新会话状态
            db.update_session_status(db_session_id, "completed", result)
            
            return {
                "session_id": db_session_id,
                "workflow_id": workflow_id,
                "status": "completed",
                "result": result
            }
        except HTTPException:
            raise
        except Exception as e:
            # 更新会话状态为失败
            if 'db_session_id' in locals():
                db.update_session_status(db_session_id, "failed", error_message=str(e))
            raise HTTPException(status_code=500, detail=f"执行工作流失败: {str(e)}")
    
    # 会话管理
    @router.get("/sessions")
    async def get_sessions(
        page: int = Query(1, ge=1),
        page_size: int = Query(20, ge=1, le=100),
        status: Optional[str] = Query(None)
    ):
        """获取会话列表"""
        try:
            db = get_database()
            offset = (page - 1) * page_size
            sessions = db.list_sessions(status=status, limit=page_size, offset=offset)

            sessions_list = []
            for session in sessions:
                sessions_list.append({
                    "id": session.id,
                    "user_id": session.user_id,
                    "workflow_name": session.workflow_name,
                    "status": session.status.value,
                    "input_data": session.input_data,
                    "output_data": session.output_data,
                    "created_at": session.created_at.isoformat(),
                    "updated_at": session.updated_at.isoformat(),
                    "started_at": session.started_at.isoformat() if session.started_at else None,
                    "completed_at": session.completed_at.isoformat() if session.completed_at else None
                })

            return {
                "sessions": sessions_list,
                "total": len(sessions_list),
                "page": page,
                "page_size": page_size
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

    @router.get("/sessions/{session_id}")
    async def get_session(session_id: str):
        """获取会话详情"""
        try:
            db = get_database()
            session = db.get_session_by_id(session_id)

            if not session:
                raise HTTPException(status_code=404, detail=f"会话 {session_id} 未找到")

            return {
                "id": session.id,
                "user_id": session.user_id,
                "workflow_name": session.workflow_name,
                "status": session.status.value,
                "input_data": session.input_data,
                "output_data": session.output_data,
                "context": session.context,
                "error_message": session.error_message,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "started_at": session.started_at.isoformat() if session.started_at else None,
                "completed_at": session.completed_at.isoformat() if session.completed_at else None
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话详情失败: {str(e)}")

    @router.post("/sessions")
    async def create_session(session_request: SessionRequest):
        """创建会话"""
        try:
            db = get_database()
            session_id = db.create_session(
                user_id=session_request.user_id,
                workflow_name=session_request.workflow_name,
                input_data=session_request.input_data
            )

            return {
                "session_id": session_id,
                "workflow_name": session_request.workflow_name,
                "status": "created",
                "message": "会话创建成功"
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")

    @router.get("/sessions/{session_id}/tasks")
    async def get_session_tasks(session_id: str):
        """获取会话任务"""
        try:
            db = get_database()
            tasks = db.get_session_tasks(session_id)

            tasks_list = []
            for task in tasks:
                tasks_list.append({
                    "id": task.id,
                    "session_id": task.session_id,
                    "agent_name": task.agent_name,
                    "task_name": task.task_name,
                    "task_description": task.task_description,
                    "status": task.status.value,
                    "input_data": task.input_data,
                    "output_data": task.output_data,
                    "error_message": task.error_message,
                    "progress": task.progress,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                })

            return {"tasks": tasks_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话任务失败: {str(e)}")

    @router.get("/sessions/{session_id}/messages")
    async def get_session_messages(session_id: str):
        """获取会话消息"""
        try:
            db = get_database()
            messages = db.get_session_messages(session_id)

            messages_list = []
            for message in messages:
                messages_list.append({
                    "id": message.id,
                    "session_id": message.session_id,
                    "sender": message.sender,
                    "receiver": message.receiver,
                    "message_type": message.message_type,
                    "content": message.content,
                    "timestamp": message.timestamp.isoformat()
                })

            return {"messages": messages_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")

    @router.get("/sessions/{session_id}/logs")
    async def get_session_logs(session_id: str, level: Optional[str] = Query(None)):
        """获取会话日志"""
        try:
            db = get_database()
            logs = db.get_session_logs(session_id, level)

            logs_list = []
            for log in logs:
                logs_list.append({
                    "id": log.id,
                    "session_id": log.session_id,
                    "level": log.level.value,
                    "message": log.message,
                    "context": log.context,
                    "timestamp": log.timestamp.isoformat()
                })

            return {"logs": logs_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话日志失败: {str(e)}")

    return router
