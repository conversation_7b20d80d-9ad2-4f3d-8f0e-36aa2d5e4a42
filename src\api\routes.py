"""
API路由定义
基于FastAPI的RESTful API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import uuid
from datetime import datetime

from ..database.database import get_database
from ..workflow.engine import workflow_engine
from ..mcp.manager import get_mcp_manager
from ..workflows.examples import (
    create_code_generation_workflow,
    create_market_research_workflow,
    create_product_analysis_workflow,
    create_travel_planning_workflow,
    create_data_analysis_workflow
)


# 请求模型
class TaskRequest(BaseModel):
    task_id: Optional[str] = None
    skill_name: str
    parameters: Dict[str, Any] = {}


class WorkflowRequest(BaseModel):
    workflow_id: str
    name: str
    description: str
    steps: List[Dict[str, Any]] = []
    input_schema: Dict[str, Any] = {}
    output_schema: Dict[str, Any] = {}
    tags: List[str] = []
    metadata: Dict[str, Any] = {}


class WorkflowExecutionRequest(BaseModel):
    input_data: Dict[str, Any]
    session_id: Optional[str] = None


class SessionRequest(BaseModel):
    workflow_name: str
    user_id: Optional[str] = None
    input_data: Dict[str, Any]


def create_api_router() -> APIRouter:
    """创建API路由器"""
    router = APIRouter()
    
    # 系统信息
    @router.get("/")
    async def get_system_info():
        """获取系统信息"""
        return {
            "name": "A2A多智能体协作系统",
            "version": "1.0.0",
            "description": "基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统",
            "status": "running",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    @router.get("/health")
    async def health_check():
        """系统健康检查"""
        try:
            # 检查数据库
            db = get_database()
            db_stats = db.get_statistics()
            
            # 检查智能体
            agents_info = []
            for agent_id, agent in workflow_engine.agents.items():
                agents_info.append({
                    "agent_id": agent_id,
                    "name": agent.name,
                    "is_active": agent.statistics.get("is_active", True),
                    "total_tasks": agent.statistics.get("total_tasks", 0),
                    "success_rate": agent.statistics.get("success_rate", 0.0)
                })
            
            # 检查MCP服务
            mcp_manager = get_mcp_manager()
            mcp_health = await mcp_manager.health_check_all()
            
            return {
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "database": db_stats,
                "agents": agents_info,
                "total_agents": len(agents_info),
                "active_tasks": sum(agent["total_tasks"] for agent in agents_info),
                "mcp_services": mcp_health,
                "healthy_services": mcp_health.get("healthy_services", 0),
                "unhealthy_services": mcp_health.get("unhealthy_services", 0)
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")
    
    # 智能体管理
    @router.get("/agents")
    async def get_agents():
        """获取智能体列表"""
        try:
            agents_list = []
            for agent_id, agent in workflow_engine.agents.items():
                agent_card = agent.get_agent_card()
                agents_list.append({
                    "agent_card": agent_card,
                    "statistics": agent.statistics
                })
            
            return {"agents": agents_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取智能体列表失败: {str(e)}")
    
    @router.get("/agents/{agent_id}")
    async def get_agent(agent_id: str):
        """获取智能体详情"""
        try:
            if agent_id not in workflow_engine.agents:
                raise HTTPException(status_code=404, detail=f"智能体 {agent_id} 未找到")
            
            agent = workflow_engine.agents[agent_id]
            return {
                "agent_card": agent.get_agent_card(),
                "statistics": agent.statistics,
                "skills": list(agent.skills.keys()) if hasattr(agent, 'skills') else []
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取智能体详情失败: {str(e)}")
    
    @router.post("/agents/{agent_id}/tasks")
    async def create_agent_task(agent_id: str, task_request: TaskRequest):
        """创建智能体任务"""
        try:
            if agent_id not in workflow_engine.agents:
                raise HTTPException(status_code=404, detail=f"智能体 {agent_id} 未找到")
            
            task_id = task_request.task_id or str(uuid.uuid4())
            
            # 执行任务
            result = await workflow_engine.execute_agent_task(
                agent_id,
                task_request.skill_name,
                task_request.parameters
            )
            
            return {
                "task_id": task_id,
                "status": "completed",
                "result": result,
                "agent_id": agent_id,
                "skill_name": task_request.skill_name
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建智能体任务失败: {str(e)}")
    
    @router.get("/agents/{agent_id}/health")
    async def get_agent_health(agent_id: str):
        """智能体健康检查"""
        try:
            if agent_id not in workflow_engine.agents:
                raise HTTPException(status_code=404, detail=f"智能体 {agent_id} 未找到")
            
            agent = workflow_engine.agents[agent_id]
            
            # 简单的健康检查 - 尝试调用LLM
            try:
                test_response = await agent.call_llm("测试连接")
                llm_test_passed = bool(test_response)
            except Exception:
                llm_test_passed = False
            
            return {
                "agent_id": agent_id,
                "status": "healthy" if llm_test_passed else "unhealthy",
                "llm_test_passed": llm_test_passed,
                "statistics": agent.statistics,
                "timestamp": datetime.utcnow().isoformat()
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"智能体健康检查失败: {str(e)}")
    
    # 工作流管理
    @router.get("/workflows")
    async def get_workflows():
        """获取工作流列表"""
        try:
            # 返回预定义工作流
            workflows = [
                create_code_generation_workflow(),
                create_market_research_workflow(),
                create_product_analysis_workflow(),
                create_travel_planning_workflow(),
                create_data_analysis_workflow()
            ]
            
            workflows_list = []
            for workflow in workflows:
                workflows_list.append({
                    "workflow_id": workflow.workflow_id,
                    "name": workflow.name,
                    "description": workflow.description,
                    "steps": [step.dict() for step in workflow.steps],
                    "input_schema": workflow.input_schema,
                    "output_schema": workflow.output_schema,
                    "tags": workflow.tags,
                    "metadata": workflow.metadata,
                    "version": "1.0.0",
                    "is_active": True
                })
            
            return {"workflows": workflows_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取工作流列表失败: {str(e)}")
    
    @router.get("/workflows/{workflow_id}")
    async def get_workflow(workflow_id: str):
        """获取工作流详情"""
        try:
            # 查找预定义工作流
            workflows = {
                "code_generation_workflow": create_code_generation_workflow(),
                "market_research_workflow": create_market_research_workflow(),
                "product_analysis_workflow": create_product_analysis_workflow(),
                "travel_planning_workflow": create_travel_planning_workflow(),
                "data_analysis_workflow": create_data_analysis_workflow()
            }
            
            if workflow_id not in workflows:
                raise HTTPException(status_code=404, detail=f"工作流 {workflow_id} 未找到")
            
            workflow = workflows[workflow_id]
            return {
                "workflow_id": workflow.workflow_id,
                "name": workflow.name,
                "description": workflow.description,
                "steps": [step.dict() for step in workflow.steps],
                "input_schema": workflow.input_schema,
                "output_schema": workflow.output_schema,
                "tags": workflow.tags,
                "metadata": workflow.metadata,
                "version": "1.0.0",
                "is_active": True
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取工作流详情失败: {str(e)}")
    
    @router.post("/workflows")
    async def create_workflow(workflow_request: WorkflowRequest):
        """创建工作流"""
        try:
            # 这里应该保存到数据库，暂时返回成功
            return {
                "workflow_id": workflow_request.workflow_id,
                "name": workflow_request.name,
                "status": "created",
                "message": "工作流创建成功"
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建工作流失败: {str(e)}")
    
    @router.post("/workflows/{workflow_id}/execute")
    async def execute_workflow(workflow_id: str, execution_request: WorkflowExecutionRequest):
        """执行工作流"""
        try:
            # 查找工作流
            workflows = {
                "code_generation_workflow": create_code_generation_workflow(),
                "market_research_workflow": create_market_research_workflow(),
                "product_analysis_workflow": create_product_analysis_workflow(),
                "travel_planning_workflow": create_travel_planning_workflow(),
                "data_analysis_workflow": create_data_analysis_workflow()
            }
            
            if workflow_id not in workflows:
                raise HTTPException(status_code=404, detail=f"工作流 {workflow_id} 未找到")
            
            workflow = workflows[workflow_id]
            
            # 创建会话
            db = get_database()
            session_id = execution_request.session_id or str(uuid.uuid4())
            
            db_session_id = db.create_session(
                user_id=None,
                workflow_name=workflow.name,
                input_data=execution_request.input_data
            )
            
            # 执行工作流
            result = await workflow_engine.execute_workflow(workflow, execution_request.input_data)
            
            # 更新会话状态
            db.update_session_status(db_session_id, "completed", result)
            
            return {
                "session_id": db_session_id,
                "workflow_id": workflow_id,
                "status": "completed",
                "result": result
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"执行工作流失败: {str(e)}")
    
    # 会话管理
    @router.get("/sessions")
    async def get_sessions(
        page: int = Query(1, ge=1),
        page_size: int = Query(20, ge=1, le=100),
        status: Optional[str] = Query(None)
    ):
        """获取会话列表"""
        try:
            db = get_database()
            sessions = db.list_sessions(status=status, limit=page_size)

            # 简单分页处理
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_sessions = sessions[start_idx:end_idx]

            sessions_list = []
            for session in paginated_sessions:
                sessions_list.append({
                    "id": session.id,
                    "user_id": session.user_id,
                    "workflow_name": session.workflow_name,
                    "status": session.status.value if hasattr(session.status, 'value') else session.status,
                    "input_data": session.input_data,
                    "output_data": session.output_data,
                    "created_at": session.created_at.isoformat() if session.created_at else None,
                    "updated_at": session.updated_at.isoformat() if session.updated_at else None
                })

            return {
                "sessions": sessions_list,
                "total": len(sessions),
                "page": page,
                "page_size": page_size
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

    @router.get("/sessions/{session_id}")
    async def get_session(session_id: str):
        """获取会话详情"""
        try:
            db = get_database()
            session = db.get_session_by_id(session_id)

            if not session:
                raise HTTPException(status_code=404, detail=f"会话 {session_id} 未找到")

            return {
                "id": session.id,
                "user_id": session.user_id,
                "workflow_name": session.workflow_name,
                "status": session.status.value if hasattr(session.status, 'value') else session.status,
                "input_data": session.input_data,
                "output_data": session.output_data,
                "created_at": session.created_at.isoformat() if session.created_at else None,
                "updated_at": session.updated_at.isoformat() if session.updated_at else None
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话详情失败: {str(e)}")

    @router.post("/sessions")
    async def create_session(session_request: SessionRequest):
        """创建会话"""
        try:
            db = get_database()
            session_id = db.create_session(
                user_id=session_request.user_id,
                workflow_name=session_request.workflow_name,
                input_data=session_request.input_data
            )

            return {
                "session_id": session_id,
                "workflow_name": session_request.workflow_name,
                "status": "created",
                "message": "会话创建成功"
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")

    @router.get("/sessions/{session_id}/tasks")
    async def get_session_tasks(session_id: str):
        """获取会话任务"""
        try:
            db = get_database()
            tasks = db.get_session_tasks(session_id)

            tasks_list = []
            for task in tasks:
                tasks_list.append({
                    "id": task.id,
                    "session_id": task.session_id,
                    "agent_name": task.agent_name,
                    "task_name": task.task_name,
                    "task_description": task.task_description,
                    "status": task.status.value if hasattr(task.status, 'value') else task.status,
                    "input_data": task.input_data,
                    "output_data": task.output_data,
                    "error_message": task.error_message,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "created_at": task.created_at.isoformat() if task.created_at else None
                })

            return {"tasks": tasks_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话任务失败: {str(e)}")

    @router.get("/sessions/{session_id}/messages")
    async def get_session_messages(session_id: str):
        """获取会话消息"""
        try:
            db = get_database()
            messages = db.get_session_messages(session_id)

            messages_list = []
            for message in messages:
                messages_list.append({
                    "id": message.id,
                    "session_id": message.session_id,
                    "sender": message.sender,
                    "receiver": message.receiver,
                    "message_type": message.message_type,
                    "content": message.content,
                    "timestamp": message.timestamp.isoformat() if message.timestamp else None
                })

            return {"messages": messages_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")

    @router.get("/sessions/{session_id}/logs")
    async def get_session_logs(session_id: str, level: Optional[str] = Query(None)):
        """获取会话日志"""
        try:
            db = get_database()
            logs = db.get_session_logs(session_id, level)

            logs_list = []
            for log in logs:
                logs_list.append({
                    "id": log.id,
                    "session_id": log.session_id,
                    "level": log.level.value if hasattr(log.level, 'value') else log.level,
                    "message": log.message,
                    "context": log.context,
                    "timestamp": log.timestamp.isoformat() if log.timestamp else None
                })

            return {"logs": logs_list}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取会话日志失败: {str(e)}")

    # MCP服务管理
    @router.get("/mcp/services")
    async def get_mcp_services():
        """获取MCP服务列表"""
        try:
            mcp_manager = get_mcp_manager()
            services = mcp_manager.list_services()
            return {"services": services}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取MCP服务列表失败: {str(e)}")

    @router.get("/mcp/services/{service_id}")
    async def get_mcp_service(service_id: str):
        """获取MCP服务详情"""
        try:
            mcp_manager = get_mcp_manager()
            service = await mcp_manager.get_service(service_id)

            if not service:
                raise HTTPException(status_code=404, detail=f"MCP服务 {service_id} 未找到")

            return {
                "service_id": service_id,
                "name": service.name,
                "connected": service.client.is_connected
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取MCP服务详情失败: {str(e)}")

    @router.post("/mcp/services/{service_id}/tools/{tool_name}")
    async def call_mcp_tool(service_id: str, tool_name: str, parameters: Dict[str, Any]):
        """调用MCP工具"""
        try:
            mcp_manager = get_mcp_manager()
            result = await mcp_manager.call_service_tool(service_id, tool_name, parameters)
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"调用MCP工具失败: {str(e)}")

    @router.get("/mcp/services/{service_id}/health")
    async def get_mcp_service_health(service_id: str):
        """MCP服务健康检查"""
        try:
            mcp_manager = get_mcp_manager()
            service = await mcp_manager.get_service(service_id)

            if not service:
                raise HTTPException(status_code=404, detail=f"MCP服务 {service_id} 未找到")

            health = await service.health_check()
            return health
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"MCP服务健康检查失败: {str(e)}")

    @router.get("/mcp/health")
    async def get_all_mcp_services_health():
        """所有MCP服务健康检查"""
        try:
            mcp_manager = get_mcp_manager()
            health = await mcp_manager.health_check_all()
            return health
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"MCP服务健康检查失败: {str(e)}")

    return router
