"""
智能体配置
"""

import yaml
import os
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from loguru import logger


class LLMConfig(BaseModel):
    """LLM配置"""
    provider: str = "qwen"
    model: str = "qwen-plus"
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    temperature: float = 0.7
    max_tokens: int = 2000


class AgentConfig(BaseModel):
    """智能体配置"""
    agent_id: str
    name: str
    description: str
    llm: LLMConfig
    prompt_template: str = ""
    capabilities: List[str] = Field(default_factory=list)
    mcp_services: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


def load_agent_configs(config_file: str = "config/agents.yaml") -> Dict[str, AgentConfig]:
    """加载智能体配置"""
    try:
        if not os.path.exists(config_file):
            logger.warning(f"智能体配置文件不存在: {config_file}")
            return create_default_agent_configs()
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        agents_config = {}
        agents_data = config_data.get('agents', {})
        
        for agent_id, agent_data in agents_data.items():
            try:
                # 处理环境变量替换
                if 'llm' in agent_data and 'api_key' in agent_data['llm']:
                    api_key = agent_data['llm']['api_key']
                    if api_key.startswith('${') and api_key.endswith('}'):
                        env_var = api_key[2:-1]
                        agent_data['llm']['api_key'] = os.getenv(env_var, "")
                
                agent_config = AgentConfig(
                    agent_id=agent_id,
                    **agent_data
                )
                agents_config[agent_id] = agent_config
                
            except Exception as e:
                logger.error(f"加载智能体配置失败 {agent_id}: {e}")
        
        logger.info(f"加载了 {len(agents_config)} 个智能体配置")
        return agents_config
        
    except Exception as e:
        logger.error(f"加载智能体配置文件失败: {e}")
        return create_default_agent_configs()


def create_default_agent_configs() -> Dict[str, AgentConfig]:
    """创建默认智能体配置"""
    logger.info("使用默认智能体配置")
    
    # 从环境变量获取API密钥
    qwen_api_key = os.getenv("QWEN_API_KEY", "")
    
    default_llm_config = LLMConfig(
        provider="qwen",
        model="qwen-plus",
        api_key=qwen_api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        temperature=0.7,
        max_tokens=2000
    )
    
    configs = {}
    
    # 意图识别智能体
    configs["intent_recognizer"] = AgentConfig(
        agent_id="intent_recognizer",
        name="意图识别智能体",
        description="专门用于识别用户意图和提取实体信息的智能体",
        llm=default_llm_config.copy(update={"temperature": 0.1}),
        prompt_template="""分析用户输入，识别用户意图。
用户输入：{user_input}
可能的意图类型：{intent_types}
请返回最匹配的意图类型和置信度。""",
        capabilities=["intent_recognition"]
    )
    
    # 任务分解智能体
    configs["task_decomposer"] = AgentConfig(
        agent_id="task_decomposer",
        name="任务分解智能体",
        description="专门用于将复杂任务分解为可执行子任务的智能体",
        llm=default_llm_config.copy(update={"temperature": 0.2}),
        prompt_template="""根据识别的意图，将任务分解为具体的子任务。
意图：{intent}
最大任务数：{max_tasks}
请提供详细的任务分解方案。""",
        capabilities=["task_decomposition"]
    )
    
    # 代码生成智能体
    configs["code_generator"] = AgentConfig(
        agent_id="code_generator",
        name="代码生成智能体",
        description="专门用于生成高质量代码的智能体",
        llm=default_llm_config.copy(update={"temperature": 0.3}),
        prompt_template="""你是一个专业的代码生成专家。
任务描述：{task_description}
编程语言：{language}
请生成高质量的代码。""",
        capabilities=["code_generation"],
        mcp_services=["file_operations", "git_operations"]
    )
    
    # 市场调研智能体
    configs["market_researcher"] = AgentConfig(
        agent_id="market_researcher",
        name="市场调研智能体",
        description="专门用于市场调研和商业分析的智能体",
        llm=default_llm_config.copy(update={"temperature": 0.4}),
        prompt_template="""你是一个专业的市场调研分析师。
调研主题：{research_topic}
调研范围：{research_scope}
目标市场：{target_market}
请提供详细的市场调研报告。""",
        capabilities=["market_research", "data_analysis"],
        mcp_services=["web_search", "data_analysis"]
    )
    
    # 结果验证智能体
    configs["result_validator"] = AgentConfig(
        agent_id="result_validator",
        name="结果验证智能体",
        description="专门用于验证任务执行结果质量的智能体",
        llm=default_llm_config.copy(update={"temperature": 0.1}),
        prompt_template="""你是一个专业的质量验证专家，负责验证任务执行结果是否满足用户需求。
原始需求：{original_requirement}
执行结果：{execution_result}
验证标准：{validation_criteria}
请进行详细的验证分析。""",
        capabilities=["result_validation", "quality_assessment", "requirement_matching"]
    )
    
    return configs


def save_agent_configs(configs: Dict[str, AgentConfig], config_file: str = "config/agents.yaml"):
    """保存智能体配置"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        
        # 转换为YAML格式
        config_data = {"agents": {}}
        for agent_id, config in configs.items():
            config_data["agents"][agent_id] = config.dict(exclude={"agent_id"})
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"智能体配置已保存到: {config_file}")
        
    except Exception as e:
        logger.error(f"保存智能体配置失败: {e}")


def get_agent_config(agent_id: str, configs: Dict[str, AgentConfig]) -> Optional[AgentConfig]:
    """获取指定智能体配置"""
    return configs.get(agent_id)
