"""
智能体配置模块
定义智能体、工作流和MCP服务的配置结构
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class LLMProvider(str, Enum):
    """LLM提供商枚举"""
    QWEN = "qwen"  # 阿里千问（主要使用）
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    ZHIPU = "zhipu"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExecutionMode(str, Enum):
    """执行模式枚举"""
    SEQUENTIAL = "sequential"  # 顺序执行
    PARALLEL = "parallel"      # 并行执行


class AggregationStrategy(str, Enum):
    """并行结果聚合策略枚举"""
    MERGE_ALL = "merge_all"           # 合并所有结果
    SELECT_BEST = "select_best"       # 选择最佳结果
    VOTING = "voting"                 # 投票决策
    WEIGHTED_AVERAGE = "weighted_average"  # 加权平均


class LLMConfig(BaseModel):
    """LLM配置"""
    provider: LLMProvider = Field(default=LLMProvider.QWEN, description="LLM提供商")
    model: str = Field(default="qwen-plus", description="模型名称")
    api_key: str = Field(description="API密钥")
    base_url: str = Field(description="API基础URL")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度参数")
    max_tokens: int = Field(default=2048, gt=0, description="最大输出令牌数")
    max_context_tokens: int = Field(default=128000, gt=0, description="最大上下文令牌数")
    timeout: int = Field(default=60, gt=0, description="请求超时时间（秒）")
    
    class Config:
        use_enum_values = True


class AgentConfig(BaseModel):
    """智能体配置"""
    id: str = Field(description="智能体唯一标识")
    name: str = Field(description="智能体名称")
    description: str = Field(description="智能体描述")
    llm: LLMConfig = Field(description="LLM配置")
    system_prompt: str = Field(description="系统提示词")
    capabilities: List[str] = Field(default_factory=list, description="智能体能力列表")
    mcp_services: List[str] = Field(default_factory=list, description="关联的MCP服务")
    is_active: bool = Field(default=True, description="是否启用")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    
    # A2A协议相关配置
    a2a_skills: List[str] = Field(default_factory=list, description="A2A技能列表")
    a2a_metadata: Dict[str, Any] = Field(default_factory=dict, description="A2A元数据")
    
    class Config:
        use_enum_values = True


class WorkflowStepConfig(BaseModel):
    """工作流步骤配置"""
    step_id: str = Field(description="步骤标识")
    step_type: str = Field(default="agent", description="步骤类型")
    agent_id: Optional[str] = Field(default=None, description="执行的智能体ID")
    condition: Optional[str] = Field(default=None, description="执行条件")
    input_mapping: Dict[str, Any] = Field(default_factory=dict, description="输入映射")
    output_mapping: Dict[str, Any] = Field(default_factory=dict, description="输出映射")
    
    # 并行执行配置
    execution_mode: ExecutionMode = Field(default=ExecutionMode.SEQUENTIAL, description="执行模式")
    parallel_agents: List[Dict[str, Any]] = Field(default_factory=list, description="并行智能体配置")
    aggregation_strategy: AggregationStrategy = Field(
        default=AggregationStrategy.MERGE_ALL, 
        description="并行结果聚合策略"
    )
    parallel_timeout: int = Field(default=120, description="并行执行超时时间（秒）")
    
    # 分支和循环配置
    branches: Dict[str, List[Dict[str, Any]]] = Field(default_factory=dict, description="分支配置")
    loop_target: Optional[str] = Field(default=None, description="循环目标步骤")
    input_override: Dict[str, Any] = Field(default_factory=dict, description="输入覆盖")
    
    class Config:
        use_enum_values = True


class WorkflowConfig(BaseModel):
    """工作流配置"""
    id: str = Field(description="工作流唯一标识")
    name: str = Field(description="工作流名称")
    description: str = Field(description="工作流描述")
    steps: List[WorkflowStepConfig] = Field(description="工作流步骤")
    max_iterations: int = Field(default=3, description="最大循环迭代次数")
    timeout: int = Field(default=300, description="工作流超时时间（秒）")
    is_active: bool = Field(default=True, description="是否启用")
    
    # A2A协议相关配置
    a2a_skills: List[str] = Field(default_factory=list, description="工作流提供的A2A技能")
    a2a_metadata: Dict[str, Any] = Field(default_factory=dict, description="A2A元数据")


class MCPServiceConfig(BaseModel):
    """MCP服务配置"""
    id: str = Field(description="MCP服务唯一标识")
    name: str = Field(description="MCP服务名称")
    description: str = Field(description="MCP服务描述")
    type: str = Field(description="MCP服务类型（stdio/http/websocket）")
    config: Dict[str, Any] = Field(description="MCP服务配置")
    capabilities: List[str] = Field(description="MCP服务能力列表")
    is_active: bool = Field(default=True, description="是否启用")
    timeout: int = Field(default=30, description="服务超时时间（秒）")


class SystemConfig(BaseModel):
    """系统配置"""
    agents: Dict[str, AgentConfig] = Field(default_factory=dict, description="智能体配置")
    workflows: Dict[str, WorkflowConfig] = Field(default_factory=dict, description="工作流配置")
    mcp_services: Dict[str, MCPServiceConfig] = Field(default_factory=dict, description="MCP服务配置")
    
    # 全局配置
    max_concurrent_tasks: int = Field(default=10, description="最大并发任务数")
    default_timeout: int = Field(default=300, description="默认超时时间（秒）")
    enable_metrics: bool = Field(default=True, description="是否启用指标监控")
    enable_logging: bool = Field(default=True, description="是否启用日志记录")


def create_default_qwen_agent_config(
    agent_id: str,
    name: str,
    description: str,
    system_prompt: str,
    api_key: str,
    capabilities: List[str] = None,
    mcp_services: List[str] = None,
    a2a_skills: List[str] = None
) -> AgentConfig:
    """创建默认的千问智能体配置"""
    return AgentConfig(
        id=agent_id,
        name=name,
        description=description,
        llm=LLMConfig(
            provider=LLMProvider.QWEN,
            model="qwen-plus",
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            temperature=0.7,
            max_tokens=2048,
            max_context_tokens=128000,
            timeout=60
        ),
        system_prompt=system_prompt,
        capabilities=capabilities or [],
        mcp_services=mcp_services or [],
        a2a_skills=a2a_skills or [],
        is_active=True,
        max_retries=3,
        retry_delay=1.0
    )


def create_default_workflow_config(
    workflow_id: str,
    name: str,
    description: str,
    steps: List[WorkflowStepConfig],
    a2a_skills: List[str] = None
) -> WorkflowConfig:
    """创建默认的工作流配置"""
    return WorkflowConfig(
        id=workflow_id,
        name=name,
        description=description,
        steps=steps,
        max_iterations=3,
        timeout=300,
        is_active=True,
        a2a_skills=a2a_skills or []
    )
