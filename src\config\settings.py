"""
系统设置配置
基于Pydantic Settings的配置管理
"""

from typing import Optional, Dict, Any
from pydantic import BaseSettings, Field
from functools import lru_cache
import os


class Settings(BaseSettings):
    """系统配置类"""
    
    # ==================== 基础配置 ====================
    app_name: str = Field(default="A2A多智能体协作系统", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    environment: str = Field(default="development", description="运行环境")
    
    # ==================== 服务器配置 ====================
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")
    
    # ==================== 数据库配置 ====================
    database_url: str = Field(
        default="mysql+pymysql://root:password@localhost:3306/a2a",
        description="数据库连接URL"
    )
    database_echo: bool = Field(default=False, description="数据库SQL日志")
    database_pool_size: int = Field(default=10, description="数据库连接池大小")
    database_max_overflow: int = Field(default=20, description="数据库连接池最大溢出")
    
    # ==================== Redis配置 ====================
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    
    # ==================== 阿里千问配置 ====================
    qwen_api_key: str = Field(default="", description="阿里千问API密钥")
    qwen_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        description="阿里千问API基础URL"
    )
    qwen_model: str = Field(default="qwen-plus", description="默认使用的千问模型")
    qwen_temperature: float = Field(default=0.7, description="千问模型温度")
    qwen_max_tokens: int = Field(default=2048, description="千问模型最大输出令牌")
    
    # ==================== 其他LLM配置（备用） ====================
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL")
    
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API密钥")
    
    google_api_key: Optional[str] = Field(default=None, description="Google API密钥")
    
    zhipu_api_key: Optional[str] = Field(default=None, description="智谱AI API密钥")
    
    # ==================== A2A协议配置 ====================
    a2a_server_name: str = Field(default="A2A多智能体系统", description="A2A服务器名称")
    a2a_server_description: str = Field(
        default="基于Google A2A协议的多智能体协作系统",
        description="A2A服务器描述"
    )
    a2a_server_version: str = Field(default="1.0.0", description="A2A服务器版本")
    a2a_max_concurrent_tasks: int = Field(default=10, description="最大并发任务数")
    
    # ==================== 安全配置 ====================
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT密钥"
    )
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间（分钟）")
    
    # ==================== 日志配置 ====================
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    log_rotation: str = Field(default="1 day", description="日志轮转")
    log_retention: str = Field(default="30 days", description="日志保留时间")
    
    # ==================== 工作流配置 ====================
    max_workflow_iterations: int = Field(default=3, description="工作流最大迭代次数")
    workflow_timeout: int = Field(default=300, description="工作流超时时间（秒）")
    task_timeout: int = Field(default=120, description="单个任务超时时间（秒）")
    
    # ==================== MCP服务配置 ====================
    mcp_enabled: bool = Field(default=True, description="是否启用MCP服务")
    mcp_timeout: int = Field(default=30, description="MCP服务超时时间（秒）")
    
    # ==================== 监控配置 ====================
    metrics_enabled: bool = Field(default=True, description="是否启用指标监控")
    metrics_port: int = Field(default=9090, description="指标监控端口")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # 环境变量前缀
        env_prefix = "A2A_"


@lru_cache()
def get_settings() -> Settings:
    """获取系统设置（单例模式）"""
    return Settings()


def get_qwen_config() -> Dict[str, Any]:
    """获取阿里千问配置"""
    settings = get_settings()
    return {
        "api_key": settings.qwen_api_key,
        "base_url": settings.qwen_base_url,
        "model": settings.qwen_model,
        "temperature": settings.qwen_temperature,
        "max_tokens": settings.qwen_max_tokens,
    }


def get_database_config() -> Dict[str, Any]:
    """获取数据库配置"""
    settings = get_settings()
    return {
        "url": settings.database_url,
        "echo": settings.database_echo,
        "pool_size": settings.database_pool_size,
        "max_overflow": settings.database_max_overflow,
    }


def get_a2a_config() -> Dict[str, Any]:
    """获取A2A协议配置"""
    settings = get_settings()
    return {
        "server_name": settings.a2a_server_name,
        "server_description": settings.a2a_server_description,
        "server_version": settings.a2a_server_version,
        "max_concurrent_tasks": settings.a2a_max_concurrent_tasks,
        "host": settings.host,
        "port": settings.port,
    }
