"""
系统设置
"""

import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from loguru import logger


class Settings(BaseModel):
    """系统设置"""
    
    # 基本设置
    app_name: str = "A2A多智能体协作系统"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器设置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库设置
    database_url: str = "sqlite:///a2a_system.db"
    
    # 千问API设置
    qwen_api_key: str = Field(default="", env="QWEN_API_KEY")
    qwen_base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    qwen_model: str = "qwen-plus"
    qwen_temperature: float = 0.4
    qwen_max_tokens: int = 2000
    
    # 日志设置
    log_level: str = "INFO"
    log_file: Optional[str] = "logs/a2a_system.log"
    
    # 工作流设置
    max_workflow_execution_time: int = 3600  # 秒
    max_parallel_tasks: int = 10
    default_retry_count: int = 3
    default_retry_delay: int = 1
    
    # 安全设置
    secret_key: str = Field(default="", env="SECRET_KEY")
    cors_origins: list = ["*"]
    
    # 功能开关
    enable_web_ui: bool = True
    enable_api_docs: bool = True
    enable_metrics: bool = True
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 验证必需的配置
        if not self.qwen_api_key:
            logger.warning("千问API密钥未设置，请设置环境变量 QWEN_API_KEY")
        
        if not self.secret_key:
            # 生成默认密钥
            import secrets
            self.secret_key = secrets.token_urlsafe(32)
            logger.warning("未设置SECRET_KEY，使用随机生成的密钥")
    
    def get_qwen_config(self) -> Dict[str, Any]:
        """获取千问配置"""
        return {
            "api_key": self.qwen_api_key,
            "base_url": self.qwen_base_url,
            "model": self.qwen_model,
            "temperature": self.qwen_temperature,
            "max_tokens": self.qwen_max_tokens
        }
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            "url": self.database_url
        }
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return {
            "host": self.host,
            "port": self.port,
            "debug": self.debug
        }


# 全局设置实例
_settings_instance = None


def get_settings() -> Settings:
    """获取设置实例"""
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = Settings()
        logger.info("系统设置加载完成")
    return _settings_instance
