"""
数据库实现
提供数据持久化功能
"""

import json
import sqlite3
from typing import Dict, Any, List, Optional
from loguru import logger
from datetime import datetime
import os

from .models import Session, Task, Message, Log, SessionStatus, TaskStatus, LogLevel


class Database:
    """数据库类"""
    
    def __init__(self, db_path: str = "a2a_system.db"):
        """初始化数据库"""
        self.db_path = db_path
        self.init_database()
        logger.info(f"数据库初始化完成: {db_path}")
    
    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建会话表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    workflow_name TEXT NOT NULL,
                    status TEXT NOT NULL,
                    input_data TEXT,
                    output_data TEXT,
                    context TEXT,
                    error_message TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT
                )
            """)
            
            # 创建任务表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    agent_name TEXT NOT NULL,
                    task_name TEXT NOT NULL,
                    task_description TEXT,
                    status TEXT NOT NULL,
                    input_data TEXT,
                    output_data TEXT,
                    error_message TEXT,
                    progress REAL DEFAULT 0.0,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id)
                )
            """)
            
            # 创建消息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    sender TEXT NOT NULL,
                    receiver TEXT NOT NULL,
                    message_type TEXT NOT NULL,
                    content TEXT,
                    timestamp TEXT NOT NULL,
                    FOREIGN KEY (session_id) REFERENCES sessions (id)
                )
            """)
            
            # 创建日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS logs (
                    id TEXT PRIMARY KEY,
                    session_id TEXT,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    context TEXT,
                    timestamp TEXT NOT NULL
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_created_at ON sessions (created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_session_id ON tasks (session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages (session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_session_id ON logs (session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_logs_level ON logs (level)")
            
            conn.commit()
    
    def create_session(
        self, 
        user_id: Optional[str], 
        workflow_name: str, 
        input_data: Dict[str, Any]
    ) -> str:
        """创建会话"""
        session = Session(
            user_id=user_id,
            workflow_name=workflow_name,
            input_data=input_data
        )
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO sessions (
                    id, user_id, workflow_name, status, input_data, 
                    context, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session.id,
                session.user_id,
                session.workflow_name,
                session.status.value,
                json.dumps(session.input_data),
                json.dumps(session.context),
                session.created_at.isoformat(),
                session.updated_at.isoformat()
            ))
            conn.commit()
        
        logger.info(f"创建会话: {session.id}")
        return session.id
    
    def get_session_by_id(self, session_id: str) -> Optional[Session]:
        """根据ID获取会话"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM sessions WHERE id = ?", (session_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_session(row)
            return None
    
    def list_sessions(
        self, 
        status: Optional[str] = None, 
        limit: int = 100, 
        offset: int = 0
    ) -> List[Session]:
        """列出会话"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if status:
                cursor.execute("""
                    SELECT * FROM sessions WHERE status = ? 
                    ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (status, limit, offset))
            else:
                cursor.execute("""
                    SELECT * FROM sessions 
                    ORDER BY created_at DESC LIMIT ? OFFSET ?
                """, (limit, offset))
            
            rows = cursor.fetchall()
            return [self._row_to_session(row) for row in rows]
    
    def update_session_status(
        self, 
        session_id: str, 
        status: str, 
        output_data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ):
        """更新会话状态"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            update_fields = ["status = ?", "updated_at = ?"]
            params = [status, datetime.utcnow().isoformat()]
            
            if output_data is not None:
                update_fields.append("output_data = ?")
                params.append(json.dumps(output_data))
            
            if error_message is not None:
                update_fields.append("error_message = ?")
                params.append(error_message)
            
            if status == SessionStatus.RUNNING.value:
                update_fields.append("started_at = ?")
                params.append(datetime.utcnow().isoformat())
            elif status in [SessionStatus.COMPLETED.value, SessionStatus.FAILED.value]:
                update_fields.append("completed_at = ?")
                params.append(datetime.utcnow().isoformat())
            
            params.append(session_id)
            
            cursor.execute(f"""
                UPDATE sessions SET {', '.join(update_fields)} WHERE id = ?
            """, params)
            conn.commit()
        
        logger.debug(f"更新会话状态: {session_id} -> {status}")
    
    def create_task(
        self,
        session_id: str,
        agent_name: str,
        task_name: str,
        task_description: Optional[str] = None,
        input_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建任务"""
        task = Task(
            session_id=session_id,
            agent_name=agent_name,
            task_name=task_name,
            task_description=task_description,
            input_data=input_data or {}
        )
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO tasks (
                    id, session_id, agent_name, task_name, task_description,
                    status, input_data, progress, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.id,
                task.session_id,
                task.agent_name,
                task.task_name,
                task.task_description,
                task.status.value,
                json.dumps(task.input_data),
                task.progress,
                task.created_at.isoformat()
            ))
            conn.commit()
        
        logger.debug(f"创建任务: {task.id}")
        return task.id
    
    def get_session_tasks(self, session_id: str) -> List[Task]:
        """获取会话任务"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM tasks WHERE session_id = ? ORDER BY created_at
            """, (session_id,))
            rows = cursor.fetchall()
            return [self._row_to_task(row) for row in rows]
    
    def create_message(
        self,
        session_id: str,
        sender: str,
        receiver: str,
        message_type: str,
        content: Dict[str, Any]
    ) -> str:
        """创建消息"""
        message = Message(
            session_id=session_id,
            sender=sender,
            receiver=receiver,
            message_type=message_type,
            content=content
        )
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO messages (
                    id, session_id, sender, receiver, message_type, content, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                message.id,
                message.session_id,
                message.sender,
                message.receiver,
                message.message_type,
                json.dumps(message.content),
                message.timestamp.isoformat()
            ))
            conn.commit()
        
        return message.id
    
    def get_session_messages(self, session_id: str) -> List[Message]:
        """获取会话消息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM messages WHERE session_id = ? ORDER BY timestamp
            """, (session_id,))
            rows = cursor.fetchall()
            return [self._row_to_message(row) for row in rows]
    
    def create_log(
        self,
        level: LogLevel,
        message: str,
        session_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建日志"""
        log = Log(
            session_id=session_id,
            level=level,
            message=message,
            context=context
        )
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO logs (
                    id, session_id, level, message, context, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                log.id,
                log.session_id,
                log.level.value,
                log.message,
                json.dumps(log.context) if log.context else None,
                log.timestamp.isoformat()
            ))
            conn.commit()
        
        return log.id
    
    def get_session_logs(self, session_id: str, level: Optional[str] = None) -> List[Log]:
        """获取会话日志"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if level:
                cursor.execute("""
                    SELECT * FROM logs WHERE session_id = ? AND level = ? ORDER BY timestamp
                """, (session_id, level))
            else:
                cursor.execute("""
                    SELECT * FROM logs WHERE session_id = ? ORDER BY timestamp
                """, (session_id,))
            
            rows = cursor.fetchall()
            return [self._row_to_log(row) for row in rows]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 会话统计
            cursor.execute("SELECT COUNT(*) FROM sessions")
            total_sessions = cursor.fetchone()[0]
            
            cursor.execute("SELECT status, COUNT(*) FROM sessions GROUP BY status")
            session_status_counts = dict(cursor.fetchall())
            
            # 任务统计
            cursor.execute("SELECT COUNT(*) FROM tasks")
            total_tasks = cursor.fetchone()[0]
            
            cursor.execute("SELECT status, COUNT(*) FROM tasks GROUP BY status")
            task_status_counts = dict(cursor.fetchall())
            
            # 消息统计
            cursor.execute("SELECT COUNT(*) FROM messages")
            total_messages = cursor.fetchone()[0]
            
            # 日志统计
            cursor.execute("SELECT level, COUNT(*) FROM logs GROUP BY level")
            log_level_counts = dict(cursor.fetchall())
            
            return {
                "total_sessions": total_sessions,
                "session_status_counts": session_status_counts,
                "total_tasks": total_tasks,
                "task_status_counts": task_status_counts,
                "total_messages": total_messages,
                "log_level_counts": log_level_counts
            }
    
    def _row_to_session(self, row) -> Session:
        """将数据库行转换为Session对象"""
        return Session(
            id=row[0],
            user_id=row[1],
            workflow_name=row[2],
            status=SessionStatus(row[3]),
            input_data=json.loads(row[4]) if row[4] else {},
            output_data=json.loads(row[5]) if row[5] else None,
            context=json.loads(row[6]) if row[6] else {},
            error_message=row[7],
            created_at=datetime.fromisoformat(row[8]),
            updated_at=datetime.fromisoformat(row[9]),
            started_at=datetime.fromisoformat(row[10]) if row[10] else None,
            completed_at=datetime.fromisoformat(row[11]) if row[11] else None
        )
    
    def _row_to_task(self, row) -> Task:
        """将数据库行转换为Task对象"""
        return Task(
            id=row[0],
            session_id=row[1],
            agent_name=row[2],
            task_name=row[3],
            task_description=row[4],
            status=TaskStatus(row[5]),
            input_data=json.loads(row[6]) if row[6] else {},
            output_data=json.loads(row[7]) if row[7] else None,
            error_message=row[8],
            progress=row[9],
            created_at=datetime.fromisoformat(row[10]),
            started_at=datetime.fromisoformat(row[11]) if row[11] else None,
            completed_at=datetime.fromisoformat(row[12]) if row[12] else None
        )
    
    def _row_to_message(self, row) -> Message:
        """将数据库行转换为Message对象"""
        return Message(
            id=row[0],
            session_id=row[1],
            sender=row[2],
            receiver=row[3],
            message_type=row[4],
            content=json.loads(row[5]) if row[5] else {},
            timestamp=datetime.fromisoformat(row[6])
        )
    
    def _row_to_log(self, row) -> Log:
        """将数据库行转换为Log对象"""
        return Log(
            id=row[0],
            session_id=row[1],
            level=LogLevel(row[2]),
            message=row[3],
            context=json.loads(row[4]) if row[4] else None,
            timestamp=datetime.fromisoformat(row[5])
        )


# 全局数据库实例
_database_instance = None


def get_database() -> Database:
    """获取数据库实例"""
    global _database_instance
    if _database_instance is None:
        _database_instance = Database()
    return _database_instance
