"""
数据库管理器
处理数据库连接和操作
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session as SQLSession
from sqlalchemy.pool import StaticPool
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from loguru import logger

from .models import Base, Session, Task, AgentMessage, WorkflowLog
from ..config.settings import get_database_config


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.engine = None
        self.SessionLocal = None
        self._initialized = False
    
    def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        try:
            db_config = get_database_config()
            
            # 创建数据库引擎
            self.engine = create_engine(
                db_config["url"],
                echo=db_config["echo"],
                pool_size=db_config["pool_size"],
                max_overflow=db_config["max_overflow"],
                # 对于SQLite，添加特殊配置
                poolclass=StaticPool if "sqlite" in db_config["url"] else None,
                connect_args={"check_same_thread": False} if "sqlite" in db_config["url"] else {}
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            
            self._initialized = True
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话（上下文管理器）"""
        if not self._initialized:
            self.initialize()
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def create_session(self, user_id: Optional[str], workflow_name: str, input_data: Dict[str, Any]) -> str:
        """创建新会话"""
        with self.get_session() as db:
            session = Session(
                user_id=user_id,
                workflow_name=workflow_name,
                input_data=input_data
            )
            db.add(session)
            db.flush()  # 获取生成的ID
            session_id = session.id
            logger.info(f"创建新会话: {session_id}")
            return session_id
    
    def get_session_by_id(self, session_id: str) -> Optional[Session]:
        """根据ID获取会话"""
        with self.get_session() as db:
            return db.query(Session).filter(Session.id == session_id).first()
    
    def update_session_status(self, session_id: str, status: str, output_data: Optional[Dict[str, Any]] = None):
        """更新会话状态"""
        with self.get_session() as db:
            session = db.query(Session).filter(Session.id == session_id).first()
            if session:
                session.status = status
                if output_data:
                    session.output_data = output_data
                logger.info(f"更新会话 {session_id} 状态为: {status}")
    
    def create_task(self, session_id: str, agent_name: str, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        with self.get_session() as db:
            task = Task(
                session_id=session_id,
                agent_name=agent_name,
                task_name=task_data.get("task_name"),
                task_description=task_data.get("task_description"),
                input_data=task_data.get("input_data"),
                execution_mode=task_data.get("execution_mode"),
                parallel_agents=task_data.get("parallel_agents"),
                aggregation_strategy=task_data.get("aggregation_strategy"),
                parallel_timeout=task_data.get("parallel_timeout")
            )
            db.add(task)
            db.flush()
            task_id = task.id
            logger.info(f"创建新任务: {task_id}")
            return task_id
    
    def update_task_status(self, task_id: str, status: str, output_data: Optional[Dict[str, Any]] = None, error_message: Optional[str] = None):
        """更新任务状态"""
        with self.get_session() as db:
            task = db.query(Task).filter(Task.id == task_id).first()
            if task:
                task.status = status
                if output_data:
                    task.output_data = output_data
                if error_message:
                    task.error_message = error_message
                if status == "running" and not task.started_at:
                    task.started_at = datetime.utcnow()
                elif status in ["completed", "failed"]:
                    task.completed_at = datetime.utcnow()
                logger.info(f"更新任务 {task_id} 状态为: {status}")
    
    def create_agent_message(self, session_id: str, sender: str, receiver: str, message_type: str, content: Dict[str, Any]) -> str:
        """创建智能体消息"""
        with self.get_session() as db:
            message = AgentMessage(
                session_id=session_id,
                sender=sender,
                receiver=receiver,
                message_type=message_type,
                content=content
            )
            db.add(message)
            db.flush()
            message_id = message.id
            logger.debug(f"创建智能体消息: {sender} -> {receiver}")
            return message_id
    
    def create_workflow_log(self, session_id: str, level: str, message: str, context: Optional[Dict[str, Any]] = None):
        """创建工作流日志"""
        with self.get_session() as db:
            log = WorkflowLog(
                session_id=session_id,
                level=level,
                message=message,
                context=context
            )
            db.add(log)
            logger.debug(f"创建工作流日志: [{level}] {message}")
    
    def get_session_tasks(self, session_id: str) -> List[Task]:
        """获取会话的所有任务"""
        with self.get_session() as db:
            return db.query(Task).filter(Task.session_id == session_id).order_by(Task.created_at).all()
    
    def get_session_messages(self, session_id: str) -> List[AgentMessage]:
        """获取会话的所有消息"""
        with self.get_session() as db:
            return db.query(AgentMessage).filter(AgentMessage.session_id == session_id).order_by(AgentMessage.timestamp).all()
    
    def get_session_logs(self, session_id: str, level: Optional[str] = None) -> List[WorkflowLog]:
        """获取会话的所有日志"""
        with self.get_session() as db:
            query = db.query(WorkflowLog).filter(WorkflowLog.session_id == session_id)
            if level:
                query = query.filter(WorkflowLog.level == level)
            return query.order_by(WorkflowLog.timestamp).all()
    
    def list_sessions(self, user_id: Optional[str] = None, status: Optional[str] = None, limit: int = 100) -> List[Session]:
        """列出会话"""
        with self.get_session() as db:
            query = db.query(Session)
            if user_id:
                query = query.filter(Session.user_id == user_id)
            if status:
                query = query.filter(Session.status == status)
            return query.order_by(Session.created_at.desc()).limit(limit).all()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        with self.get_session() as db:
            total_sessions = db.query(Session).count()
            total_tasks = db.query(Task).count()
            total_messages = db.query(AgentMessage).count()
            total_logs = db.query(WorkflowLog).count()
            
            # 按状态统计会话
            session_stats = {}
            for status in ["pending", "running", "completed", "failed"]:
                count = db.query(Session).filter(Session.status == status).count()
                session_stats[status] = count
            
            # 按状态统计任务
            task_stats = {}
            for status in ["pending", "running", "completed", "failed"]:
                count = db.query(Task).filter(Task.status == status).count()
                task_stats[status] = count
            
            return {
                "total_sessions": total_sessions,
                "total_tasks": total_tasks,
                "total_messages": total_messages,
                "total_logs": total_logs,
                "session_stats": session_stats,
                "task_stats": task_stats
            }
    
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        with self.get_session() as db:
            # 删除旧的已完成会话
            old_sessions = db.query(Session).filter(
                Session.status.in_(["completed", "failed"]),
                Session.created_at < cutoff_date
            ).all()
            
            for session in old_sessions:
                db.delete(session)  # 级联删除相关的任务、消息和日志
            
            deleted_count = len(old_sessions)
            logger.info(f"清理了 {deleted_count} 个旧会话数据")
            return deleted_count


# 全局数据库管理器实例
_database_manager = None


def get_database() -> DatabaseManager:
    """获取数据库管理器实例（单例模式）"""
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
        _database_manager.initialize()
    return _database_manager
