"""
数据库模型
基于design_document.md中的数据库设计
"""

from sqlalchemy import Column, String, Text, JSON, Enum, TIMESTAMP, ForeignKey, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
import uuid


Base = declarative_base()


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


class LogLevel(enum.Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class Session(Base):
    """会话表 - 记录工作流执行会话"""
    __tablename__ = "sessions"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(100), nullable=True)
    workflow_name = Column(String(100), nullable=False)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    input_data = Column(JSON, nullable=True)
    output_data = Column(JSON, nullable=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    tasks = relationship("Task", back_populates="session", cascade="all, delete-orphan")
    messages = relationship("AgentMessage", back_populates="session", cascade="all, delete-orphan")
    logs = relationship("WorkflowLog", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Session(id='{self.id}', workflow='{self.workflow_name}', status='{self.status}')>"


class Task(Base):
    """任务表 - 记录具体的智能体任务执行"""
    __tablename__ = "tasks"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    agent_name = Column(String(100), nullable=False)
    task_name = Column(String(200), nullable=True)
    task_description = Column(Text, nullable=True)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING)
    input_data = Column(JSON, nullable=True)
    output_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    started_at = Column(TIMESTAMP, nullable=True)
    completed_at = Column(TIMESTAMP, nullable=True)
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 工作流执行模式相关字段
    execution_mode = Column(String(50), nullable=True)  # sequential, parallel, loop, branch
    parallel_agents = Column(JSON, nullable=True)  # 并行智能体配置
    aggregation_strategy = Column(String(50), nullable=True)  # 聚合策略
    parallel_timeout = Column(BigInteger, nullable=True)  # 并行超时时间
    
    # 关系
    session = relationship("Session", back_populates="tasks")
    
    def __repr__(self):
        return f"<Task(id='{self.id}', agent='{self.agent_name}', status='{self.status}')>"


class AgentMessage(Base):
    """智能体消息表 - 记录智能体间的通信消息"""
    __tablename__ = "agent_messages"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    sender = Column(String(100), nullable=False)
    receiver = Column(String(100), nullable=False)
    message_type = Column(String(50), nullable=False)
    content = Column(JSON, nullable=True)
    timestamp = Column(TIMESTAMP, default=datetime.utcnow)
    
    # 关系
    session = relationship("Session", back_populates="messages")
    
    def __repr__(self):
        return f"<AgentMessage(id='{self.id}', sender='{self.sender}', receiver='{self.receiver}')>"


class WorkflowLog(Base):
    """工作流执行日志表 - 记录工作流执行过程的详细日志"""
    __tablename__ = "workflow_logs"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    level = Column(Enum(LogLevel), default=LogLevel.INFO)
    message = Column(Text, nullable=False)
    context = Column(JSON, nullable=True)
    timestamp = Column(TIMESTAMP, default=datetime.utcnow)
    
    # 关系
    session = relationship("Session", back_populates="logs")
    
    def __repr__(self):
        return f"<WorkflowLog(id='{self.id}', level='{self.level}', session='{self.session_id}')>"


# 扩展模型：工作流定义表（用于持久化工作流配置）
class WorkflowDefinitionModel(Base):
    """工作流定义表 - 持久化工作流配置"""
    __tablename__ = "workflow_definitions"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    workflow_id = Column(String(100), nullable=False, unique=True)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    version = Column(String(20), default="1.0.0")
    definition = Column(JSON, nullable=False)  # 完整的工作流定义JSON
    is_active = Column(String(10), default="true")  # 使用字符串而不是布尔值
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<WorkflowDefinition(id='{self.workflow_id}', name='{self.name}')>"


# 扩展模型：智能体配置表
class AgentConfigModel(Base):
    """智能体配置表 - 持久化智能体配置"""
    __tablename__ = "agent_configs"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = Column(String(100), nullable=False, unique=True)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    config = Column(JSON, nullable=False)  # 完整的智能体配置JSON
    is_active = Column(String(10), default="true")
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<AgentConfig(id='{self.agent_id}', name='{self.name}')>"


# 扩展模型：MCP服务配置表
class MCPServiceModel(Base):
    """MCP服务配置表 - 持久化MCP服务配置"""
    __tablename__ = "mcp_services"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    service_id = Column(String(100), nullable=False, unique=True)
    name = Column(String(200), nullable=False)
    service_type = Column(String(50), nullable=False)  # stdio, http, websocket
    config = Column(JSON, nullable=False)  # 完整的服务配置JSON
    capabilities = Column(JSON, nullable=True)  # 服务能力列表
    is_active = Column(String(10), default="true")
    created_at = Column(TIMESTAMP, default=datetime.utcnow)
    updated_at = Column(TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<MCPService(id='{self.service_id}', name='{self.name}')>"
