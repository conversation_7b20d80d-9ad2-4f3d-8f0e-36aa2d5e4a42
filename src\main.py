"""
A2A多智能体协作系统主程序
基于Google A2A协议和阿里千问的多智能体系统
"""

import asyncio
import os
from typing import Dict, Any
import uvicorn
from loguru import logger

from .config.settings import get_settings, get_qwen_config
from .config.agent_config import create_default_qwen_agent_config
from .a2a.server import A2AServer
from .agents.intent_recognizer import create_intent_recognizer_agent
from .agents.task_decomposer import create_task_decomposer_agent
from .workflow.engine import workflow_engine
from .mcp.manager import mcp_manager, create_default_mcp_services
from .database.database import get_database


class A2AApplication:
    """A2A多智能体应用程序"""
    
    def __init__(self):
        """初始化应用程序"""
        self.settings = get_settings()
        self.qwen_config = get_qwen_config()
        
        # 检查千问API密钥
        if not self.qwen_config["api_key"]:
            raise ValueError("请设置阿里千问API密钥 (A2A_QWEN_API_KEY)")
        
        # 创建A2A服务器
        self.server = A2AServer()
        
        # 初始化智能体
        self.agents = {}
        
        logger.info("A2A多智能体应用程序初始化完成")
    
    def _create_default_agents(self):
        """创建默认智能体"""
        api_key = self.qwen_config["api_key"]
        
        try:
            # 创建意图识别智能体
            intent_agent = create_intent_recognizer_agent(api_key)
            self.agents["intent_recognizer"] = intent_agent
            self.server.register_agent(intent_agent)
            
            # 创建任务分解智能体
            task_agent = create_task_decomposer_agent(api_key)
            self.agents["task_decomposer"] = task_agent
            self.server.register_agent(task_agent)
            
            # 创建代码生成智能体
            code_agent_config = create_default_qwen_agent_config(
                agent_id="code_generator",
                name="代码生成智能体",
                description="专业的代码生成智能体，基于阿里千问Plus模型",
                system_prompt="""你是一个专业的代码生成专家。你能够：
1. 根据需求生成高质量的代码
2. 支持多种编程语言
3. 遵循最佳实践和编码规范
4. 提供详细的代码注释
5. 考虑代码的可维护性和性能

请始终生成清晰、可读、高质量的代码。""",
                api_key=api_key,
                capabilities=["code_generation", "programming", "software_development"],
                a2a_skills=["generate_code", "review_code", "explain_code"]
            )
            
            from .agents.qwen_agent import QwenAgent
            code_agent = QwenAgent(code_agent_config)
            self.agents["code_generator"] = code_agent
            self.server.register_agent(code_agent)
            
            # 创建市场调研智能体
            market_agent_config = create_default_qwen_agent_config(
                agent_id="market_researcher",
                name="市场调研智能体",
                description="专业的市场调研智能体，基于阿里千问Plus模型",
                system_prompt="""你是一个专业的市场调研分析师。你能够：
1. 进行深入的市场分析
2. 分析竞争格局和趋势
3. 提供数据驱动的洞察
4. 生成专业的调研报告
5. 识别市场机会和风险

请提供准确、全面、有价值的市场分析。""",
                api_key=api_key,
                capabilities=["market_research", "competitive_analysis", "trend_analysis"],
                a2a_skills=["research_market", "analyze_competition", "generate_report"]
            )
            
            market_agent = QwenAgent(market_agent_config)
            self.agents["market_researcher"] = market_agent
            self.server.register_agent(market_agent)
            
            # 创建结果验证智能体
            validator_agent_config = create_default_qwen_agent_config(
                agent_id="result_validator",
                name="结果验证智能体",
                description="专业的结果验证智能体，基于阿里千问Plus模型",
                system_prompt="""你是一个专业的质量验证专家。你能够：
1. 验证任务执行结果的完整性和准确性
2. 评估结果质量和可用性
3. 识别潜在问题和改进点
4. 提供详细的验证报告
5. 给出改进建议

请进行严格、客观、专业的质量验证。""",
                api_key=api_key,
                capabilities=["result_validation", "quality_assessment", "error_detection"],
                a2a_skills=["validate_result", "assess_quality", "provide_feedback"]
            )
            
            validator_agent = QwenAgent(validator_agent_config)
            self.agents["result_validator"] = validator_agent
            self.server.register_agent(validator_agent)
            
            logger.info(f"成功创建 {len(self.agents)} 个默认智能体")

        except Exception as e:
            logger.error(f"创建默认智能体失败: {str(e)}")
            raise

    async def _initialize_mcp_services(self):
        """初始化MCP服务"""
        try:
            # 注册默认MCP服务
            default_services = create_default_mcp_services()
            for service_config in default_services:
                if service_config.get("is_active", True):
                    mcp_manager.register_service(service_config)

            # 初始化所有服务
            await mcp_manager.initialize_all_services()

            logger.info(f"成功初始化 {len(mcp_manager.services)} 个MCP服务")

        except Exception as e:
            logger.error(f"初始化MCP服务失败: {str(e)}")
            # MCP服务失败不应该阻止应用启动
            pass
    
    async def startup(self):
        """启动应用程序"""
        try:
            # 初始化数据库
            database = get_database()
            logger.info("数据库初始化完成")

            # 创建默认智能体
            self._create_default_agents()

            # 注册智能体到工作流引擎
            for agent_id, agent in self.agents.items():
                workflow_engine.register_agent(agent)

            # 初始化MCP服务
            await self._initialize_mcp_services()

            # 启动A2A服务器
            await self.server.start()

            logger.info("A2A多智能体应用程序启动成功")

            # 打印智能体信息
            logger.info("已注册的智能体:")
            for agent_id, agent in self.agents.items():
                logger.info(f"  - {agent.name} ({agent_id}): {len(agent.a2a_skills)} 个技能")

        except Exception as e:
            logger.error(f"应用程序启动失败: {str(e)}")
            raise
    
    async def shutdown(self):
        """关闭应用程序"""
        try:
            # 清理MCP服务
            await mcp_manager.cleanup_all_services()

            # 停止A2A服务器
            await self.server.stop()

            logger.info("A2A多智能体应用程序已关闭")

        except Exception as e:
            logger.error(f"应用程序关闭失败: {str(e)}")
    
    def get_fastapi_app(self):
        """获取FastAPI应用实例"""
        return self.server.app


# 全局应用实例
app_instance = None


def create_app() -> A2AApplication:
    """创建应用程序实例"""
    global app_instance
    if app_instance is None:
        app_instance = A2AApplication()
    return app_instance


def get_app() -> A2AApplication:
    """获取应用程序实例"""
    global app_instance
    if app_instance is None:
        app_instance = create_app()
    return app_instance


# FastAPI应用实例（用于uvicorn）
app = create_app().get_fastapi_app()


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    await get_app().startup()


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    await get_app().shutdown()


def main():
    """主函数"""
    settings = get_settings()
    
    # 配置日志
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    if settings.log_file:
        logger.add(
            settings.log_file,
            level=settings.log_level,
            rotation=settings.log_rotation,
            retention=settings.log_retention,
            encoding="utf-8"
        )
    
    logger.info("启动A2A多智能体协作系统")
    logger.info(f"Python版本要求: 3.12+")
    logger.info(f"主要LLM: 阿里千问Plus")
    logger.info(f"协议: Google A2A")
    
    # 启动服务器
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        workers=settings.workers,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
