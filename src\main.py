"""
A2A多智能体协作系统主程序
基于Google Agent2Agent协议和阿里千问Plus
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from loguru import logger
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config.settings import get_settings
from src.config.agent_config import load_agent_configs
from src.database.database import get_database
from src.workflow.engine import workflow_engine
from src.agents import (
    IntentRecognizerAgent, TaskDecomposerAgent, CodeGeneratorAgent,
    MarketResearcherAgent, ResultValidatorAgent
)
from src.api.routes import create_api_router


def setup_logging():
    """设置日志"""
    settings = get_settings()
    
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台处理器
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件处理器
    if settings.log_file:
        os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
        logger.add(
            settings.log_file,
            level=settings.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days"
        )


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="基于Google Agent2Agent协议和阿里千问Plus的多智能体协作系统",
        docs_url="/docs" if settings.enable_api_docs else None,
        redoc_url="/redoc" if settings.enable_api_docs else None
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册API路由
    api_router = create_api_router()
    app.include_router(api_router, prefix="/api/v1")
    
    # 静态文件服务
    if settings.enable_web_ui and os.path.exists("frontend/dist"):
        app.mount("/static", StaticFiles(directory="frontend/dist/static"), name="static")
        
        @app.get("/")
        async def serve_frontend():
            return FileResponse("frontend/dist/index.html")
        
        @app.get("/{path:path}")
        async def serve_frontend_routes(path: str):
            # 对于前端路由，返回index.html
            if not path.startswith("api/") and not path.startswith("docs") and not path.startswith("redoc"):
                return FileResponse("frontend/dist/index.html")
            raise HTTPException(status_code=404, detail="Not found")
    
    return app


async def initialize_system():
    """初始化系统"""
    logger.info("开始初始化A2A多智能体协作系统")
    
    try:
        # 初始化数据库
        logger.info("初始化数据库...")
        db = get_database()
        
        # 加载智能体配置
        logger.info("加载智能体配置...")
        agent_configs = load_agent_configs()
        
        # 初始化智能体
        logger.info("初始化智能体...")
        settings = get_settings()
        qwen_config = settings.get_qwen_config()
        
        if not qwen_config["api_key"]:
            logger.error("千问API密钥未设置，请设置环境变量 QWEN_API_KEY")
            return False
        
        # 创建智能体实例
        agents = {}
        
        # 意图识别智能体
        if "intent_recognizer" in agent_configs:
            agents["intent_recognizer"] = IntentRecognizerAgent(qwen_config["api_key"])
            workflow_engine.register_agent(agents["intent_recognizer"])
        
        # 任务分解智能体
        if "task_decomposer" in agent_configs:
            agents["task_decomposer"] = TaskDecomposerAgent(qwen_config["api_key"])
            workflow_engine.register_agent(agents["task_decomposer"])
        
        # 代码生成智能体
        if "code_generator" in agent_configs:
            agents["code_generator"] = CodeGeneratorAgent(qwen_config["api_key"])
            workflow_engine.register_agent(agents["code_generator"])
        
        # 市场调研智能体
        if "market_researcher" in agent_configs:
            agents["market_researcher"] = MarketResearcherAgent(qwen_config["api_key"])
            workflow_engine.register_agent(agents["market_researcher"])
        
        # 结果验证智能体
        if "result_validator" in agent_configs:
            agents["result_validator"] = ResultValidatorAgent(qwen_config["api_key"])
            workflow_engine.register_agent(agents["result_validator"])
        
        logger.info(f"成功初始化 {len(agents)} 个智能体")
        
        # 注册预定义工作流
        logger.info("注册预定义工作流...")
        from src.workflows.examples import (
            create_code_generation_workflow,
            create_market_research_workflow
        )
        
        # 注册工作流
        workflow_engine.register_workflow(create_code_generation_workflow())
        workflow_engine.register_workflow(create_market_research_workflow())
        
        logger.info("A2A多智能体协作系统初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 初始化系统
    if not await initialize_system():
        logger.error("系统初始化失败，退出程序")
        return
    
    # 创建应用
    app = create_app()
    settings = get_settings()
    
    # 启动服务器
    logger.info(f"启动服务器: http://{settings.host}:{settings.port}")
    
    config = uvicorn.Config(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        access_log=True
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭系统...")
    except Exception as e:
        logger.error(f"系统运行异常: {e}")
    finally:
        logger.info("A2A多智能体协作系统已关闭")
