"""
MCP (Model Context Protocol) 服务集成模块
实现与各种MCP服务的集成
"""

from .client import MCPClient
from .services import (
    FileOperationsService,
    WebSearchService,
    DataAnalysisService,
    GitOperationsService
)
from .manager import MCPServiceManager

__all__ = [
    "MCPClient",
    "FileOperationsService",
    "WebSearchService", 
    "DataAnalysisService",
    "GitOperationsService",
    "MCPServiceManager"
]
