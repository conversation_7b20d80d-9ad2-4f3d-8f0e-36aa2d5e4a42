"""
MCP客户端实现
基于Model Context Protocol的客户端
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from loguru import logger
import httpx
import subprocess
import websockets


class MCPClient:
    """MCP协议客户端"""
    
    def __init__(self, service_config: Dict[str, Any]):
        """初始化MCP客户端"""
        self.service_id = service_config.get("service_id")
        self.name = service_config.get("name")
        self.service_type = service_config.get("type", "stdio")
        self.config = service_config.get("config", {})
        self.capabilities = service_config.get("capabilities", [])
        self.is_connected = False
        
        # 连接相关
        self.process = None
        self.websocket = None
        self.http_client = None
        
        logger.info(f"MCP客户端初始化: {self.name} ({self.service_type})")
    
    async def connect(self):
        """连接到MCP服务"""
        try:
            if self.service_type == "stdio":
                await self._connect_stdio()
            elif self.service_type == "http":
                await self._connect_http()
            elif self.service_type == "websocket":
                await self._connect_websocket()
            else:
                raise ValueError(f"不支持的MCP服务类型: {self.service_type}")
            
            self.is_connected = True
            logger.info(f"MCP服务 {self.name} 连接成功")
            
        except Exception as e:
            logger.error(f"MCP服务 {self.name} 连接失败: {e}")
            raise
    
    async def _connect_stdio(self):
        """连接到stdio类型的MCP服务"""
        command = self.config.get("command")
        args = self.config.get("args", [])
        
        if not command:
            raise ValueError("stdio类型的MCP服务需要指定command")
        
        # 启动子进程
        self.process = await asyncio.create_subprocess_exec(
            command, *args,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        logger.debug(f"启动stdio MCP服务: {command} {' '.join(args)}")
    
    async def _connect_http(self):
        """连接到HTTP类型的MCP服务"""
        base_url = self.config.get("base_url")
        headers = self.config.get("headers", {})
        
        if not base_url:
            raise ValueError("HTTP类型的MCP服务需要指定base_url")
        
        self.http_client = httpx.AsyncClient(
            base_url=base_url,
            headers=headers,
            timeout=30.0
        )
        
        # 测试连接
        response = await self.http_client.get("/health")
        if response.status_code != 200:
            raise Exception(f"HTTP MCP服务健康检查失败: {response.status_code}")
    
    async def _connect_websocket(self):
        """连接到WebSocket类型的MCP服务"""
        url = self.config.get("url")
        
        if not url:
            raise ValueError("WebSocket类型的MCP服务需要指定url")
        
        self.websocket = await websockets.connect(url)
        logger.debug(f"连接到WebSocket MCP服务: {url}")
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        if not self.is_connected:
            await self.connect()
        
        try:
            if self.service_type == "stdio":
                return await self._call_stdio_tool(tool_name, parameters)
            elif self.service_type == "http":
                return await self._call_http_tool(tool_name, parameters)
            elif self.service_type == "websocket":
                return await self._call_websocket_tool(tool_name, parameters)
            else:
                raise ValueError(f"不支持的服务类型: {self.service_type}")
                
        except Exception as e:
            logger.error(f"MCP工具调用失败 {tool_name}: {e}")
            raise
    
    async def _call_stdio_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """通过stdio调用工具"""
        if not self.process:
            raise Exception("stdio进程未启动")
        
        # 构建MCP请求
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": parameters
            }
        }
        
        # 发送请求
        request_data = json.dumps(request) + "\n"
        self.process.stdin.write(request_data.encode())
        await self.process.stdin.drain()
        
        # 读取响应
        response_line = await self.process.stdout.readline()
        response_data = json.loads(response_line.decode().strip())
        
        if "error" in response_data:
            raise Exception(f"MCP工具错误: {response_data['error']}")
        
        return response_data.get("result", {})
    
    async def _call_http_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """通过HTTP调用工具"""
        if not self.http_client:
            raise Exception("HTTP客户端未初始化")
        
        # 发送HTTP请求
        response = await self.http_client.post(
            f"/tools/{tool_name}",
            json=parameters
        )
        
        if response.status_code != 200:
            raise Exception(f"HTTP MCP工具调用失败: {response.status_code}")
        
        return response.json()
    
    async def _call_websocket_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """通过WebSocket调用工具"""
        if not self.websocket:
            raise Exception("WebSocket连接未建立")
        
        # 构建WebSocket消息
        message = {
            "type": "tool_call",
            "tool": tool_name,
            "parameters": parameters
        }
        
        # 发送消息
        await self.websocket.send(json.dumps(message))
        
        # 接收响应
        response_data = await self.websocket.recv()
        response = json.loads(response_data)
        
        if response.get("type") == "error":
            raise Exception(f"WebSocket MCP工具错误: {response.get('message')}")
        
        return response.get("result", {})
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """列出可用工具"""
        if not self.is_connected:
            await self.connect()
        
        try:
            if self.service_type == "stdio":
                return await self._list_stdio_tools()
            elif self.service_type == "http":
                return await self._list_http_tools()
            elif self.service_type == "websocket":
                return await self._list_websocket_tools()
            else:
                return []
                
        except Exception as e:
            logger.error(f"列出MCP工具失败: {e}")
            return []
    
    async def _list_stdio_tools(self) -> List[Dict[str, Any]]:
        """通过stdio列出工具"""
        request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        request_data = json.dumps(request) + "\n"
        self.process.stdin.write(request_data.encode())
        await self.process.stdin.drain()
        
        response_line = await self.process.stdout.readline()
        response_data = json.loads(response_line.decode().strip())
        
        return response_data.get("result", {}).get("tools", [])
    
    async def _list_http_tools(self) -> List[Dict[str, Any]]:
        """通过HTTP列出工具"""
        response = await self.http_client.get("/tools")
        if response.status_code == 200:
            return response.json().get("tools", [])
        return []
    
    async def _list_websocket_tools(self) -> List[Dict[str, Any]]:
        """通过WebSocket列出工具"""
        message = {"type": "list_tools"}
        await self.websocket.send(json.dumps(message))
        
        response_data = await self.websocket.recv()
        response = json.loads(response_data)
        
        return response.get("tools", [])
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.process:
                self.process.terminate()
                await self.process.wait()
                self.process = None
            
            if self.http_client:
                await self.http_client.aclose()
                self.http_client = None
            
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            
            self.is_connected = False
            logger.info(f"MCP服务 {self.name} 已断开连接")
            
        except Exception as e:
            logger.error(f"断开MCP服务连接失败: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.is_connected:
                return {
                    "status": "disconnected",
                    "service_id": self.service_id,
                    "name": self.name,
                    "type": self.service_type
                }
            
            # 尝试列出工具作为健康检查
            tools = await self.list_tools()
            
            return {
                "status": "healthy",
                "service_id": self.service_id,
                "name": self.name,
                "type": self.service_type,
                "tools_count": len(tools),
                "capabilities": self.capabilities
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "service_id": self.service_id,
                "name": self.name,
                "type": self.service_type,
                "error": str(e)
            }
    
    def __repr__(self):
        return f"MCPClient(name='{self.name}', type='{self.service_type}', connected={self.is_connected})"
