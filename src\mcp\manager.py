"""
MCP服务管理器
管理所有MCP服务的生命周期
"""

from typing import Dict, Any, List, Optional
from loguru import logger

from .services import (
    BaseMCPService, FileOperationsService, 
    WebSearchService, DataAnalysisService, GitOperationsService
)


class MCPServiceManager:
    """MCP服务管理器"""
    
    def __init__(self):
        """初始化MCP服务管理器"""
        self.services: Dict[str, BaseMCPService] = {}
        self.service_configs: Dict[str, Dict[str, Any]] = {}
        
        logger.info("MCP服务管理器初始化完成")
    
    def register_service(self, service_config: Dict[str, Any]):
        """注册MCP服务"""
        service_id = service_config.get("service_id")
        service_type = service_config.get("type", "generic")
        
        if not service_id:
            raise ValueError("服务配置必须包含service_id")
        
        # 根据服务类型创建服务实例
        if service_type == "file_operations":
            service = FileOperationsService(service_config)
        elif service_type == "web_search":
            service = WebSearchService(service_config)
        elif service_type == "data_analysis":
            service = DataAnalysisService(service_config)
        elif service_type == "git_operations":
            service = GitOperationsService(service_config)
        else:
            service = BaseMCPService(service_config)
        
        self.services[service_id] = service
        self.service_configs[service_id] = service_config
        
        logger.info(f"MCP服务已注册: {service_id} ({service_type})")
    
    async def initialize_all_services(self):
        """初始化所有服务"""
        for service_id, service in self.services.items():
            try:
                await service.initialize()
                logger.info(f"MCP服务 {service_id} 初始化成功")
            except Exception as e:
                logger.error(f"MCP服务 {service_id} 初始化失败: {e}")
    
    async def get_service(self, service_id: str) -> Optional[BaseMCPService]:
        """获取服务实例"""
        return self.services.get(service_id)
    
    async def call_service_tool(self, service_id: str, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用服务工具"""
        service = await self.get_service(service_id)
        if not service:
            return {
                "success": False,
                "error": f"服务不存在: {service_id}"
            }
        
        try:
            return await service.call_tool(tool_name, parameters)
        except Exception as e:
            logger.error(f"调用MCP服务工具失败 {service_id}.{tool_name}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def health_check_all(self) -> Dict[str, Any]:
        """检查所有服务健康状态"""
        health_status = {}
        
        for service_id, service in self.services.items():
            try:
                health_status[service_id] = await service.health_check()
            except Exception as e:
                health_status[service_id] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # 统计健康状态
        total_services = len(self.services)
        healthy_services = len([s for s in health_status.values() if s.get("status") == "healthy"])
        
        return {
            "total_services": total_services,
            "healthy_services": healthy_services,
            "unhealthy_services": total_services - healthy_services,
            "services": health_status
        }
    
    def list_services(self) -> List[Dict[str, Any]]:
        """列出所有服务"""
        services_info = []
        
        for service_id, config in self.service_configs.items():
            service = self.services.get(service_id)
            services_info.append({
                "service_id": service_id,
                "name": config.get("name"),
                "type": config.get("type"),
                "capabilities": config.get("capabilities", []),
                "is_active": config.get("is_active", True),
                "connected": service.client.is_connected if service else False
            })
        
        return services_info
    
    async def cleanup_all_services(self):
        """清理所有服务"""
        for service_id, service in self.services.items():
            try:
                await service.cleanup()
                logger.info(f"MCP服务 {service_id} 清理完成")
            except Exception as e:
                logger.error(f"MCP服务 {service_id} 清理失败: {e}")
    
    def get_service_by_capability(self, capability: str) -> List[str]:
        """根据能力获取服务ID列表"""
        matching_services = []
        
        for service_id, config in self.service_configs.items():
            capabilities = config.get("capabilities", [])
            if capability in capabilities:
                matching_services.append(service_id)
        
        return matching_services


# 创建默认MCP服务配置
def create_default_mcp_services() -> List[Dict[str, Any]]:
    """创建默认的MCP服务配置"""
    return [
        {
            "service_id": "file_operations",
            "name": "文件操作服务",
            "type": "file_operations",
            "config": {
                "base_path": "./workspace",
                "allowed_extensions": [".txt", ".md", ".json", ".py", ".js", ".html", ".css"]
            },
            "capabilities": ["read_file", "write_file", "list_files"],
            "is_active": True
        },
        {
            "service_id": "web_search",
            "name": "网络搜索服务",
            "type": "web_search",
            "config": {
                "engine": "google",
                "api_key": "",  # 需要配置
                "max_results": 10
            },
            "capabilities": ["web_search", "search_results"],
            "is_active": False  # 默认关闭，需要API密钥
        },
        {
            "service_id": "data_analysis",
            "name": "数据分析服务",
            "type": "data_analysis",
            "config": {},
            "capabilities": ["analyze_data", "statistical_analysis"],
            "is_active": True
        },
        {
            "service_id": "git_operations",
            "name": "Git操作服务",
            "type": "git_operations",
            "config": {
                "repo_path": "."
            },
            "capabilities": ["git_status", "git_operations"],
            "is_active": True
        }
    ]


# 全局MCP服务管理器实例
mcp_manager = MCPServiceManager()


def get_mcp_manager() -> MCPServiceManager:
    """获取MCP服务管理器实例"""
    return mcp_manager
