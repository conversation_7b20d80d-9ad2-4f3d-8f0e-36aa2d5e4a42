"""
MCP服务实现
实现各种具体的MCP服务
"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import aiofiles
import httpx
from loguru import logger

from .client import MCPClient


class BaseMCPService:
    """基础MCP服务类"""
    
    def __init__(self, service_config: Dict[str, Any]):
        """初始化MCP服务"""
        self.client = MCPClient(service_config)
        self.service_id = service_config.get("service_id")
        self.name = service_config.get("name")
    
    async def initialize(self):
        """初始化服务"""
        await self.client.connect()
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        return await self.client.call_tool(tool_name, parameters)
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return await self.client.health_check()
    
    async def cleanup(self):
        """清理资源"""
        await self.client.disconnect()


class FileOperationsService(BaseMCPService):
    """文件操作MCP服务"""
    
    def __init__(self, service_config: Dict[str, Any]):
        """初始化文件操作服务"""
        super().__init__(service_config)
        self.base_path = service_config.get("config", {}).get("base_path", ".")
        self.allowed_extensions = service_config.get("config", {}).get("allowed_extensions", [])
    
    async def read_file(self, file_path: str) -> Dict[str, Any]:
        """读取文件"""
        try:
            # 安全检查
            full_path = Path(self.base_path) / file_path
            if not self._is_safe_path(full_path):
                raise ValueError(f"不安全的文件路径: {file_path}")
            
            if not full_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检查文件扩展名
            if self.allowed_extensions and full_path.suffix not in self.allowed_extensions:
                raise ValueError(f"不允许的文件类型: {full_path.suffix}")
            
            async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            return {
                "success": True,
                "content": content,
                "file_path": str(full_path),
                "size": len(content)
            }
            
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def write_file(self, file_path: str, content: str, mode: str = "w") -> Dict[str, Any]:
        """写入文件"""
        try:
            # 安全检查
            full_path = Path(self.base_path) / file_path
            if not self._is_safe_path(full_path):
                raise ValueError(f"不安全的文件路径: {file_path}")
            
            # 检查文件扩展名
            if self.allowed_extensions and full_path.suffix not in self.allowed_extensions:
                raise ValueError(f"不允许的文件类型: {full_path.suffix}")
            
            # 确保目录存在
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(full_path, mode, encoding='utf-8') as f:
                await f.write(content)
            
            return {
                "success": True,
                "file_path": str(full_path),
                "size": len(content),
                "mode": mode
            }
            
        except Exception as e:
            logger.error(f"写入文件失败 {file_path}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def list_files(self, directory: str = ".", pattern: str = "*") -> Dict[str, Any]:
        """列出文件"""
        try:
            # 安全检查
            full_path = Path(self.base_path) / directory
            if not self._is_safe_path(full_path):
                raise ValueError(f"不安全的目录路径: {directory}")
            
            if not full_path.exists() or not full_path.is_dir():
                raise ValueError(f"目录不存在: {directory}")
            
            files = []
            for item in full_path.glob(pattern):
                if item.is_file():
                    # 检查文件扩展名
                    if not self.allowed_extensions or item.suffix in self.allowed_extensions:
                        files.append({
                            "name": item.name,
                            "path": str(item.relative_to(self.base_path)),
                            "size": item.stat().st_size,
                            "modified": item.stat().st_mtime
                        })
            
            return {
                "success": True,
                "files": files,
                "directory": directory,
                "count": len(files)
            }
            
        except Exception as e:
            logger.error(f"列出文件失败 {directory}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _is_safe_path(self, path: Path) -> bool:
        """检查路径是否安全"""
        try:
            # 解析绝对路径
            abs_path = path.resolve()
            base_abs_path = Path(self.base_path).resolve()
            
            # 检查是否在基础路径内
            return str(abs_path).startswith(str(base_abs_path))
        except Exception:
            return False


class WebSearchService(BaseMCPService):
    """网络搜索MCP服务"""
    
    def __init__(self, service_config: Dict[str, Any]):
        """初始化网络搜索服务"""
        super().__init__(service_config)
        self.api_key = service_config.get("config", {}).get("api_key")
        self.search_engine = service_config.get("config", {}).get("engine", "google")
        self.max_results = service_config.get("config", {}).get("max_results", 10)
    
    async def search(self, query: str, num_results: Optional[int] = None) -> Dict[str, Any]:
        """执行网络搜索"""
        try:
            if not self.api_key:
                raise ValueError("搜索服务需要API密钥")
            
            num_results = num_results or self.max_results
            
            if self.search_engine == "google":
                return await self._google_search(query, num_results)
            elif self.search_engine == "bing":
                return await self._bing_search(query, num_results)
            else:
                raise ValueError(f"不支持的搜索引擎: {self.search_engine}")
                
        except Exception as e:
            logger.error(f"网络搜索失败 '{query}': {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _google_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """Google搜索"""
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.api_key,
            "cx": "your-search-engine-id",  # 需要配置
            "q": query,
            "num": min(num_results, 10)
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            items = data.get("items", [])
            
            results = []
            for item in items:
                results.append({
                    "title": item.get("title"),
                    "url": item.get("link"),
                    "snippet": item.get("snippet"),
                    "display_url": item.get("displayLink")
                })
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "total_results": len(results),
                "search_engine": "google"
            }
    
    async def _bing_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """Bing搜索"""
        url = "https://api.bing.microsoft.com/v7.0/search"
        headers = {
            "Ocp-Apim-Subscription-Key": self.api_key
        }
        params = {
            "q": query,
            "count": min(num_results, 50)
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            web_pages = data.get("webPages", {}).get("value", [])
            
            results = []
            for page in web_pages:
                results.append({
                    "title": page.get("name"),
                    "url": page.get("url"),
                    "snippet": page.get("snippet"),
                    "display_url": page.get("displayUrl")
                })
            
            return {
                "success": True,
                "query": query,
                "results": results,
                "total_results": len(results),
                "search_engine": "bing"
            }


class DataAnalysisService(BaseMCPService):
    """数据分析MCP服务"""
    
    def __init__(self, service_config: Dict[str, Any]):
        """初始化数据分析服务"""
        super().__init__(service_config)
    
    async def analyze_data(self, data: List[Dict[str, Any]], analysis_type: str = "basic") -> Dict[str, Any]:
        """分析数据"""
        try:
            if not data:
                return {
                    "success": False,
                    "error": "没有提供数据"
                }
            
            if analysis_type == "basic":
                return await self._basic_analysis(data)
            elif analysis_type == "statistical":
                return await self._statistical_analysis(data)
            else:
                raise ValueError(f"不支持的分析类型: {analysis_type}")
                
        except Exception as e:
            logger.error(f"数据分析失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _basic_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """基础数据分析"""
        total_records = len(data)
        
        # 统计字段
        fields = set()
        for record in data:
            fields.update(record.keys())
        
        # 字段统计
        field_stats = {}
        for field in fields:
            values = [record.get(field) for record in data if field in record]
            non_null_values = [v for v in values if v is not None]
            
            field_stats[field] = {
                "total_count": len(values),
                "non_null_count": len(non_null_values),
                "null_count": len(values) - len(non_null_values),
                "null_percentage": (len(values) - len(non_null_values)) / len(values) * 100 if values else 0
            }
            
            # 数值字段的额外统计
            numeric_values = []
            for v in non_null_values:
                try:
                    numeric_values.append(float(v))
                except (ValueError, TypeError):
                    pass
            
            if numeric_values:
                field_stats[field].update({
                    "is_numeric": True,
                    "min": min(numeric_values),
                    "max": max(numeric_values),
                    "mean": sum(numeric_values) / len(numeric_values),
                    "numeric_count": len(numeric_values)
                })
            else:
                field_stats[field]["is_numeric"] = False
        
        return {
            "success": True,
            "analysis_type": "basic",
            "total_records": total_records,
            "total_fields": len(fields),
            "fields": list(fields),
            "field_statistics": field_stats
        }
    
    async def _statistical_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """统计分析"""
        # 这里可以实现更复杂的统计分析
        # 为简化，先返回基础分析结果
        basic_result = await self._basic_analysis(data)
        basic_result["analysis_type"] = "statistical"
        return basic_result


class GitOperationsService(BaseMCPService):
    """Git操作MCP服务"""
    
    def __init__(self, service_config: Dict[str, Any]):
        """初始化Git操作服务"""
        super().__init__(service_config)
        self.repo_path = service_config.get("config", {}).get("repo_path", ".")
    
    async def git_status(self) -> Dict[str, Any]:
        """获取Git状态"""
        try:
            process = await asyncio.create_subprocess_exec(
                "git", "status", "--porcelain",
                cwd=self.repo_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise Exception(f"Git命令失败: {stderr.decode()}")
            
            status_lines = stdout.decode().strip().split('\n')
            changes = []
            
            for line in status_lines:
                if line.strip():
                    status = line[:2]
                    file_path = line[3:]
                    changes.append({
                        "status": status,
                        "file": file_path
                    })
            
            return {
                "success": True,
                "changes": changes,
                "total_changes": len(changes)
            }
            
        except Exception as e:
            logger.error(f"Git状态检查失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
