"""
工作流引擎模块
实现顺序、并发、循环、分支四种执行模式
"""

from .engine import WorkflowEngine
from .executor import (
    SequentialExecutor,
    ParallelExecutor, 
    LoopExecutor,
    BranchExecutor
)
from .models import (
    WorkflowDefinition,
    WorkflowStep,
    WorkflowExecution,
    ExecutionContext
)
from .aggregators import (
    MergeAllAggregator,
    SelectBestAggregator,
    VotingAggregator,
    WeightedAverageAggregator
)

__all__ = [
    "WorkflowEngine",
    "SequentialExecutor",
    "ParallelExecutor",
    "LoopExecutor", 
    "BranchExecutor",
    "WorkflowDefinition",
    "WorkflowStep",
    "WorkflowExecution",
    "ExecutionContext",
    "MergeAllAggregator",
    "SelectBestAggregator",
    "VotingAggregator",
    "WeightedAverageAggregator"
]
