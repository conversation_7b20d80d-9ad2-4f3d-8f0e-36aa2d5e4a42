"""
并行结果聚合器
实现不同的结果聚合策略
"""

from typing import List, Dict, Any
from abc import ABC, abstractmethod
import statistics
from loguru import logger


class BaseAggregator(ABC):
    """基础聚合器"""
    
    @abstractmethod
    def aggregate(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """聚合结果"""
        pass


class MergeAllAggregator(BaseAggregator):
    """合并所有结果聚合器"""
    
    def aggregate(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并所有结果"""
        if not results:
            return {}
        
        merged = {
            "aggregation_strategy": "merge_all",
            "total_results": len(results),
            "results": results
        }
        
        # 尝试合并相同键的值
        combined_data = {}
        for result in results:
            if isinstance(result, dict):
                for key, value in result.items():
                    if key not in combined_data:
                        combined_data[key] = []
                    combined_data[key].append(value)
        
        # 处理合并后的数据
        for key, values in combined_data.items():
            if len(values) == 1:
                merged[key] = values[0]
            else:
                merged[key] = values
        
        logger.debug(f"合并了 {len(results)} 个结果")
        return merged


class SelectBestAggregator(BaseAggregator):
    """选择最佳结果聚合器"""
    
    def aggregate(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """选择最佳结果"""
        if not results:
            return {}
        
        if len(results) == 1:
            return {
                "aggregation_strategy": "select_best",
                "selected_result": results[0],
                "selection_reason": "只有一个结果"
            }
        
        # 评分机制：根据结果的完整性和质量评分
        scored_results = []
        for i, result in enumerate(results):
            score = self._calculate_score(result)
            scored_results.append({
                "index": i,
                "result": result,
                "score": score
            })
        
        # 选择得分最高的结果
        best_result = max(scored_results, key=lambda x: x["score"])
        
        logger.debug(f"从 {len(results)} 个结果中选择了得分最高的结果 (得分: {best_result['score']})")
        
        return {
            "aggregation_strategy": "select_best",
            "selected_result": best_result["result"],
            "selection_score": best_result["score"],
            "total_candidates": len(results),
            "all_scores": [r["score"] for r in scored_results]
        }
    
    def _calculate_score(self, result: Dict[str, Any]) -> float:
        """计算结果得分"""
        score = 0.0
        
        # 基础得分：有内容就得分
        if result:
            score += 1.0
        
        # 内容丰富度得分
        if isinstance(result, dict):
            score += len(result) * 0.1
            
            # 特定字段加分
            if "content" in result and result["content"]:
                score += 2.0
            if "confidence" in result:
                try:
                    confidence = float(result["confidence"])
                    score += confidence * 2.0
                except (ValueError, TypeError):
                    pass
            if "quality_score" in result:
                try:
                    quality = float(result["quality_score"])
                    score += quality * 1.5
                except (ValueError, TypeError):
                    pass
        
        return score


class VotingAggregator(BaseAggregator):
    """投票决策聚合器"""
    
    def aggregate(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """通过投票决策聚合结果"""
        if not results:
            return {}
        
        if len(results) == 1:
            return {
                "aggregation_strategy": "voting",
                "winning_result": results[0],
                "vote_count": 1,
                "total_votes": 1
            }
        
        # 统计投票
        vote_counts = {}
        for result in results:
            # 将结果转换为可哈希的键进行投票
            result_key = self._result_to_key(result)
            if result_key not in vote_counts:
                vote_counts[result_key] = {
                    "count": 0,
                    "result": result
                }
            vote_counts[result_key]["count"] += 1
        
        # 找出得票最多的结果
        winning_entry = max(vote_counts.values(), key=lambda x: x["count"])
        
        logger.debug(f"投票结果：{len(vote_counts)} 个不同结果，获胜结果得票 {winning_entry['count']}")
        
        return {
            "aggregation_strategy": "voting",
            "winning_result": winning_entry["result"],
            "vote_count": winning_entry["count"],
            "total_votes": len(results),
            "unique_results": len(vote_counts),
            "vote_distribution": {k: v["count"] for k, v in vote_counts.items()}
        }
    
    def _result_to_key(self, result: Dict[str, Any]) -> str:
        """将结果转换为投票键"""
        if isinstance(result, dict):
            # 使用主要内容作为投票键
            if "content" in result:
                return str(result["content"])[:100]  # 截取前100个字符
            elif "intent" in result:
                return str(result["intent"])
            elif "type" in result:
                return str(result["type"])
            else:
                return str(sorted(result.items()))[:100]
        else:
            return str(result)[:100]


class WeightedAverageAggregator(BaseAggregator):
    """加权平均聚合器"""
    
    def aggregate(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算加权平均结果"""
        if not results:
            return {}
        
        if len(results) == 1:
            return {
                "aggregation_strategy": "weighted_average",
                "averaged_result": results[0],
                "weight": 1.0
            }
        
        # 提取数值字段进行加权平均
        numeric_fields = {}
        weights = []
        
        for result in results:
            if isinstance(result, dict):
                # 计算权重（基于置信度或质量分数）
                weight = self._calculate_weight(result)
                weights.append(weight)
                
                # 收集数值字段
                for key, value in result.items():
                    if self._is_numeric(value):
                        if key not in numeric_fields:
                            numeric_fields[key] = []
                        numeric_fields[key].append((float(value), weight))
        
        # 计算加权平均
        averaged_result = {
            "aggregation_strategy": "weighted_average",
            "total_results": len(results),
            "weights": weights
        }
        
        for field, values_weights in numeric_fields.items():
            if values_weights:
                weighted_sum = sum(value * weight for value, weight in values_weights)
                total_weight = sum(weight for _, weight in values_weights)
                
                if total_weight > 0:
                    averaged_result[f"{field}_average"] = weighted_sum / total_weight
                    averaged_result[f"{field}_values"] = [vw[0] for vw in values_weights]
        
        # 合并非数值字段
        text_fields = {}
        for result in results:
            if isinstance(result, dict):
                for key, value in result.items():
                    if not self._is_numeric(value) and key not in ["confidence", "quality_score"]:
                        if key not in text_fields:
                            text_fields[key] = []
                        text_fields[key].append(value)
        
        # 添加文本字段的集合
        for field, values in text_fields.items():
            unique_values = list(set(str(v) for v in values))
            if len(unique_values) == 1:
                averaged_result[field] = unique_values[0]
            else:
                averaged_result[f"{field}_options"] = unique_values
        
        logger.debug(f"计算了 {len(results)} 个结果的加权平均")
        return averaged_result
    
    def _calculate_weight(self, result: Dict[str, Any]) -> float:
        """计算结果权重"""
        weight = 1.0
        
        # 基于置信度的权重
        if "confidence" in result:
            try:
                confidence = float(result["confidence"])
                weight *= confidence
            except (ValueError, TypeError):
                pass
        
        # 基于质量分数的权重
        if "quality_score" in result:
            try:
                quality = float(result["quality_score"])
                weight *= quality
            except (ValueError, TypeError):
                pass
        
        return max(weight, 0.1)  # 最小权重为0.1
    
    def _is_numeric(self, value) -> bool:
        """检查值是否为数值"""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
