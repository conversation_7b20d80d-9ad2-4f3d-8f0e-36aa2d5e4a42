"""
工作流引擎
基于Google A2A协议的工作流编排和执行引擎
"""

import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger
from datetime import datetime

from .models import Workflow, WorkflowStep, ExecutionContext, StepExecution, ExecutionMode, StepType
from .executor import WorkflowExecutor
from ..a2a.agent import A2AAgent
from ..a2a.protocol import A2ATaskStatus


class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self):
        """初始化工作流引擎"""
        self.agents: Dict[str, A2AAgent] = {}
        self.workflows: Dict[str, Workflow] = {}
        self.active_sessions: Dict[str, ExecutionContext] = {}
        self.executor = WorkflowExecutor(self)
        
        # 统计信息
        self.statistics = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "active_sessions": 0,
            "average_execution_time": 0.0
        }
        
        logger.info("工作流引擎初始化完成")
    
    def register_agent(self, agent: A2AAgent):
        """注册智能体"""
        self.agents[agent.agent_id] = agent
        logger.info(f"注册智能体到工作流引擎: {agent.name} ({agent.agent_id})")
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.agents:
            agent = self.agents.pop(agent_id)
            logger.info(f"从工作流引擎注销智能体: {agent.name} ({agent_id})")
        else:
            logger.warning(f"尝试注销不存在的智能体: {agent_id}")
    
    def register_workflow(self, workflow: Workflow):
        """注册工作流"""
        self.workflows[workflow.workflow_id] = workflow
        logger.info(f"注册工作流: {workflow.name} ({workflow.workflow_id})")
    
    def unregister_workflow(self, workflow_id: str):
        """注销工作流"""
        if workflow_id in self.workflows:
            workflow = self.workflows.pop(workflow_id)
            logger.info(f"注销工作流: {workflow.name} ({workflow_id})")
        else:
            logger.warning(f"尝试注销不存在的工作流: {workflow_id}")
    
    async def execute_workflow(
        self, 
        workflow: Workflow, 
        input_data: Dict[str, Any],
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """执行工作流"""
        try:
            # 创建执行上下文
            context = ExecutionContext(
                workflow_id=workflow.workflow_id,
                user_id=user_id,
                input_data=input_data
            )
            
            if session_id:
                context.session_id = session_id
            
            # 初始化变量
            for key, value in input_data.items():
                context.set_variable(key, value)
            
            # 记录会话
            self.active_sessions[context.session_id] = context
            self.statistics["active_sessions"] = len(self.active_sessions)
            
            logger.info(f"开始执行工作流: {workflow.name} (会话: {context.session_id})")
            
            # 更新统计
            self.statistics["total_executions"] += 1
            start_time = datetime.utcnow()
            context.started_at = start_time
            
            try:
                # 执行工作流
                result = await self.executor.execute_workflow(workflow, context)
                
                # 更新成功统计
                self.statistics["successful_executions"] += 1
                context.output_data = result
                context.completed_at = datetime.utcnow()
                
                # 更新平均执行时间
                execution_time = (context.completed_at - start_time).total_seconds()
                self._update_average_execution_time(execution_time)
                
                logger.info(f"工作流执行成功: {workflow.name} (会话: {context.session_id})")
                return result
                
            except Exception as e:
                # 更新失败统计
                self.statistics["failed_executions"] += 1
                context.add_error(f"工作流执行失败: {str(e)}")
                context.completed_at = datetime.utcnow()
                
                logger.error(f"工作流执行失败: {workflow.name} (会话: {context.session_id}) - {e}")
                raise
                
        except Exception as e:
            logger.error(f"工作流执行异常: {e}")
            raise
        
        finally:
            # 清理会话（可选，也可以保留用于查询）
            # self.active_sessions.pop(context.session_id, None)
            # self.statistics["active_sessions"] = len(self.active_sessions)
            pass
    
    async def execute_agent_task(
        self, 
        agent_id: str, 
        skill_name: str, 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行智能体任务"""
        try:
            if agent_id not in self.agents:
                raise ValueError(f"智能体 {agent_id} 未找到")
            
            agent = self.agents[agent_id]
            logger.debug(f"执行智能体任务: {agent_id}.{skill_name}")
            
            # 执行技能
            result = await agent.execute_skill(skill_name, parameters)
            
            logger.debug(f"智能体任务执行完成: {agent_id}.{skill_name}")
            return result
            
        except Exception as e:
            logger.error(f"智能体任务执行失败: {agent_id}.{skill_name} - {e}")
            raise
    
    def get_session(self, session_id: str) -> Optional[ExecutionContext]:
        """获取会话"""
        return self.active_sessions.get(session_id)
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        context = self.get_session(session_id)
        if not context:
            return {"error": "会话未找到"}
        
        return {
            "session_id": context.session_id,
            "workflow_id": context.workflow_id,
            "user_id": context.user_id,
            "current_step": context.current_step,
            "current_iteration": context.current_iteration,
            "variables": context.variables,
            "execution_history": context.execution_history,
            "errors": context.errors,
            "warnings": context.warnings,
            "created_at": context.created_at.isoformat() if context.created_at else None,
            "started_at": context.started_at.isoformat() if context.started_at else None,
            "completed_at": context.completed_at.isoformat() if context.completed_at else None
        }
    
    def list_active_sessions(self) -> List[Dict[str, Any]]:
        """列出活跃会话"""
        sessions = []
        for session_id, context in self.active_sessions.items():
            sessions.append({
                "session_id": session_id,
                "workflow_id": context.workflow_id,
                "user_id": context.user_id,
                "current_step": context.current_step,
                "started_at": context.started_at.isoformat() if context.started_at else None,
                "error_count": len(context.errors)
            })
        return sessions
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[Workflow]:
        """列出所有工作流"""
        return list(self.workflows.values())
    
    def list_agents(self) -> List[A2AAgent]:
        """列出所有智能体"""
        return list(self.agents.values())
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        return {
            "registered_agents": len(self.agents),
            "registered_workflows": len(self.workflows),
            "active_sessions": len(self.active_sessions),
            "statistics": self.statistics,
            "agents": [
                {
                    "agent_id": agent.agent_id,
                    "name": agent.name,
                    "skills_count": len(agent.skills),
                    "statistics": agent.statistics
                }
                for agent in self.agents.values()
            ],
            "workflows": [
                {
                    "workflow_id": workflow.workflow_id,
                    "name": workflow.name,
                    "steps_count": len(workflow.steps),
                    "is_active": workflow.is_active
                }
                for workflow in self.workflows.values()
            ]
        }
    
    def _update_average_execution_time(self, execution_time: float):
        """更新平均执行时间"""
        total_executions = self.statistics["total_executions"]
        if total_executions > 0:
            current_avg = self.statistics["average_execution_time"]
            self.statistics["average_execution_time"] = (
                (current_avg * (total_executions - 1) + execution_time) / total_executions
            )
    
    async def stop_session(self, session_id: str):
        """停止会话"""
        context = self.get_session(session_id)
        if context:
            context.add_warning("会话被手动停止")
            context.completed_at = datetime.utcnow()
            logger.info(f"会话已停止: {session_id}")
        else:
            logger.warning(f"尝试停止不存在的会话: {session_id}")


# 全局工作流引擎实例
workflow_engine = WorkflowEngine()
