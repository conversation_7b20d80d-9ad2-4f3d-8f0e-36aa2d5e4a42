"""
工作流引擎核心实现
基于Google Agent2Agent协议的工作流执行引擎
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from .models import (
    WorkflowDefinition, WorkflowExecution, WorkflowStatus,
    ExecutionContext, StepExecution, StepStatus, ExecutionMode
)
from .executor import (
    SequentialExecutor, ParallelExecutor, 
    LoopExecutor, BranchExecutor
)
from ..agents.base_agent import BaseAgent
from ..a2a.protocol import A2ATaskRequest, A2ATaskResponse, A2ATaskStatus


class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self):
        """初始化工作流引擎"""
        self.agents: Dict[str, BaseAgent] = {}
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        
        # 执行器
        self.sequential_executor = SequentialExecutor(self)
        self.parallel_executor = ParallelExecutor(self)
        self.loop_executor = LoopExecutor(self)
        self.branch_executor = BranchExecutor(self)
        
        logger.info("工作流引擎初始化完成")
    
    def register_agent(self, agent: BaseAgent):
        """注册智能体"""
        self.agents[agent.agent_id] = agent
        logger.info(f"智能体 {agent.name} ({agent.agent_id}) 已注册到工作流引擎")
    
    def register_workflow(self, workflow: WorkflowDefinition):
        """注册工作流定义"""
        self.workflows[workflow.workflow_id] = workflow
        logger.info(f"工作流 {workflow.name} ({workflow.workflow_id}) 已注册")
    
    async def execute_workflow(
        self, 
        workflow_id: str, 
        input_data: Dict[str, Any],
        session_id: Optional[str] = None
    ) -> WorkflowExecution:
        """执行工作流"""
        if workflow_id not in self.workflows:
            raise ValueError(f"工作流 {workflow_id} 未找到")
        
        workflow = self.workflows[workflow_id]
        
        # 创建执行记录
        execution = WorkflowExecution(
            workflow_id=workflow_id,
            session_id=session_id,
            input_data=input_data,
            max_iterations=workflow.max_iterations
        )
        
        # 保存执行记录
        self.executions[execution.execution_id] = execution
        
        try:
            # 开始执行
            execution.status = WorkflowStatus.RUNNING
            execution.started_at = datetime.utcnow()
            
            logger.info(f"开始执行工作流 {workflow.name} (执行ID: {execution.execution_id})")
            
            # 创建执行上下文
            context = ExecutionContext(
                workflow_execution=execution,
                variables=input_data.copy()
            )
            
            # 执行工作流步骤
            await self._execute_steps(workflow, context)
            
            # 完成执行
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.utcnow()
            execution.output_data = context.variables
            
            logger.info(f"工作流 {workflow.name} 执行完成")
            
        except Exception as e:
            # 执行失败
            error_msg = f"工作流执行失败: {str(e)}"
            execution.status = WorkflowStatus.FAILED
            execution.completed_at = datetime.utcnow()
            execution.error_message = error_msg
            
            logger.error(f"工作流 {workflow.name} 执行失败: {error_msg}")
        
        return execution
    
    async def _execute_steps(self, workflow: WorkflowDefinition, context: ExecutionContext):
        """执行工作流步骤"""
        for step in workflow.steps:
            # 检查执行条件
            if step.condition and not context.evaluate_expression(step.condition):
                logger.info(f"步骤 {step.step_id} 条件不满足，跳过执行")
                continue
            
            # 更新当前步骤
            context.workflow_execution.current_step = step.step_id
            
            # 根据执行模式选择执行器
            if step.execution_mode == ExecutionMode.SEQUENTIAL:
                await self.sequential_executor.execute_step(step, context)
            elif step.execution_mode == ExecutionMode.PARALLEL:
                await self.parallel_executor.execute_step(step, context)
            elif step.execution_mode == ExecutionMode.LOOP:
                await self.loop_executor.execute_step(step, context)
            elif step.execution_mode == ExecutionMode.BRANCH:
                await self.branch_executor.execute_step(step, context)
            else:
                raise ValueError(f"不支持的执行模式: {step.execution_mode}")
    
    async def execute_agent_task(
        self, 
        agent_id: str, 
        skill_name: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行智能体任务（基于A2A协议）"""
        if agent_id not in self.agents:
            raise ValueError(f"智能体 {agent_id} 未找到")
        
        agent = self.agents[agent_id]
        
        # 创建A2A任务请求
        task_request = A2ATaskRequest(
            skill_name=skill_name,
            parameters=parameters
        )
        
        # 执行任务
        response = await agent.execute_task(task_request)
        
        # 检查执行结果
        if response.status == A2ATaskStatus.COMPLETED:
            return response.result or {}
        elif response.status == A2ATaskStatus.FAILED:
            raise Exception(f"智能体任务执行失败: {response.error}")
        else:
            raise Exception(f"智能体任务状态异常: {response.status}")
    
    def get_execution(self, execution_id: str) -> Optional[WorkflowExecution]:
        """获取执行记录"""
        return self.executions.get(execution_id)
    
    def get_execution_status(self, execution_id: str) -> Optional[WorkflowStatus]:
        """获取执行状态"""
        execution = self.get_execution(execution_id)
        return execution.status if execution else None
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """取消工作流执行"""
        execution = self.get_execution(execution_id)
        if not execution:
            return False
        
        if execution.status in [WorkflowStatus.PENDING, WorkflowStatus.RUNNING]:
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = datetime.utcnow()
            logger.info(f"工作流执行 {execution_id} 已取消")
            return True
        
        return False
    
    def list_executions(self, status: Optional[WorkflowStatus] = None) -> List[WorkflowExecution]:
        """列出执行记录"""
        executions = list(self.executions.values())
        
        if status:
            executions = [e for e in executions if e.status == status]
        
        # 按创建时间排序
        executions.sort(key=lambda e: e.created_at, reverse=True)
        
        return executions
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        total_executions = len(self.executions)
        completed = len([e for e in self.executions.values() if e.status == WorkflowStatus.COMPLETED])
        failed = len([e for e in self.executions.values() if e.status == WorkflowStatus.FAILED])
        running = len([e for e in self.executions.values() if e.status == WorkflowStatus.RUNNING])
        
        return {
            "total_workflows": len(self.workflows),
            "total_agents": len(self.agents),
            "total_executions": total_executions,
            "completed_executions": completed,
            "failed_executions": failed,
            "running_executions": running,
            "success_rate": completed / total_executions if total_executions > 0 else 0
        }


# 全局工作流引擎实例
workflow_engine = WorkflowEngine()
