"""
工作流执行器实现
实现顺序、并行、循环、分支四种执行模式
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from .models import (
    WorkflowStep, StepExecution, StepStatus, 
    ExecutionContext, AggregationStrategy
)
from .aggregators import (
    MergeAllAggregator, SelectBestAggregator,
    VotingAggregator, WeightedAverageAggregator
)


class BaseExecutor:
    """基础执行器"""
    
    def __init__(self, engine):
        """初始化执行器"""
        self.engine = engine
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行步骤（抽象方法）"""
        raise NotImplementedError
    
    def create_step_execution(self, step: WorkflowStep, context: ExecutionContext) -> StepExecution:
        """创建步骤执行记录"""
        step_execution = StepExecution(
            step_id=step.step_id,
            agent_id=step.agent_id,
            input_data=context.resolve_input_mapping(step.input_mapping)
        )
        
        # 添加到工作流执行记录
        context.workflow_execution.step_executions.append(step_execution)
        
        return step_execution
    
    def update_context_from_output(self, step: WorkflowStep, result: Dict[str, Any], context: ExecutionContext):
        """根据输出映射更新上下文"""
        for output_key, context_key in step.output_mapping.items():
            if output_key in result:
                context.set_variable(context_key, result[output_key])


class SequentialExecutor(BaseExecutor):
    """顺序执行器"""
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """顺序执行步骤"""
        logger.info(f"开始顺序执行步骤: {step.step_id}")
        
        # 创建步骤执行记录
        step_execution = self.create_step_execution(step, context)
        
        try:
            step_execution.status = StepStatus.RUNNING
            step_execution.started_at = datetime.utcnow()
            
            # 执行智能体任务
            if step.agent_id and step.agent_id in self.engine.agents:
                # 确定技能名称
                skill_name = step_execution.input_data.get("skill_name", "process_task")
                
                # 执行A2A任务
                result = await self.engine.execute_agent_task(
                    step.agent_id,
                    skill_name,
                    step_execution.input_data
                )
                
                step_execution.output_data = result
                step_execution.status = StepStatus.COMPLETED
                
                # 更新上下文
                self.update_context_from_output(step, result, context)
                
                logger.info(f"步骤 {step.step_id} 顺序执行完成")
            else:
                raise ValueError(f"智能体 {step.agent_id} 未找到")
                
        except Exception as e:
            step_execution.status = StepStatus.FAILED
            step_execution.error_message = str(e)
            logger.error(f"步骤 {step.step_id} 顺序执行失败: {e}")
            raise
        
        finally:
            step_execution.completed_at = datetime.utcnow()


class ParallelExecutor(BaseExecutor):
    """并行执行器"""
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """并行执行步骤"""
        logger.info(f"开始并行执行步骤: {step.step_id}")
        
        # 创建步骤执行记录
        step_execution = self.create_step_execution(step, context)
        
        try:
            step_execution.status = StepStatus.RUNNING
            step_execution.started_at = datetime.utcnow()
            
            # 创建并行任务
            tasks = []
            for agent_config in step.parallel_agents:
                agent_id = agent_config.get("agent", step.agent_id)
                input_mapping = agent_config.get("input_mapping", {})
                
                # 解析输入映射
                resolved_input = context.resolve_input_mapping(input_mapping)
                skill_name = resolved_input.get("skill_name", "process_task")
                
                # 创建异步任务
                task = self._execute_parallel_agent(agent_id, skill_name, resolved_input)
                tasks.append(task)
            
            # 并行执行，设置超时
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=step.parallel_timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"步骤 {step.step_id} 并行执行超时")
                results = ["timeout"] * len(tasks)
            
            # 保存并行结果
            step_execution.parallel_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    step_execution.parallel_results.append({
                        "status": "failed",
                        "error": str(result)
                    })
                else:
                    step_execution.parallel_results.append({
                        "status": "completed",
                        "result": result
                    })
            
            # 聚合结果
            aggregated_result = self._aggregate_results(
                step_execution.parallel_results,
                step.aggregation_strategy
            )
            
            step_execution.aggregated_result = aggregated_result
            step_execution.output_data = aggregated_result
            step_execution.status = StepStatus.COMPLETED
            
            # 更新上下文
            self.update_context_from_output(step, aggregated_result, context)
            
            logger.info(f"步骤 {step.step_id} 并行执行完成")
            
        except Exception as e:
            step_execution.status = StepStatus.FAILED
            step_execution.error_message = str(e)
            logger.error(f"步骤 {step.step_id} 并行执行失败: {e}")
            raise
        
        finally:
            step_execution.completed_at = datetime.utcnow()
    
    async def _execute_parallel_agent(self, agent_id: str, skill_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个并行智能体任务"""
        try:
            return await self.engine.execute_agent_task(agent_id, skill_name, parameters)
        except Exception as e:
            logger.error(f"并行智能体 {agent_id} 执行失败: {e}")
            raise
    
    def _aggregate_results(self, results: List[Dict[str, Any]], strategy: AggregationStrategy) -> Dict[str, Any]:
        """聚合并行执行结果"""
        # 过滤成功的结果
        successful_results = [
            r["result"] for r in results 
            if r.get("status") == "completed" and "result" in r
        ]
        
        if not successful_results:
            return {"error": "所有并行任务都失败了"}
        
        # 根据策略聚合
        if strategy == AggregationStrategy.MERGE_ALL:
            aggregator = MergeAllAggregator()
        elif strategy == AggregationStrategy.SELECT_BEST:
            aggregator = SelectBestAggregator()
        elif strategy == AggregationStrategy.VOTING:
            aggregator = VotingAggregator()
        elif strategy == AggregationStrategy.WEIGHTED_AVERAGE:
            aggregator = WeightedAverageAggregator()
        else:
            aggregator = MergeAllAggregator()  # 默认策略
        
        return aggregator.aggregate(successful_results)


class LoopExecutor(BaseExecutor):
    """循环执行器"""
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """循环执行步骤"""
        logger.info(f"开始循环执行步骤: {step.step_id}")
        
        # 检查循环条件
        if not step.condition:
            logger.warning(f"循环步骤 {step.step_id} 没有设置条件，跳过执行")
            return
        
        # 检查循环目标
        if not step.loop_target:
            logger.warning(f"循环步骤 {step.step_id} 没有设置循环目标，跳过执行")
            return
        
        # 应用输入覆盖
        if step.input_override:
            context.update_variables(step.input_override)
            logger.info(f"循环步骤 {step.step_id} 应用输入覆盖: {step.input_override}")
        
        # 增加迭代计数
        context.workflow_execution.current_iteration += 1
        context.set_variable("current_iteration", context.workflow_execution.current_iteration)
        
        logger.info(f"循环步骤 {step.step_id} 跳转到目标步骤: {step.loop_target}")


class BranchExecutor(BaseExecutor):
    """分支执行器"""
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """分支执行步骤"""
        logger.info(f"开始分支执行步骤: {step.step_id}")
        
        # 评估分支条件
        if not step.condition:
            logger.warning(f"分支步骤 {step.step_id} 没有设置条件，跳过执行")
            return
        
        condition_result = context.evaluate_expression(step.condition)
        logger.info(f"分支步骤 {step.step_id} 条件评估结果: {condition_result}")
        
        # 选择分支
        branch_key = str(condition_result).lower()
        if branch_key in step.branches:
            branch_steps = step.branches[branch_key]
            logger.info(f"分支步骤 {step.step_id} 选择分支: {branch_key}")
            
            # 执行分支步骤
            for branch_step_config in branch_steps:
                # 这里需要递归执行分支中的步骤
                # 为简化实现，暂时记录分支选择
                context.set_variable(f"{step.step_id}_branch", branch_key)
                context.set_variable(f"{step.step_id}_branch_steps", branch_steps)
        else:
            logger.warning(f"分支步骤 {step.step_id} 没有找到匹配的分支: {branch_key}")
