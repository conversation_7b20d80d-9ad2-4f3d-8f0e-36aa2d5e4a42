"""
工作流执行器
负责具体的工作流步骤执行逻辑
"""

import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger
from datetime import datetime, timezone

from .models import (
    Workflow, WorkflowStep, ExecutionContext, StepExecution,
    ExecutionMode, StepType
)
from ..a2a.protocol import A2ATaskStatus


class WorkflowExecutor:
    """工作流执行器"""
    
    def __init__(self, engine):
        """初始化执行器"""
        self.engine = engine
        
        # 步骤执行器映射
        self.step_executors = {
            StepType.AGENT_TASK: self._execute_agent_task_step,
            StepType.BRANCH: self._execute_branch_step,
            StepType.LOOP: self._execute_loop_step,
            StepType.END: self._execute_end_step,
            StepType.WAIT: self._execute_wait_step
        }
        
        logger.debug("工作流执行器初始化完成")
    
    async def execute_workflow(self, workflow: Workflow, context: ExecutionContext) -> Dict[str, Any]:
        """执行工作流"""
        try:
            logger.info(f"开始执行工作流: {workflow.name}")
            
            # 初始化执行上下文
            context.current_iteration = 0
            
            # 按顺序执行步骤
            for step in workflow.steps:
                try:
                    context.current_step = step.step_id
                    logger.debug(f"执行步骤: {step.step_id}")
                    
                    # 检查步骤条件
                    if step.condition and not context.evaluate_expression(step.condition):
                        logger.debug(f"步骤条件不满足，跳过: {step.step_id}")
                        context.add_execution_record(step.step_id, "skipped", None, "条件不满足")
                        continue
                    
                    # 执行步骤
                    await self._execute_step(step, context)
                    
                    # 检查是否需要结束
                    if step.step_type == StepType.END:
                        break
                        
                except Exception as e:
                    logger.error(f"步骤执行失败: {step.step_id} - {e}")
                    context.add_error(f"步骤 {step.step_id} 执行失败: {str(e)}", step.step_id)
                    
                    # 根据错误处理策略决定是否继续
                    if step.on_error == "fail":
                        raise
                    elif step.on_error == "continue":
                        continue
                    elif step.on_error == "retry" and step.retry_count > 0:
                        # 实现重试逻辑
                        await self._retry_step(step, context)
            
            # 构建输出结果
            output_data = {}
            for key, var_ref in workflow.output_schema.items():
                if isinstance(var_ref, str) and var_ref.startswith("${") and var_ref.endswith("}"):
                    var_name = var_ref[2:-1]
                    output_data[key] = context.get_variable(var_name)
                else:
                    output_data[key] = var_ref
            
            logger.info(f"工作流执行完成: {workflow.name}")
            return output_data
            
        except Exception as e:
            logger.error(f"工作流执行失败: {workflow.name} - {e}")
            raise
    
    async def _execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行单个步骤"""
        try:
            # 获取步骤执行器
            executor = self.step_executors.get(step.step_type)
            if not executor:
                raise ValueError(f"不支持的步骤类型: {step.step_type}")
            
            # 执行步骤
            await executor(step, context)
            
        except Exception as e:
            logger.error(f"步骤执行异常: {step.step_id} - {e}")
            raise
    
    async def _execute_agent_task_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行智能体任务步骤"""
        try:
            # 创建步骤执行记录
            step_execution = StepExecution(
                session_id=context.session_id,
                step_id=step.step_id,
                agent_id=step.agent_id,
                skill_name=step.skill_name,
                status=A2ATaskStatus.RUNNING,
                started_at=datetime.now(timezone.utc)
            )
            
            # 解析输入参数
            parameters = {}
            for param_name, param_ref in step.input_mapping.items():
                parameters[param_name] = context.resolve_variable_reference(param_ref)
            
            step_execution.input_data = parameters
            
            # 根据执行模式执行
            if step.execution_mode == ExecutionMode.SEQUENTIAL:
                result = await self._execute_sequential_task(step, parameters, context)
            elif step.execution_mode == ExecutionMode.PARALLEL:
                result = await self._execute_parallel_tasks(step, parameters, context)
            else:
                raise ValueError(f"不支持的执行模式: {step.execution_mode}")
            
            # 更新输出变量
            if step.output_mapping:
                for output_name, var_name in step.output_mapping.items():
                    if output_name in result:
                        context.set_variable(var_name, result[output_name])
            
            # 更新执行记录
            step_execution.status = A2ATaskStatus.COMPLETED
            step_execution.output_data = result
            step_execution.completed_at = datetime.now(timezone.utc)
            step_execution.progress = 1.0
            
            context.add_execution_record(step.step_id, "completed", result)
            logger.debug(f"智能体任务步骤执行完成: {step.step_id}")
            
        except Exception as e:
            step_execution.status = A2ATaskStatus.FAILED
            step_execution.error_message = str(e)
            step_execution.completed_at = datetime.now(timezone.utc)
            
            context.add_execution_record(step.step_id, "failed", None, str(e))
            logger.error(f"智能体任务步骤执行失败: {step.step_id} - {e}")
            raise
    
    async def _execute_sequential_task(
        self, 
        step: WorkflowStep, 
        parameters: Dict[str, Any], 
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """执行顺序任务"""
        if not step.agent_id or not step.skill_name:
            raise ValueError(f"步骤 {step.step_id} 缺少智能体ID或技能名称")
        
        # 执行智能体任务
        result = await self.engine.execute_agent_task(
            step.agent_id, 
            step.skill_name, 
            parameters
        )
        
        return result
    
    async def _execute_parallel_tasks(
        self, 
        step: WorkflowStep, 
        parameters: Dict[str, Any], 
        context: ExecutionContext
    ) -> Dict[str, Any]:
        """执行并行任务"""
        if not step.parallel_agents:
            raise ValueError(f"步骤 {step.step_id} 没有配置并行智能体")
        
        # 创建并行任务
        tasks = []
        for agent_config in step.parallel_agents:
            agent_id = agent_config.get("agent")
            if not agent_id:
                continue
            
            # 解析智能体特定的输入参数
            agent_parameters = {}
            input_mapping = agent_config.get("input_mapping", {})
            for param_name, param_ref in input_mapping.items():
                agent_parameters[param_name] = context.resolve_variable_reference(param_ref)
            
            # 合并通用参数
            agent_parameters.update(parameters)
            
            # 创建任务
            task = self.engine.execute_agent_task(
                agent_id,
                step.skill_name,  # 使用步骤的技能名称
                agent_parameters
            )
            tasks.append(task)
        
        # 执行并行任务
        try:
            if step.parallel_timeout:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=step.parallel_timeout
                )
            else:
                results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 聚合结果
            return self._aggregate_parallel_results(results, step.aggregation_strategy)
            
        except asyncio.TimeoutError:
            logger.error(f"并行任务超时: {step.step_id}")
            raise Exception(f"并行任务执行超时")
    
    def _aggregate_parallel_results(self, results: List[Any], strategy: str) -> Dict[str, Any]:
        """聚合并行结果"""
        if strategy == "merge_all":
            # 合并所有结果
            aggregated = {}
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    aggregated[f"error_{i}"] = str(result)
                elif isinstance(result, dict):
                    aggregated.update(result)
                else:
                    aggregated[f"result_{i}"] = result
            return aggregated
        
        elif strategy == "first_success":
            # 返回第一个成功的结果
            for result in results:
                if not isinstance(result, Exception):
                    return result if isinstance(result, dict) else {"result": result}
            return {"error": "所有并行任务都失败了"}
        
        elif strategy == "majority":
            # 返回多数结果（简化实现）
            success_results = [r for r in results if not isinstance(r, Exception)]
            if success_results:
                return success_results[0] if isinstance(success_results[0], dict) else {"result": success_results[0]}
            return {"error": "没有成功的结果"}
        
        else:
            # 默认策略：列表形式返回所有结果
            return {"results": results}
    
    async def _execute_branch_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行分支步骤"""
        try:
            logger.debug(f"执行分支步骤: {step.step_id}")
            
            # 评估分支条件
            if not step.condition:
                logger.warning(f"分支步骤 {step.step_id} 没有设置条件")
                return
            
            condition_result = context.evaluate_expression(step.condition)
            logger.debug(f"分支条件评估结果: {condition_result}")
            
            # 确定分支键
            branch_key = self._determine_branch_key(condition_result, step.branches)
            
            if branch_key and branch_key in step.branches:
                logger.debug(f"选择分支: {branch_key}")
                context.set_variable(f"{step.step_id}_selected_branch", branch_key)
                
                # 执行分支（这里简化处理，实际应该递归执行分支中的步骤）
                branch_config = step.branches[branch_key]
                context.set_variable(f"{step.step_id}_branch_result", branch_config)
                
            else:
                logger.warning(f"没有找到匹配的分支: {condition_result}")
                context.set_variable(f"{step.step_id}_selected_branch", None)
            
            context.add_execution_record(step.step_id, "completed", {"branch": branch_key})
            
        except Exception as e:
            logger.error(f"分支步骤执行失败: {step.step_id} - {e}")
            context.add_execution_record(step.step_id, "failed", None, str(e))
            raise
    
    def _determine_branch_key(self, condition_result: Any, branches: Dict[str, Any]) -> Optional[str]:
        """确定分支键"""
        # 尝试直接匹配
        direct_key = str(condition_result).lower()
        if direct_key in branches:
            return direct_key
        
        # 尝试布尔值匹配
        if isinstance(condition_result, bool):
            bool_key = "true" if condition_result else "false"
            if bool_key in branches:
                return bool_key
        
        # 检查默认分支
        if "default" in branches:
            return "default"
        
        return None
    
    async def _execute_loop_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行循环步骤"""
        logger.debug(f"执行循环步骤: {step.step_id}")
        
        # 简化的循环实现
        context.set_variable(f"{step.step_id}_loop_executed", True)
        context.add_execution_record(step.step_id, "completed", {"loop": "executed"})
    
    async def _execute_end_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行结束步骤"""
        logger.debug(f"执行结束步骤: {step.step_id}")
        
        # 设置输出变量
        if step.output_mapping:
            for output_name, var_name in step.output_mapping.items():
                value = context.get_variable(output_name)
                context.set_variable(var_name, value)
        
        context.add_execution_record(step.step_id, "completed", {"end": True})
    
    async def _execute_wait_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行等待步骤"""
        logger.debug(f"执行等待步骤: {step.step_id}")
        
        # 简单的等待实现
        wait_time = step.metadata.get("wait_time", 1)
        await asyncio.sleep(wait_time)
        
        context.add_execution_record(step.step_id, "completed", {"waited": wait_time})
    
    async def _retry_step(self, step: WorkflowStep, context: ExecutionContext):
        """重试步骤"""
        for retry in range(step.retry_count):
            try:
                logger.info(f"重试步骤 {step.step_id} (第 {retry + 1} 次)")
                
                if step.retry_delay > 0:
                    await asyncio.sleep(step.retry_delay)
                
                await self._execute_step(step, context)
                return  # 成功则退出重试
                
            except Exception as e:
                logger.warning(f"步骤重试失败: {step.step_id} (第 {retry + 1} 次) - {e}")
                if retry == step.retry_count - 1:  # 最后一次重试
                    raise
