"""
工作流执行器实现
实现顺序、并行、循环、分支四种执行模式
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from .models import (
    WorkflowStep, StepExecution, StepStatus, 
    ExecutionContext, AggregationStrategy
)
from .aggregators import (
    MergeAllAggregator, SelectBestAggregator,
    VotingAggregator, WeightedAverageAggregator
)


class BaseExecutor:
    """基础执行器"""
    
    def __init__(self, engine):
        """初始化执行器"""
        self.engine = engine
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """执行步骤（抽象方法）"""
        raise NotImplementedError
    
    def create_step_execution(self, step: WorkflowStep, context: ExecutionContext) -> StepExecution:
        """创建步骤执行记录"""
        step_execution = StepExecution(
            step_id=step.step_id,
            agent_id=step.agent_id,
            input_data=context.resolve_input_mapping(step.input_mapping)
        )
        
        # 添加到工作流执行记录
        context.workflow_execution.step_executions.append(step_execution)
        
        return step_execution
    
    def update_context_from_output(self, step: WorkflowStep, result: Dict[str, Any], context: ExecutionContext):
        """根据输出映射更新上下文"""
        for output_key, context_key in step.output_mapping.items():
            if output_key in result:
                context.set_variable(context_key, result[output_key])


class SequentialExecutor(BaseExecutor):
    """顺序执行器"""
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """顺序执行步骤"""
        logger.info(f"开始顺序执行步骤: {step.step_id}")
        
        # 创建步骤执行记录
        step_execution = self.create_step_execution(step, context)
        
        try:
            step_execution.status = StepStatus.RUNNING
            step_execution.started_at = datetime.utcnow()
            
            # 执行智能体任务
            if step.agent_id and step.agent_id in self.engine.agents:
                # 确定技能名称
                skill_name = step_execution.input_data.get("skill_name", "process_task")
                
                # 执行A2A任务
                result = await self.engine.execute_agent_task(
                    step.agent_id,
                    skill_name,
                    step_execution.input_data
                )
                
                step_execution.output_data = result
                step_execution.status = StepStatus.COMPLETED
                
                # 更新上下文
                self.update_context_from_output(step, result, context)
                
                logger.info(f"步骤 {step.step_id} 顺序执行完成")
            else:
                raise ValueError(f"智能体 {step.agent_id} 未找到")
                
        except Exception as e:
            step_execution.status = StepStatus.FAILED
            step_execution.error_message = str(e)
            logger.error(f"步骤 {step.step_id} 顺序执行失败: {e}")
            raise
        
        finally:
            step_execution.completed_at = datetime.utcnow()


class ParallelExecutor(BaseExecutor):
    """并行执行器"""
    
    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """并行执行步骤"""
        logger.info(f"开始并行执行步骤: {step.step_id}")
        
        # 创建步骤执行记录
        step_execution = self.create_step_execution(step, context)
        
        try:
            step_execution.status = StepStatus.RUNNING
            step_execution.started_at = datetime.utcnow()
            
            # 创建并行任务
            tasks = []
            for agent_config in step.parallel_agents:
                agent_id = agent_config.get("agent", step.agent_id)
                input_mapping = agent_config.get("input_mapping", {})
                
                # 解析输入映射
                resolved_input = context.resolve_input_mapping(input_mapping)
                skill_name = resolved_input.get("skill_name", "process_task")
                
                # 创建异步任务
                task = self._execute_parallel_agent(agent_id, skill_name, resolved_input)
                tasks.append(task)
            
            # 并行执行，设置超时
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=step.parallel_timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"步骤 {step.step_id} 并行执行超时")
                results = ["timeout"] * len(tasks)
            
            # 保存并行结果
            step_execution.parallel_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    step_execution.parallel_results.append({
                        "status": "failed",
                        "error": str(result)
                    })
                else:
                    step_execution.parallel_results.append({
                        "status": "completed",
                        "result": result
                    })
            
            # 聚合结果
            aggregated_result = self._aggregate_results(
                step_execution.parallel_results,
                step.aggregation_strategy
            )
            
            step_execution.aggregated_result = aggregated_result
            step_execution.output_data = aggregated_result
            step_execution.status = StepStatus.COMPLETED
            
            # 更新上下文
            self.update_context_from_output(step, aggregated_result, context)
            
            logger.info(f"步骤 {step.step_id} 并行执行完成")
            
        except Exception as e:
            step_execution.status = StepStatus.FAILED
            step_execution.error_message = str(e)
            logger.error(f"步骤 {step.step_id} 并行执行失败: {e}")
            raise
        
        finally:
            step_execution.completed_at = datetime.utcnow()
    
    async def _execute_parallel_agent(self, agent_id: str, skill_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个并行智能体任务"""
        try:
            return await self.engine.execute_agent_task(agent_id, skill_name, parameters)
        except Exception as e:
            logger.error(f"并行智能体 {agent_id} 执行失败: {e}")
            raise
    
    def _aggregate_results(self, results: List[Dict[str, Any]], strategy: AggregationStrategy) -> Dict[str, Any]:
        """聚合并行执行结果"""
        # 过滤成功的结果
        successful_results = [
            r["result"] for r in results 
            if r.get("status") == "completed" and "result" in r
        ]
        
        if not successful_results:
            return {"error": "所有并行任务都失败了"}
        
        # 根据策略聚合
        if strategy == AggregationStrategy.MERGE_ALL:
            aggregator = MergeAllAggregator()
        elif strategy == AggregationStrategy.SELECT_BEST:
            aggregator = SelectBestAggregator()
        elif strategy == AggregationStrategy.VOTING:
            aggregator = VotingAggregator()
        elif strategy == AggregationStrategy.WEIGHTED_AVERAGE:
            aggregator = WeightedAverageAggregator()
        else:
            aggregator = MergeAllAggregator()  # 默认策略
        
        return aggregator.aggregate(successful_results)


class LoopExecutor(BaseExecutor):
    """循环执行器 - 完整实现循环逻辑"""

    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """循环执行步骤"""
        logger.info(f"开始循环执行步骤: {step.step_id}")

        # 创建步骤执行记录
        step_execution = self.create_step_execution(step, context)

        try:
            step_execution.status = StepStatus.RUNNING
            step_execution.started_at = datetime.utcnow()

            # 检查循环条件
            if not step.condition:
                logger.warning(f"循环步骤 {step.step_id} 没有设置条件，跳过执行")
                step_execution.status = StepStatus.COMPLETED
                return

            # 初始化循环计数器
            loop_counter_key = f"{step.step_id}_loop_counter"
            if loop_counter_key not in context.variables:
                context.set_variable(loop_counter_key, 0)

            max_iterations = step.max_iterations or 100  # 默认最大100次迭代
            current_iteration = context.get_variable(loop_counter_key, 0)

            # 循环执行
            while current_iteration < max_iterations:
                # 评估循环条件
                condition_result = context.evaluate_expression(step.condition)
                logger.info(f"循环步骤 {step.step_id} 第{current_iteration + 1}次迭代，条件评估: {condition_result}")

                # 如果条件为假，退出循环
                if not condition_result:
                    logger.info(f"循环步骤 {step.step_id} 条件不满足，退出循环")
                    break

                # 应用输入覆盖
                if step.input_override:
                    for key, value in step.input_override.items():
                        resolved_value = context.resolve_variable_reference(value)
                        context.set_variable(key, resolved_value)

                # 设置当前迭代信息
                context.set_variable("current_iteration", current_iteration + 1)
                context.set_variable("loop_step_id", step.step_id)

                # 执行循环体（如果有指定的智能体任务）
                if step.agent_id and step.agent_id in self.engine.agents:
                    skill_name = step_execution.input_data.get("skill_name", "process_task")

                    # 准备循环任务参数
                    loop_parameters = step_execution.input_data.copy()
                    loop_parameters.update({
                        "iteration": current_iteration + 1,
                        "max_iterations": max_iterations,
                        "loop_variables": {k: v for k, v in context.variables.items() if k.startswith(f"{step.step_id}_")}
                    })

                    # 执行智能体任务
                    result = await self.engine.execute_agent_task(
                        step.agent_id,
                        skill_name,
                        loop_parameters
                    )

                    # 更新上下文
                    if step.output_mapping:
                        self.update_context_from_output(step, result, context)

                # 增加迭代计数
                current_iteration += 1
                context.set_variable(loop_counter_key, current_iteration)

                # 检查是否需要提前退出
                if context.get_variable(f"{step.step_id}_break_loop", False):
                    logger.info(f"循环步骤 {step.step_id} 收到退出信号，提前结束循环")
                    break

            # 检查是否达到最大迭代次数
            if current_iteration >= max_iterations:
                logger.warning(f"循环步骤 {step.step_id} 达到最大迭代次数 {max_iterations}，强制退出")

            step_execution.status = StepStatus.COMPLETED
            step_execution.output_data = {
                "total_iterations": current_iteration,
                "max_iterations": max_iterations,
                "exit_reason": "condition_false" if current_iteration < max_iterations else "max_iterations"
            }

            logger.info(f"循环步骤 {step.step_id} 执行完成，共执行 {current_iteration} 次迭代")

        except Exception as e:
            step_execution.status = StepStatus.FAILED
            step_execution.error_message = str(e)
            logger.error(f"循环步骤 {step.step_id} 执行失败: {e}")
            raise

        finally:
            step_execution.completed_at = datetime.utcnow()


class BranchExecutor(BaseExecutor):
    """分支执行器 - 完整实现分支逻辑"""

    async def execute_step(self, step: WorkflowStep, context: ExecutionContext):
        """分支执行步骤"""
        logger.info(f"开始分支执行步骤: {step.step_id}")

        # 创建步骤执行记录
        step_execution = self.create_step_execution(step, context)

        try:
            step_execution.status = StepStatus.RUNNING
            step_execution.started_at = datetime.utcnow()

            # 评估分支条件
            if not step.condition:
                logger.warning(f"分支步骤 {step.step_id} 没有设置条件，跳过执行")
                step_execution.status = StepStatus.COMPLETED
                return

            condition_result = context.evaluate_expression(step.condition)
            logger.info(f"分支步骤 {step.step_id} 条件评估结果: {condition_result}")

            # 确定分支键
            branch_key = self._determine_branch_key(condition_result, step.branches)

            if branch_key and branch_key in step.branches:
                logger.info(f"分支步骤 {step.step_id} 选择分支: {branch_key}")

                # 记录分支选择
                context.set_variable(f"{step.step_id}_selected_branch", branch_key)
                context.set_variable(f"{step.step_id}_condition_result", condition_result)

                # 执行选中的分支
                branch_config = step.branches[branch_key]
                branch_result = await self._execute_branch(branch_config, context, step)

                step_execution.output_data = {
                    "selected_branch": branch_key,
                    "condition_result": condition_result,
                    "branch_result": branch_result
                }

                # 更新上下文
                if step.output_mapping:
                    self.update_context_from_output(step, step_execution.output_data, context)

            else:
                # 没有匹配的分支，检查是否有默认分支
                if "default" in step.branches:
                    logger.info(f"分支步骤 {step.step_id} 使用默认分支")
                    branch_config = step.branches["default"]
                    branch_result = await self._execute_branch(branch_config, context, step)

                    step_execution.output_data = {
                        "selected_branch": "default",
                        "condition_result": condition_result,
                        "branch_result": branch_result
                    }
                else:
                    logger.warning(f"分支步骤 {step.step_id} 没有找到匹配的分支: {condition_result}")
                    step_execution.output_data = {
                        "selected_branch": None,
                        "condition_result": condition_result,
                        "branch_result": None
                    }

            step_execution.status = StepStatus.COMPLETED
            logger.info(f"分支步骤 {step.step_id} 执行完成")

        except Exception as e:
            step_execution.status = StepStatus.FAILED
            step_execution.error_message = str(e)
            logger.error(f"分支步骤 {step.step_id} 执行失败: {e}")
            raise

        finally:
            step_execution.completed_at = datetime.utcnow()

    def _determine_branch_key(self, condition_result: Any, branches: Dict[str, Any]) -> Optional[str]:
        """确定分支键"""
        # 尝试直接匹配
        direct_key = str(condition_result).lower()
        if direct_key in branches:
            return direct_key

        # 尝试布尔值匹配
        if isinstance(condition_result, bool):
            bool_key = "true" if condition_result else "false"
            if bool_key in branches:
                return bool_key

        # 尝试数值范围匹配
        if isinstance(condition_result, (int, float)):
            for branch_key in branches.keys():
                if self._matches_numeric_condition(condition_result, branch_key):
                    return branch_key

        return None

    def _matches_numeric_condition(self, value: float, condition: str) -> bool:
        """检查数值是否匹配条件"""
        try:
            # 支持简单的数值比较条件，如 ">5", "<=10", "==0"
            if condition.startswith(">="):
                return value >= float(condition[2:])
            elif condition.startswith("<="):
                return value <= float(condition[2:])
            elif condition.startswith(">"):
                return value > float(condition[1:])
            elif condition.startswith("<"):
                return value < float(condition[1:])
            elif condition.startswith("=="):
                return value == float(condition[2:])
            elif condition.startswith("!="):
                return value != float(condition[2:])
            else:
                return str(value) == condition
        except (ValueError, IndexError):
            return False

    async def _execute_branch(self, branch_config: Any, context: ExecutionContext, step: WorkflowStep) -> Dict[str, Any]:
        """执行分支"""
        if isinstance(branch_config, dict):
            # 分支配置是一个字典，可能包含智能体任务
            if "agent_id" in branch_config:
                agent_id = branch_config["agent_id"]
                skill_name = branch_config.get("skill_name", "process_task")
                parameters = branch_config.get("parameters", {})

                # 解析参数中的变量引用
                resolved_parameters = {}
                for key, value in parameters.items():
                    resolved_parameters[key] = context.resolve_variable_reference(value)

                # 执行智能体任务
                if agent_id in self.engine.agents:
                    result = await self.engine.execute_agent_task(agent_id, skill_name, resolved_parameters)
                    return result
                else:
                    raise ValueError(f"分支中指定的智能体 {agent_id} 未找到")
            else:
                # 直接返回配置作为结果
                return branch_config
        elif isinstance(branch_config, str):
            # 分支配置是字符串，作为消息返回
            return {"message": branch_config}
        else:
            # 其他类型，直接返回
            return {"value": branch_config}
