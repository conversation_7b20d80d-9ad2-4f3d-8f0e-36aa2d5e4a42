"""
工作流模型定义
基于Google A2A协议的工作流数据模型
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
import uuid
from datetime import datetime

from ..a2a.protocol import A2AWorkflow, A2AWorkflowStep, A2ASession, A2ATaskStatus


class ExecutionMode(str, Enum):
    """执行模式"""
    SEQUENTIAL = "sequential"  # 顺序执行
    PARALLEL = "parallel"      # 并行执行
    BRANCH = "branch"          # 分支执行
    LOOP = "loop"             # 循环执行


class StepType(str, Enum):
    """步骤类型"""
    AGENT_TASK = "agent_task"  # 智能体任务
    BRANCH = "branch"          # 分支
    LOOP = "loop"             # 循环
    END = "end"               # 结束
    WAIT = "wait"             # 等待


class WorkflowStep(BaseModel):
    """工作流步骤"""
    step_id: str
    step_type: StepType = StepType.AGENT_TASK
    agent_id: Optional[str] = None
    skill_name: Optional[str] = None
    execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    
    # 输入输出映射
    input_mapping: Dict[str, str] = Field(default_factory=dict)
    output_mapping: Dict[str, str] = Field(default_factory=dict)
    
    # 条件和分支
    condition: Optional[str] = None
    branches: Dict[str, Any] = Field(default_factory=dict)
    
    # 并行执行配置
    parallel_agents: List[Dict[str, Any]] = Field(default_factory=list)
    parallel_timeout: Optional[int] = None
    aggregation_strategy: str = "merge_all"
    
    # 循环配置
    loop_target: Optional[str] = None
    loop_condition: Optional[str] = None
    max_iterations: int = 10
    
    # 错误处理
    on_error: str = "fail"  # fail, continue, retry
    retry_count: int = 0
    retry_delay: int = 1
    
    # 超时和优先级
    timeout: Optional[int] = None
    priority: int = 0
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Workflow(BaseModel):
    """工作流定义"""
    workflow_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    version: str = "1.0.0"
    
    # 步骤定义
    steps: List[WorkflowStep] = Field(default_factory=list)
    
    # 输入输出模式
    input_schema: Dict[str, Any] = Field(default_factory=dict)
    output_schema: Dict[str, Any] = Field(default_factory=dict)
    
    # 工作流配置
    max_iterations: int = 3
    timeout: Optional[int] = None
    tags: List[str] = Field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ExecutionContext(BaseModel):
    """执行上下文"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workflow_id: str
    user_id: Optional[str] = None
    
    # 变量存储
    variables: Dict[str, Any] = Field(default_factory=dict)
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Dict[str, Any] = Field(default_factory=dict)
    
    # 执行状态
    current_step: Optional[str] = None
    current_iteration: int = 0
    execution_history: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 错误信息
    errors: List[Dict[str, Any]] = Field(default_factory=list)
    warnings: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def set_variable(self, key: str, value: Any):
        """设置变量"""
        self.variables[key] = value
    
    def get_variable(self, key: str, default: Any = None) -> Any:
        """获取变量"""
        return self.variables.get(key, default)
    
    def resolve_variable_reference(self, value: Any) -> Any:
        """解析变量引用"""
        if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
            var_name = value[2:-1]
            return self.get_variable(var_name, value)
        return value
    
    def evaluate_expression(self, expression: str) -> Any:
        """评估表达式"""
        try:
            # 简单的变量替换
            for var_name, var_value in self.variables.items():
                expression = expression.replace(f"${{{var_name}}}", str(var_value))
            
            # 安全的表达式评估（仅支持基本比较）
            if "==" in expression:
                left, right = expression.split("==", 1)
                return left.strip().strip('"\'') == right.strip().strip('"\'')
            elif "!=" in expression:
                left, right = expression.split("!=", 1)
                return left.strip().strip('"\'') != right.strip().strip('"\'')
            elif ">" in expression:
                left, right = expression.split(">", 1)
                try:
                    return float(left.strip()) > float(right.strip())
                except ValueError:
                    return left.strip() > right.strip()
            elif "<" in expression:
                left, right = expression.split("<", 1)
                try:
                    return float(left.strip()) < float(right.strip())
                except ValueError:
                    return left.strip() < right.strip()
            else:
                # 直接返回变量值或表达式
                return self.get_variable(expression, expression)
                
        except Exception:
            return False
    
    def add_execution_record(self, step_id: str, status: str, result: Any = None, error: str = None):
        """添加执行记录"""
        record = {
            "step_id": step_id,
            "status": status,
            "result": result,
            "error": error,
            "timestamp": datetime.utcnow().isoformat(),
            "iteration": self.current_iteration
        }
        self.execution_history.append(record)
    
    def add_error(self, error_message: str, step_id: str = None):
        """添加错误"""
        error = {
            "message": error_message,
            "step_id": step_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.errors.append(error)
    
    def add_warning(self, warning_message: str, step_id: str = None):
        """添加警告"""
        warning = {
            "message": warning_message,
            "step_id": step_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        self.warnings.append(warning)


class StepExecution(BaseModel):
    """步骤执行记录"""
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    step_id: str
    agent_id: Optional[str] = None
    skill_name: Optional[str] = None
    
    # 执行状态
    status: A2ATaskStatus = A2ATaskStatus.PENDING
    progress: float = 0.0
    
    # 输入输出数据
    input_data: Dict[str, Any] = Field(default_factory=dict)
    output_data: Optional[Dict[str, Any]] = None
    
    # 错误信息
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 重试信息
    retry_count: int = 0
    max_retries: int = 0
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
