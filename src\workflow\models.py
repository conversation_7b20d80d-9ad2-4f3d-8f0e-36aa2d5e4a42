"""
工作流数据模型
定义工作流相关的数据结构
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime
import uuid


class ExecutionMode(str, Enum):
    """执行模式枚举"""
    SEQUENTIAL = "sequential"  # 顺序执行
    PARALLEL = "parallel"      # 并行执行
    LOOP = "loop"             # 循环执行
    BRANCH = "branch"         # 分支执行


class AggregationStrategy(str, Enum):
    """并行结果聚合策略"""
    MERGE_ALL = "merge_all"           # 合并所有结果
    SELECT_BEST = "select_best"       # 选择最佳结果
    VOTING = "voting"                 # 投票决策
    WEIGHTED_AVERAGE = "weighted_average"  # 加权平均


class WorkflowStatus(str, Enum):
    """工作流状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(str, Enum):
    """步骤状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class WorkflowStep(BaseModel):
    """工作流步骤定义"""
    step_id: str = Field(description="步骤唯一标识")
    step_type: str = Field(default="agent", description="步骤类型")
    agent_id: Optional[str] = Field(default=None, description="执行的智能体ID")
    execution_mode: ExecutionMode = Field(default=ExecutionMode.SEQUENTIAL, description="执行模式")
    
    # 输入输出映射
    input_mapping: Dict[str, Any] = Field(default_factory=dict, description="输入映射")
    output_mapping: Dict[str, Any] = Field(default_factory=dict, description="输出映射")
    
    # 条件控制
    condition: Optional[str] = Field(default=None, description="执行条件")
    
    # 并行执行配置
    parallel_agents: List[Dict[str, Any]] = Field(default_factory=list, description="并行智能体配置")
    aggregation_strategy: AggregationStrategy = Field(
        default=AggregationStrategy.MERGE_ALL, 
        description="并行结果聚合策略"
    )
    parallel_timeout: int = Field(default=120, description="并行执行超时时间（秒）")
    
    # 分支配置
    branches: Dict[str, List[Dict[str, Any]]] = Field(default_factory=dict, description="分支配置")
    
    # 循环配置
    loop_target: Optional[str] = Field(default=None, description="循环目标步骤")
    input_override: Dict[str, Any] = Field(default_factory=dict, description="输入覆盖")
    
    # 其他配置
    timeout: int = Field(default=300, description="步骤超时时间（秒）")
    retry_count: int = Field(default=0, description="重试次数")
    
    class Config:
        use_enum_values = True


class WorkflowDefinition(BaseModel):
    """工作流定义"""
    workflow_id: str = Field(description="工作流唯一标识")
    name: str = Field(description="工作流名称")
    description: str = Field(description="工作流描述")
    version: str = Field(default="1.0.0", description="工作流版本")
    
    # 步骤定义
    steps: List[WorkflowStep] = Field(description="工作流步骤")
    
    # 全局配置
    max_iterations: int = Field(default=3, description="最大循环迭代次数")
    timeout: int = Field(default=600, description="工作流总超时时间（秒）")
    
    # 输入输出定义
    input_schema: Dict[str, Any] = Field(default_factory=dict, description="输入数据结构")
    output_schema: Dict[str, Any] = Field(default_factory=dict, description="输出数据结构")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="标签")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    class Config:
        use_enum_values = True


class StepExecution(BaseModel):
    """步骤执行记录"""
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="执行ID")
    step_id: str = Field(description="步骤ID")
    status: StepStatus = Field(default=StepStatus.PENDING, description="执行状态")
    
    # 时间记录
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    # 输入输出
    input_data: Dict[str, Any] = Field(default_factory=dict, description="输入数据")
    output_data: Dict[str, Any] = Field(default_factory=dict, description="输出数据")
    
    # 执行信息
    agent_id: Optional[str] = Field(default=None, description="执行的智能体ID")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    retry_count: int = Field(default=0, description="重试次数")
    
    # 并行执行结果
    parallel_results: List[Dict[str, Any]] = Field(default_factory=list, description="并行执行结果")
    aggregated_result: Optional[Dict[str, Any]] = Field(default=None, description="聚合后的结果")
    
    class Config:
        use_enum_values = True


class WorkflowExecution(BaseModel):
    """工作流执行记录"""
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="执行ID")
    workflow_id: str = Field(description="工作流ID")
    session_id: Optional[str] = Field(default=None, description="会话ID")
    
    # 状态信息
    status: WorkflowStatus = Field(default=WorkflowStatus.PENDING, description="执行状态")
    current_step: Optional[str] = Field(default=None, description="当前步骤")
    
    # 时间记录
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    # 输入输出
    input_data: Dict[str, Any] = Field(default_factory=dict, description="输入数据")
    output_data: Dict[str, Any] = Field(default_factory=dict, description="输出数据")
    
    # 执行记录
    step_executions: List[StepExecution] = Field(default_factory=list, description="步骤执行记录")
    
    # 循环控制
    current_iteration: int = Field(default=0, description="当前迭代次数")
    max_iterations: int = Field(default=3, description="最大迭代次数")
    
    # 错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")
    
    class Config:
        use_enum_values = True


class ExecutionContext(BaseModel):
    """执行上下文"""
    workflow_execution: WorkflowExecution = Field(description="工作流执行记录")
    variables: Dict[str, Any] = Field(default_factory=dict, description="上下文变量")
    
    def get_variable(self, key: str, default: Any = None) -> Any:
        """获取上下文变量"""
        return self.variables.get(key, default)
    
    def set_variable(self, key: str, value: Any):
        """设置上下文变量"""
        self.variables[key] = value
    
    def update_variables(self, variables: Dict[str, Any]):
        """批量更新变量"""
        self.variables.update(variables)
    
    def evaluate_expression(self, expression: str) -> Any:
        """评估表达式"""
        try:
            # 简单的变量替换
            for key, value in self.variables.items():
                expression = expression.replace(f"${{{key}}}", str(value))
            
            # 评估简单的比较表达式
            if " < " in expression:
                left, right = expression.split(" < ", 1)
                return float(left.strip()) < float(right.strip())
            elif " > " in expression:
                left, right = expression.split(" > ", 1)
                return float(left.strip()) > float(right.strip())
            elif " == " in expression:
                left, right = expression.split(" == ", 1)
                return left.strip() == right.strip()
            elif " != " in expression:
                left, right = expression.split(" != ", 1)
                return left.strip() != right.strip()
            else:
                # 尝试转换为布尔值
                if expression.lower() in ["true", "1", "yes"]:
                    return True
                elif expression.lower() in ["false", "0", "no"]:
                    return False
                else:
                    return expression
        except Exception:
            return False
    
    def resolve_input_mapping(self, mapping: Dict[str, Any]) -> Dict[str, Any]:
        """解析输入映射"""
        resolved = {}
        for key, value in mapping.items():
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                var_name = value[2:-1]
                resolved[key] = self.get_variable(var_name)
            else:
                resolved[key] = value
        return resolved
