"""
工作流示例
提供预定义的工作流模板
"""

from ..workflow.models import Workflow, WorkflowStep, ExecutionMode, StepType


def create_code_generation_workflow() -> Workflow:
    """创建代码生成工作流"""
    steps = [
        WorkflowStep(
            step_id="intent_recognition",
            step_type=StepType.AGENT_TASK,
            agent_id="intent_recognizer",
            skill_name="recognize_intent",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "user_input": "${user_request}",
                "intent_types": ["generate_code", "optimize_code", "review_code", "debug_code"]
            },
            output_mapping={
                "intent": "recognized_intent",
                "confidence": "intent_confidence",
                "entities": "extracted_entities"
            }
        ),
        WorkflowStep(
            step_id="task_decomposition",
            step_type=StepType.AGENT_TASK,
            agent_id="task_decomposer",
            skill_name="decompose_task",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "user_request": "${user_request}",
                "intent": "${recognized_intent}",
                "max_tasks": 5
            },
            output_mapping={
                "tasks": "decomposed_tasks",
                "complexity": "task_complexity"
            }
        ),
        WorkflowStep(
            step_id="code_generation",
            step_type=StepType.AGENT_TASK,
            agent_id="code_generator",
            skill_name="generate_code",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "task_description": "${user_request}",
                "language": "${programming_language}",
                "requirements": "${extracted_entities}"
            },
            output_mapping={
                "generated_code": "final_code",
                "explanation": "code_explanation"
            }
        ),
        WorkflowStep(
            step_id="result_validation",
            step_type=StepType.AGENT_TASK,
            agent_id="result_validator",
            skill_name="validate_result",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "original_requirement": "${user_request}",
                "execution_result": {
                    "generated_code": "${final_code}",
                    "explanation": "${code_explanation}"
                },
                "validation_criteria": "完整性、准确性、可运行性、最佳实践"
            },
            output_mapping={
                "validation_passed": "is_valid",
                "overall_score": "quality_score",
                "issues_found": "validation_issues"
            }
        )
    ]
    
    return Workflow(
        workflow_id="code_generation_workflow",
        name="代码生成工作流",
        description="基于用户需求生成高质量代码的完整工作流",
        steps=steps,
        input_schema={
            "user_request": {
                "type": "string",
                "description": "用户的代码生成需求",
                "required": True
            },
            "programming_language": {
                "type": "string", 
                "description": "编程语言",
                "default": "Python"
            }
        },
        output_schema={
            "final_code": {
                "type": "string",
                "description": "生成的代码"
            },
            "code_explanation": {
                "type": "string",
                "description": "代码说明"
            },
            "quality_score": {
                "type": "number",
                "description": "代码质量评分"
            }
        },
        tags=["代码生成", "编程", "自动化"],
        metadata={
            "category": "development",
            "difficulty": "medium",
            "estimated_time": "5-10分钟"
        }
    )


def create_market_research_workflow() -> Workflow:
    """创建市场调研工作流"""
    steps = [
        WorkflowStep(
            step_id="intent_recognition",
            step_type=StepType.AGENT_TASK,
            agent_id="intent_recognizer",
            skill_name="recognize_intent",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "user_input": "${research_request}",
                "intent_types": ["market_analysis", "competitor_analysis", "trend_analysis"]
            },
            output_mapping={
                "intent": "research_intent",
                "entities": "research_entities"
            }
        ),
        WorkflowStep(
            step_id="research_planning",
            step_type=StepType.AGENT_TASK,
            agent_id="task_decomposer",
            skill_name="decompose_task",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "user_request": "${research_request}",
                "intent": "${research_intent}",
                "max_tasks": 8
            },
            output_mapping={
                "tasks": "research_tasks",
                "execution_strategy": "research_strategy"
            }
        ),
        WorkflowStep(
            step_id="parallel_research",
            step_type=StepType.AGENT_TASK,
            agent_id="market_researcher",
            skill_name="conduct_market_research",
            execution_mode=ExecutionMode.PARALLEL,
            parallel_agents=[
                {
                    "agent": "market_researcher",
                    "input_mapping": {
                        "research_topic": "${research_topic}",
                        "research_scope": "${research_scope}",
                        "analysis_dimensions": ["市场规模", "增长趋势"]
                    }
                },
                {
                    "agent": "market_researcher", 
                    "input_mapping": {
                        "research_topic": "${research_topic}",
                        "research_scope": "${research_scope}",
                        "analysis_dimensions": ["竞争格局", "客户需求"]
                    }
                }
            ],
            aggregation_strategy="merge_all",
            output_mapping={
                "research_report": "market_analysis",
                "key_findings": "market_insights"
            }
        ),
        WorkflowStep(
            step_id="result_validation",
            step_type=StepType.AGENT_TASK,
            agent_id="result_validator",
            skill_name="validate_result",
            execution_mode=ExecutionMode.SEQUENTIAL,
            input_mapping={
                "original_requirement": "${research_request}",
                "execution_result": {
                    "market_analysis": "${market_analysis}",
                    "insights": "${market_insights}"
                },
                "validation_criteria": "完整性、客观性、实用性、数据支撑"
            },
            output_mapping={
                "validation_passed": "is_valid",
                "overall_score": "report_quality"
            }
        )
    ]
    
    return Workflow(
        workflow_id="market_research_workflow",
        name="市场调研工作流",
        description="全面的市场调研和分析工作流",
        steps=steps,
        input_schema={
            "research_request": {
                "type": "string",
                "description": "市场调研需求",
                "required": True
            },
            "research_topic": {
                "type": "string",
                "description": "调研主题",
                "required": True
            },
            "research_scope": {
                "type": "string",
                "description": "调研范围",
                "default": "全球市场"
            }
        },
        output_schema={
            "market_analysis": {
                "type": "string",
                "description": "市场分析报告"
            },
            "market_insights": {
                "type": "array",
                "description": "关键洞察"
            },
            "report_quality": {
                "type": "number",
                "description": "报告质量评分"
            }
        },
        tags=["市场调研", "商业分析", "数据分析"],
        metadata={
            "category": "business",
            "difficulty": "high",
            "estimated_time": "15-30分钟"
        }
    )


def create_product_analysis_workflow() -> Workflow:
    """创建产品分析工作流"""
    return Workflow(
        workflow_id="product_analysis_workflow",
        name="产品分析工作流",
        description="产品功能和市场定位分析工作流",
        steps=[],
        tags=["产品分析", "市场定位"],
        metadata={"category": "product"}
    )


def create_travel_planning_workflow() -> Workflow:
    """创建旅游规划工作流"""
    return Workflow(
        workflow_id="travel_planning_workflow",
        name="旅游规划工作流",
        description="智能旅游行程规划工作流",
        steps=[],
        tags=["旅游规划", "行程安排"],
        metadata={"category": "lifestyle"}
    )


def create_data_analysis_workflow() -> Workflow:
    """创建数据分析工作流"""
    return Workflow(
        workflow_id="data_analysis_workflow",
        name="数据分析工作流",
        description="数据处理和分析工作流",
        steps=[],
        tags=["数据分析", "统计分析"],
        metadata={"category": "analytics"}
    )
