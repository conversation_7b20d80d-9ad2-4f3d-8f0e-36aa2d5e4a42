"""
工作流示例
实现各种预定义的工作流
"""

from typing import Dict, Any
from ..workflow.models import WorkflowDefinition, WorkflowStep, ExecutionMode, AggregationStrategy


def create_code_generation_workflow() -> WorkflowDefinition:
    """创建代码生成工作流"""
    steps = [
        WorkflowStep(
            step_id="intent_recognition",
            agent_id="intent_recognizer",
            input_mapping={
                "skill_name": "recognize_intent",
                "user_input": "${user_request}",
                "intent_types": ["generate_code", "other"]
            },
            output_mapping={
                "intent": "recognized_intent",
                "confidence": "intent_confidence"
            }
        ),
        WorkflowStep(
            step_id="task_decomposition",
            agent_id="task_decomposer",
            condition="${recognized_intent} == 'generate_code'",
            input_mapping={
                "skill_name": "decompose_task",
                "intent": "${recognized_intent}",
                "user_request": "${user_request}",
                "max_tasks": 6
            },
            output_mapping={
                "tasks": "decomposed_tasks",
                "complexity": "task_complexity"
            }
        ),
        WorkflowStep(
            step_id="code_generation",
            agent_id="code_generator",
            input_mapping={
                "skill_name": "generate_code",
                "task_description": "${user_request}",
                "decomposed_tasks": "${decomposed_tasks}",
                "language": "${programming_language}",
                "requirements": "${requirements}"
            },
            output_mapping={
                "code": "generated_code",
                "explanation": "code_explanation"
            }
        ),
        WorkflowStep(
            step_id="result_validation",
            agent_id="result_validator",
            input_mapping={
                "skill_name": "validate_result",
                "original_requirement": "${user_request}",
                "execution_result": "${generated_code}",
                "validation_criteria": ["代码完整性", "语法正确性", "功能实现"]
            },
            output_mapping={
                "validation_passed": "is_valid",
                "validation_score": "quality_score",
                "issues_found": "validation_issues"
            }
        )
    ]
    
    return WorkflowDefinition(
        workflow_id="code_generation_workflow",
        name="代码生成工作流",
        description="基于用户需求生成高质量代码的完整工作流",
        steps=steps,
        input_schema={
            "user_request": {"type": "string", "required": True, "description": "用户需求描述"},
            "programming_language": {"type": "string", "default": "Python", "description": "编程语言"},
            "requirements": {"type": "array", "default": [], "description": "具体要求列表"}
        },
        output_schema={
            "generated_code": {"type": "string", "description": "生成的代码"},
            "code_explanation": {"type": "string", "description": "代码说明"},
            "quality_score": {"type": "number", "description": "质量评分"},
            "validation_issues": {"type": "array", "description": "验证发现的问题"}
        },
        tags=["代码生成", "软件开发", "自动化"],
        metadata={
            "author": "A2A系统",
            "category": "开发工具",
            "difficulty": "中等"
        }
    )


def create_market_research_workflow() -> WorkflowDefinition:
    """创建市场调研工作流"""
    steps = [
        WorkflowStep(
            step_id="intent_recognition",
            agent_id="intent_recognizer",
            input_mapping={
                "skill_name": "recognize_intent",
                "user_input": "${research_topic}",
                "intent_types": ["market_research", "other"]
            },
            output_mapping={
                "intent": "recognized_intent"
            }
        ),
        WorkflowStep(
            step_id="research_planning",
            agent_id="task_decomposer",
            condition="${recognized_intent} == 'market_research'",
            input_mapping={
                "skill_name": "decompose_task",
                "intent": "market_research",
                "user_request": "${research_topic}",
                "max_tasks": 8
            },
            output_mapping={
                "tasks": "research_tasks"
            }
        ),
        WorkflowStep(
            step_id="parallel_research",
            execution_mode=ExecutionMode.PARALLEL,
            parallel_agents=[
                {
                    "agent": "market_researcher",
                    "input_mapping": {
                        "skill_name": "research_market",
                        "research_topic": "${research_topic}",
                        "research_scope": "${research_scope}",
                        "target_market": "${target_market}"
                    }
                },
                {
                    "agent": "market_researcher", 
                    "input_mapping": {
                        "skill_name": "analyze_competition",
                        "research_topic": "${research_topic}",
                        "competitors": "${competitors}"
                    }
                }
            ],
            aggregation_strategy=AggregationStrategy.MERGE_ALL,
            parallel_timeout=180,
            output_mapping={
                "research_results": "aggregated_research"
            }
        ),
        WorkflowStep(
            step_id="report_generation",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "generate_report",
                "research_data": "${aggregated_research}",
                "report_type": "comprehensive",
                "target_audience": "${target_audience}"
            },
            output_mapping={
                "report": "final_report",
                "recommendations": "market_recommendations"
            }
        )
    ]
    
    return WorkflowDefinition(
        workflow_id="market_research_workflow",
        name="市场调研工作流",
        description="全面的市场调研和分析工作流",
        steps=steps,
        input_schema={
            "research_topic": {"type": "string", "required": True, "description": "调研主题"},
            "research_scope": {"type": "string", "default": "全球市场", "description": "调研范围"},
            "target_market": {"type": "string", "description": "目标市场"},
            "competitors": {"type": "array", "default": [], "description": "竞争对手列表"},
            "target_audience": {"type": "string", "default": "管理层", "description": "报告目标受众"}
        },
        output_schema={
            "final_report": {"type": "string", "description": "最终调研报告"},
            "market_recommendations": {"type": "array", "description": "市场建议"},
            "aggregated_research": {"type": "object", "description": "聚合的调研数据"}
        },
        tags=["市场调研", "商业分析", "竞争分析"],
        metadata={
            "author": "A2A系统",
            "category": "商业智能",
            "difficulty": "复杂"
        }
    )


def create_product_analysis_workflow() -> WorkflowDefinition:
    """创建产品分析工作流"""
    steps = [
        WorkflowStep(
            step_id="product_research",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "research_product",
                "product_name": "${product_name}",
                "product_category": "${product_category}"
            },
            output_mapping={
                "product_info": "basic_product_info"
            }
        ),
        WorkflowStep(
            step_id="competitive_analysis",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "analyze_competition",
                "product_name": "${product_name}",
                "analysis_dimensions": "${analysis_dimensions}"
            },
            output_mapping={
                "competitive_analysis": "competition_data"
            }
        ),
        WorkflowStep(
            step_id="feature_comparison",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "compare_features",
                "target_product": "${basic_product_info}",
                "competitor_products": "${competition_data}",
                "comparison_criteria": "${comparison_criteria}"
            },
            output_mapping={
                "feature_comparison": "feature_analysis"
            }
        ),
        WorkflowStep(
            step_id="final_analysis",
            agent_id="result_validator",
            input_mapping={
                "skill_name": "analyze_product",
                "product_data": "${basic_product_info}",
                "competitive_data": "${competition_data}",
                "feature_data": "${feature_analysis}"
            },
            output_mapping={
                "analysis_report": "final_analysis_report",
                "recommendations": "product_recommendations"
            }
        )
    ]
    
    return WorkflowDefinition(
        workflow_id="product_analysis_workflow",
        name="产品分析工作流",
        description="深入的产品分析和竞品对比工作流",
        steps=steps,
        input_schema={
            "product_name": {"type": "string", "required": True, "description": "产品名称"},
            "product_category": {"type": "string", "required": True, "description": "产品类别"},
            "analysis_dimensions": {"type": "array", "default": ["功能", "价格", "用户体验"], "description": "分析维度"},
            "comparison_criteria": {"type": "array", "default": ["性能", "价格", "易用性"], "description": "对比标准"}
        },
        output_schema={
            "final_analysis_report": {"type": "string", "description": "最终分析报告"},
            "product_recommendations": {"type": "array", "description": "产品建议"},
            "feature_analysis": {"type": "object", "description": "功能分析数据"}
        },
        tags=["产品分析", "竞品对比", "市场研究"],
        metadata={
            "author": "A2A系统",
            "category": "产品管理",
            "difficulty": "中等"
        }
    )


def create_travel_planning_workflow() -> WorkflowDefinition:
    """创建旅游规划工作流"""
    steps = [
        WorkflowStep(
            step_id="destination_research",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "research_destination",
                "destination": "${destination}",
                "travel_duration": "${travel_duration}",
                "travel_preferences": "${travel_preferences}"
            },
            output_mapping={
                "destination_info": "destination_data"
            }
        ),
        WorkflowStep(
            step_id="itinerary_planning",
            agent_id="task_decomposer",
            input_mapping={
                "skill_name": "plan_itinerary",
                "destination_data": "${destination_data}",
                "duration": "${travel_duration}",
                "budget": "${budget_range}",
                "preferences": "${travel_preferences}"
            },
            output_mapping={
                "itinerary": "planned_itinerary"
            }
        ),
        WorkflowStep(
            step_id="accommodation_search",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "search_accommodation",
                "destination": "${destination}",
                "check_in": "${check_in_date}",
                "check_out": "${check_out_date}",
                "budget": "${accommodation_budget}"
            },
            output_mapping={
                "accommodations": "accommodation_options"
            }
        ),
        WorkflowStep(
            step_id="final_planning",
            agent_id="result_validator",
            input_mapping={
                "skill_name": "finalize_travel_plan",
                "itinerary": "${planned_itinerary}",
                "accommodations": "${accommodation_options}",
                "budget": "${budget_range}",
                "traveler_count": "${traveler_count}"
            },
            output_mapping={
                "travel_plan": "final_travel_plan",
                "budget_breakdown": "detailed_budget"
            }
        )
    ]
    
    return WorkflowDefinition(
        workflow_id="travel_planning_workflow",
        name="旅游规划工作流",
        description="全面的旅游行程规划工作流",
        steps=steps,
        input_schema={
            "destination": {"type": "string", "required": True, "description": "目的地"},
            "travel_duration": {"type": "string", "required": True, "description": "旅行时长"},
            "budget_range": {"type": "string", "description": "预算范围"},
            "travel_preferences": {"type": "array", "default": [], "description": "旅行偏好"},
            "traveler_count": {"type": "integer", "default": 1, "description": "旅行人数"},
            "check_in_date": {"type": "string", "description": "入住日期"},
            "check_out_date": {"type": "string", "description": "退房日期"},
            "accommodation_budget": {"type": "string", "description": "住宿预算"}
        },
        output_schema={
            "final_travel_plan": {"type": "string", "description": "最终旅行计划"},
            "detailed_budget": {"type": "object", "description": "详细预算分解"},
            "accommodation_options": {"type": "array", "description": "住宿选项"}
        },
        tags=["旅游规划", "行程安排", "预算管理"],
        metadata={
            "author": "A2A系统",
            "category": "生活服务",
            "difficulty": "简单"
        }
    )


def create_data_analysis_workflow() -> WorkflowDefinition:
    """创建数据分析工作流"""
    steps = [
        WorkflowStep(
            step_id="data_validation",
            agent_id="result_validator",
            input_mapping={
                "skill_name": "validate_data",
                "data_source": "${data_source}",
                "data_format": "${data_format}"
            },
            output_mapping={
                "is_valid": "data_valid",
                "validation_report": "data_validation_report"
            }
        ),
        WorkflowStep(
            step_id="data_analysis",
            agent_id="market_researcher",
            condition="${data_valid} == true",
            input_mapping={
                "skill_name": "analyze_data",
                "data_source": "${data_source}",
                "analysis_goal": "${analysis_goal}",
                "analysis_type": "${analysis_type}"
            },
            output_mapping={
                "analysis_results": "raw_analysis"
            }
        ),
        WorkflowStep(
            step_id="insight_generation",
            agent_id="result_validator",
            input_mapping={
                "skill_name": "generate_insights",
                "analysis_data": "${raw_analysis}",
                "business_context": "${business_context}"
            },
            output_mapping={
                "insights": "business_insights",
                "recommendations": "actionable_recommendations"
            }
        ),
        WorkflowStep(
            step_id="report_creation",
            agent_id="market_researcher",
            input_mapping={
                "skill_name": "create_analysis_report",
                "analysis_results": "${raw_analysis}",
                "insights": "${business_insights}",
                "recommendations": "${actionable_recommendations}",
                "report_format": "${report_format}"
            },
            output_mapping={
                "final_report": "analysis_report"
            }
        )
    ]
    
    return WorkflowDefinition(
        workflow_id="data_analysis_workflow",
        name="数据分析工作流",
        description="专业的数据分析和洞察生成工作流",
        steps=steps,
        input_schema={
            "data_source": {"type": "string", "required": True, "description": "数据源"},
            "data_format": {"type": "string", "default": "csv", "description": "数据格式"},
            "analysis_goal": {"type": "string", "required": True, "description": "分析目标"},
            "analysis_type": {"type": "string", "default": "descriptive", "description": "分析类型"},
            "business_context": {"type": "string", "description": "业务背景"},
            "report_format": {"type": "string", "default": "comprehensive", "description": "报告格式"}
        },
        output_schema={
            "analysis_report": {"type": "string", "description": "最终分析报告"},
            "business_insights": {"type": "array", "description": "业务洞察"},
            "actionable_recommendations": {"type": "array", "description": "可执行建议"}
        },
        tags=["数据分析", "商业智能", "洞察生成"],
        metadata={
            "author": "A2A系统",
            "category": "数据科学",
            "difficulty": "复杂"
        }
    )
