#!/usr/bin/env python3
"""
A2A系统前端启动脚本
"""

import os
import sys
import subprocess
import time
import signal
import json
from pathlib import Path

def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ Node.js版本: {version}")
            
            # 检查版本是否满足要求 (>=16.0.0)
            version_num = version.replace('v', '').split('.')[0]
            if int(version_num) < 16:
                print("❌ Node.js版本过低，需要16.0.0或更高版本")
                return False
            return True
        else:
            print("❌ Node.js未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False

def check_npm_version():
    """检查npm版本"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ npm版本: {version}")
            return True
        else:
            print("❌ npm未安装")
            return False
    except FileNotFoundError:
        print("❌ npm未安装")
        return False

def install_dependencies():
    """安装前端依赖"""
    frontend_dir = Path(__file__).parent / "frontend"
    
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    os.chdir(frontend_dir)
    
    # 检查package.json是否存在
    if not (frontend_dir / "package.json").exists():
        print("❌ package.json不存在")
        return False
    
    print("📦 安装前端依赖...")
    try:
        result = subprocess.run(['npm', 'install'], check=True)
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def start_frontend_dev():
    """启动前端开发服务器"""
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    
    print("🚀 启动前端开发服务器...")
    try:
        # 使用npm run dev启动开发服务器
        process = subprocess.Popen(['npm', 'run', 'dev'])
        
        print("✓ 前端开发服务器已启动")
        print("📱 访问地址: http://localhost:5173")
        print("按 Ctrl+C 停止服务器")
        
        # 等待进程结束
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止前端服务器...")
            process.terminate()
            process.wait()
            print("✓ 前端服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动前端服务器失败: {e}")
        return False

def build_frontend():
    """构建前端生产版本"""
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    
    print("🔨 构建前端生产版本...")
    try:
        result = subprocess.run(['npm', 'run', 'build'], check=True)
        print("✓ 前端构建完成")
        print(f"📁 构建文件位于: {frontend_dir / 'dist'}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 前端构建失败: {e}")
        return False

def preview_frontend():
    """预览前端生产版本"""
    frontend_dir = Path(__file__).parent / "frontend"
    os.chdir(frontend_dir)
    
    # 检查dist目录是否存在
    if not (frontend_dir / "dist").exists():
        print("❌ 构建文件不存在，请先运行构建命令")
        return False
    
    print("👀 启动前端预览服务器...")
    try:
        process = subprocess.Popen(['npm', 'run', 'preview'])
        
        print("✓ 前端预览服务器已启动")
        print("📱 访问地址: http://localhost:4173")
        print("按 Ctrl+C 停止服务器")
        
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止预览服务器...")
            process.terminate()
            process.wait()
            print("✓ 预览服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动预览服务器失败: {e}")
        return False

def check_backend_status():
    """检查后端服务状态"""
    try:
        import requests
        response = requests.get("http://localhost:8000/api/v1/system/status", timeout=5)
        if response.status_code == 200:
            print("✓ 后端服务运行正常")
            return True
        else:
            print(f"⚠️ 后端服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
        print("💡 请确保后端服务已启动 (python main.py)")
        return False

def main():
    """主函数"""
    print("🌟 A2A多智能体协作系统 - 前端启动脚本")
    print("=" * 50)
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1]
    else:
        command = "dev"
    
    # 检查环境
    print("🔍 检查运行环境...")
    if not check_node_version() or not check_npm_version():
        print("❌ 环境检查失败，请安装Node.js和npm")
        sys.exit(1)
    
    # 检查后端服务
    print("\n🔍 检查后端服务...")
    check_backend_status()
    
    print("\n" + "=" * 50)
    
    if command == "dev":
        # 开发模式
        print("🚀 启动开发模式")
        
        # 安装依赖
        if not install_dependencies():
            sys.exit(1)
        
        # 启动开发服务器
        start_frontend_dev()
        
    elif command == "build":
        # 构建模式
        print("🔨 构建生产版本")
        
        # 安装依赖
        if not install_dependencies():
            sys.exit(1)
        
        # 构建前端
        if not build_frontend():
            sys.exit(1)
            
    elif command == "preview":
        # 预览模式
        print("👀 预览生产版本")
        
        # 预览前端
        if not preview_frontend():
            sys.exit(1)
            
    elif command == "install":
        # 仅安装依赖
        print("📦 安装依赖")
        
        if not install_dependencies():
            sys.exit(1)
            
    else:
        print(f"❌ 未知命令: {command}")
        print("\n可用命令:")
        print("  dev      - 启动开发服务器 (默认)")
        print("  build    - 构建生产版本")
        print("  preview  - 预览生产版本")
        print("  install  - 仅安装依赖")
        sys.exit(1)

if __name__ == "__main__":
    main()
