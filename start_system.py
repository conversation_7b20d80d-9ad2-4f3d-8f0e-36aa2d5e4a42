#!/usr/bin/env python3
"""
A2A多智能体协作系统完整启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import threading
import time
import signal
import json
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    return True

def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ Node.js版本: {version}")
            
            # 检查版本是否满足要求 (>=16.0.0)
            version_num = version.replace('v', '').split('.')[0]
            if int(version_num) < 16:
                print("❌ Node.js版本过低，需要16.0.0或更高版本")
                return False
            return True
        else:
            print("❌ Node.js未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False

def install_python_dependencies():
    """安装Python依赖"""
    print("📦 检查Python依赖...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        print("⚠️ requirements.txt不存在，跳过Python依赖安装")
        return True
    
    try:
        # 检查是否在虚拟环境中
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print("✓ 检测到虚拟环境")
        else:
            print("⚠️ 建议在虚拟环境中运行")
        
        # 安装依赖
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Python依赖安装完成")
            return True
        else:
            print(f"❌ Python依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Python依赖安装异常: {e}")
        return False

def install_frontend_dependencies():
    """安装前端依赖"""
    frontend_dir = Path(__file__).parent / "frontend"
    
    if not frontend_dir.exists():
        print("⚠️ frontend目录不存在，跳过前端依赖安装")
        return True
    
    if not (frontend_dir / "package.json").exists():
        print("⚠️ package.json不存在，跳过前端依赖安装")
        return True
    
    print("📦 安装前端依赖...")
    
    try:
        # 检查node_modules是否存在
        node_modules = frontend_dir / "node_modules"
        if node_modules.exists():
            print("✓ 前端依赖已存在")
            return True
        
        # 安装依赖
        result = subprocess.run(['npm', 'install'], cwd=frontend_dir, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ 前端依赖安装完成")
            return True
        else:
            print(f"❌ 前端依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 前端依赖安装异常: {e}")
        return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    try:
        # 启动后端
        backend_process = subprocess.Popen([
            sys.executable, 'src/main.py'
        ], cwd=Path(__file__).parent)
        
        print("✓ 后端服务已启动 (http://localhost:8000)")
        return backend_process
        
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    frontend_dir = Path(__file__).parent / "frontend"
    
    if not frontend_dir.exists():
        print("⚠️ frontend目录不存在，跳过前端启动")
        return None
    
    if not (frontend_dir / "package.json").exists():
        print("⚠️ package.json不存在，跳过前端启动")
        return None
    
    print("🚀 启动前端服务...")
    
    try:
        # 启动前端开发服务器
        frontend_process = subprocess.Popen([
            'npm', 'run', 'dev'
        ], cwd=frontend_dir)
        
        print("✓ 前端服务已启动 (http://localhost:5173)")
        return frontend_process
        
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return None

def wait_for_services(backend_process, frontend_process):
    """等待服务运行"""
    print("\n🌟 A2A多智能体协作系统已启动")
    print("=" * 50)
    print("📱 前端地址: http://localhost:5173")
    print("🔧 后端API: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("=" * 50)
    print("按 Ctrl+C 停止所有服务")
    
    try:
        # 等待进程结束
        while True:
            time.sleep(1)
            
            # 检查进程状态
            if backend_process and backend_process.poll() is not None:
                print("❌ 后端服务意外停止")
                break
                
            if frontend_process and frontend_process.poll() is not None:
                print("❌ 前端服务意外停止")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 正在停止所有服务...")
        
        # 停止进程
        if backend_process:
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
            print("✓ 后端服务已停止")
        
        if frontend_process:
            frontend_process.terminate()
            try:
                frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frontend_process.kill()
            print("✓ 前端服务已停止")
        
        print("✓ 所有服务已停止")

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查Node.js版本
    if not check_node_version():
        print("💡 如果只需要后端服务，可以使用: python src/main.py")
        return False
    
    return True

def setup_environment():
    """设置环境"""
    print("\n📦 设置运行环境...")
    
    # 安装Python依赖
    if not install_python_dependencies():
        return False
    
    # 安装前端依赖
    if not install_frontend_dependencies():
        return False
    
    return True

def main():
    """主函数"""
    print("🌟 A2A多智能体协作系统启动脚本")
    print("基于Google Agent2Agent协议和阿里千问Plus")
    print("=" * 50)
    
    # 解析命令行参数
    backend_only = "--backend-only" in sys.argv
    frontend_only = "--frontend-only" in sys.argv
    skip_deps = "--skip-deps" in sys.argv
    
    # 检查环境
    if not check_environment():
        if not backend_only:
            print("❌ 环境检查失败")
            sys.exit(1)
    
    # 设置环境
    if not skip_deps:
        if not setup_environment():
            print("❌ 环境设置失败")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    
    backend_process = None
    frontend_process = None
    
    try:
        if not frontend_only:
            # 启动后端
            backend_process = start_backend()
            if not backend_process:
                print("❌ 后端启动失败")
                sys.exit(1)
            
            # 等待后端启动
            time.sleep(3)
        
        if not backend_only:
            # 启动前端
            frontend_process = start_frontend()
            if not frontend_process:
                print("⚠️ 前端启动失败，但后端仍在运行")
            
            # 等待前端启动
            time.sleep(2)
        
        # 等待服务运行
        wait_for_services(backend_process, frontend_process)
        
    except Exception as e:
        print(f"❌ 启动过程中发生错误: {e}")
        
        # 清理进程
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        
        sys.exit(1)

if __name__ == "__main__":
    main()
