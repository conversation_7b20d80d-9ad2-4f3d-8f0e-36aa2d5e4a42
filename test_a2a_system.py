#!/usr/bin/env python3
"""
A2A多智能体系统测试脚本
测试基本功能和智能体通信
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any


class A2ASystemTester:
    """A2A系统测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """初始化测试器"""
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def test_system_health(self) -> bool:
        """测试系统健康状态"""
        print("🔍 测试系统健康状态...")
        
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ 系统健康状态: {health_data.get('server_status', 'unknown')}")
                print(f"   智能体数量: {health_data.get('total_agents', 0)}")
                print(f"   活动任务: {health_data.get('active_tasks', 0)}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_list_agents(self) -> bool:
        """测试列出智能体"""
        print("\n🤖 测试智能体列表...")
        
        try:
            response = await self.client.get(f"{self.base_url}/agents")
            if response.status_code == 200:
                agents_data = response.json()
                agents = agents_data.get("agents", [])
                print(f"✅ 发现 {len(agents)} 个智能体:")
                
                for agent in agents:
                    agent_card = agent.get("agent_card", {})
                    stats = agent.get("statistics", {})
                    print(f"   - {agent_card.get('name', 'Unknown')} ({agent_card.get('agent_id', 'unknown')})")
                    print(f"     技能: {len(agent_card.get('skills', []))}")
                    print(f"     状态: {'活跃' if stats.get('is_active', False) else '非活跃'}")
                
                return len(agents) > 0
            else:
                print(f"❌ 获取智能体列表失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取智能体列表异常: {e}")
            return False
    
    async def test_intent_recognition(self) -> bool:
        """测试意图识别"""
        print("\n🎯 测试意图识别...")
        
        task_request = {
            "task_id": f"test_intent_{int(time.time())}",
            "skill_name": "recognize_intent",
            "parameters": {
                "user_input": "我想开发一个电商网站，需要用Python和React",
                "intent_types": ["generate_code", "market_research", "product_analysis", "other"]
            }
        }
        
        try:
            # 创建任务
            response = await self.client.post(
                f"{self.base_url}/agents/intent_recognizer/tasks",
                json=task_request
            )
            
            if response.status_code != 200:
                print(f"❌ 创建意图识别任务失败: HTTP {response.status_code}")
                return False
            
            task_id = task_request["task_id"]
            print(f"✅ 意图识别任务已创建: {task_id}")
            
            # 等待任务完成
            for i in range(30):  # 最多等待30秒
                await asyncio.sleep(1)
                
                status_response = await self.client.get(f"{self.base_url}/tasks/{task_id}")
                if status_response.status_code == 200:
                    task_data = status_response.json()
                    status = task_data.get("status")
                    
                    if status == "completed":
                        result = task_data.get("result", {})
                        print(f"✅ 意图识别完成:")
                        print(f"   识别意图: {result.get('intent', 'unknown')}")
                        print(f"   置信度: {result.get('confidence', 0):.2f}")
                        print(f"   关键词: {result.get('keywords', [])}")
                        return True
                    elif status == "failed":
                        error = task_data.get("error", "未知错误")
                        print(f"❌ 意图识别失败: {error}")
                        return False
                    else:
                        print(f"   任务状态: {status} (等待中...)")
            
            print("❌ 意图识别任务超时")
            return False
            
        except Exception as e:
            print(f"❌ 意图识别测试异常: {e}")
            return False
    
    async def test_task_decomposition(self) -> bool:
        """测试任务分解"""
        print("\n📋 测试任务分解...")
        
        task_request = {
            "task_id": f"test_decompose_{int(time.time())}",
            "skill_name": "decompose_task",
            "parameters": {
                "intent": "generate_code",
                "user_request": "开发一个简单的博客系统，包括用户注册、文章发布、评论功能",
                "max_tasks": 6
            }
        }
        
        try:
            # 创建任务
            response = await self.client.post(
                f"{self.base_url}/agents/task_decomposer/tasks",
                json=task_request
            )
            
            if response.status_code != 200:
                print(f"❌ 创建任务分解任务失败: HTTP {response.status_code}")
                return False
            
            task_id = task_request["task_id"]
            print(f"✅ 任务分解任务已创建: {task_id}")
            
            # 等待任务完成
            for i in range(30):  # 最多等待30秒
                await asyncio.sleep(1)
                
                status_response = await self.client.get(f"{self.base_url}/tasks/{task_id}")
                if status_response.status_code == 200:
                    task_data = status_response.json()
                    status = task_data.get("status")
                    
                    if status == "completed":
                        result = task_data.get("result", {})
                        tasks = result.get("tasks", [])
                        print(f"✅ 任务分解完成:")
                        print(f"   总任务数: {result.get('total_tasks', 0)}")
                        print(f"   复杂度: {result.get('complexity', 'unknown')}")
                        print(f"   子任务:")
                        for i, task in enumerate(tasks[:3], 1):  # 只显示前3个
                            print(f"     {i}. {task.get('name', 'Unknown')}")
                        if len(tasks) > 3:
                            print(f"     ... 还有 {len(tasks) - 3} 个任务")
                        return True
                    elif status == "failed":
                        error = task_data.get("error", "未知错误")
                        print(f"❌ 任务分解失败: {error}")
                        return False
                    else:
                        print(f"   任务状态: {status} (等待中...)")
            
            print("❌ 任务分解任务超时")
            return False
            
        except Exception as e:
            print(f"❌ 任务分解测试异常: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🚀 开始A2A系统测试")
        print("=" * 50)
        
        results = {}
        
        # 测试系统健康状态
        results["health"] = await self.test_system_health()
        
        # 测试智能体列表
        results["agents"] = await self.test_list_agents()
        
        # 测试意图识别
        results["intent_recognition"] = await self.test_intent_recognition()
        
        # 测试任务分解
        results["task_decomposition"] = await self.test_task_decomposition()
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常。")
        else:
            print("⚠️  部分测试失败，请检查系统配置。")
        
        return results
    
    async def close(self):
        """关闭测试器"""
        await self.client.aclose()


async def main():
    """主函数"""
    tester = A2ASystemTester()
    
    try:
        results = await tester.run_all_tests()
        return all(results.values())
    finally:
        await tester.close()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
